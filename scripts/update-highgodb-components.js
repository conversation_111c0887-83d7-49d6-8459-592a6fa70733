/**
 * 瀚高数据库组件支持更新脚本
 * 为组件添加或补全瀚高数据库配置
 *
 * 兼容 MongoDB 3.2.6+
 * 使用方法: mongo database_name update-highgodb-components.js
 */

var HIGHGODB_CONFIG = {
  data: {
    advanced: false,
    sql: "",
    tbId: "",
    tbName: "",
    folderId: "",
    fields: {
      dimension: [],
      numericalValue: []
    },
    where: {
      enable: false,
      whereCondition: [{
        composeType: "",
        field: "",
        type: "",
        fid: "",
        compare: "",
        compareValue: []
      }],
      orderCondition: [{
        field: "",
        type: "",
        fid: "",
        orderBy: "asc",
        calculation: ""
      }]
    },
    isLimit: false,
    limitNum: 150
  }
};

function updateHighgodbComponents() {
  print("更新瀚高数据库组件配置...");

  try {
    // 添加缺失的配置
    var addResult = db.components.update(
      { "dataConfig.dataResponse.source.highgodb": { $exists: false } },
      { $set: { "dataConfig.dataResponse.source.highgodb": HIGHGODB_CONFIG } },
      { multi: true }
    );

    // 补全不完整的配置
    var completeResult = db.components.update(
      {
        "dataConfig.dataResponse.source.highgodb": { $exists: true },
        "dataConfig.dataResponse.source.highgodb.data.fields": { $exists: false }
      },
      {
        $set: {
          "dataConfig.dataResponse.source.highgodb.data.advanced": false,
          "dataConfig.dataResponse.source.highgodb.data.sql": "",
          "dataConfig.dataResponse.source.highgodb.data.tbId": "",
          "dataConfig.dataResponse.source.highgodb.data.tbName": "",
          "dataConfig.dataResponse.source.highgodb.data.folderId": "",
          "dataConfig.dataResponse.source.highgodb.data.fields": {
            dimension: [],
            numericalValue: []
          },
          "dataConfig.dataResponse.source.highgodb.data.where": HIGHGODB_CONFIG.data.where,
          "dataConfig.dataResponse.source.highgodb.data.isLimit": false,
          "dataConfig.dataResponse.source.highgodb.data.limitNum": 150
        }
      },
      { multi: true }
    );

    var added = addResult.nModified || addResult.modifiedCount || 0;
    var completed = completeResult.nModified || completeResult.modifiedCount || 0;

    print("新增配置: " + added + " 个组件");
    print("补全配置: " + completed + " 个组件");
    print("更新完成");

  } catch (error) {
    print("更新失败: " + error.toString());
  }
}

// 执行更新
updateHighgodbComponents();
