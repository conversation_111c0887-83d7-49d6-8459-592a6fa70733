/**
 * 瀚高数据库组件支持更新脚本
 *
 * 功能：
 * 1. 为没有瀚高数据库配置的组件添加完整配置
 * 2. 为已有但不完整的瀚高数据库配置补全缺失字段
 * 3. 提供详细的执行日志和统计信息
 * 4. 支持回滚操作
 *
 * 兼容性：
 * - 支持 MongoDB 3.2.6 及以上版本
 * - 使用 count() 替代 countDocuments()
 * - 使用 update({multi: true}) 替代 updateMany()
 * - 使用 insert() 替代 insertOne()
 *
 * 使用方法：
 * mongo database_name update-highgodb-components.js
 *
 * 或在mongo shell中：
 * load('update-highgodb-components.js')
 */

// 瀚高数据库完整配置模板
var HIGHGODB_CONFIG_TEMPLATE = {
  data: {
    sourceId: "",
    advanced: false,
    sql: "",
    tbId: "",
    tbName: "",
    folderId: "",
    fields: {
      dimension: [],
      numericalValue: []
    },
    where: {
      enable: false,
      whereCondition: [
        {
          composeType: "",
          field: "",
          type: "",
          fid: "",
          compare: "",
          compareValue: []
        }
      ],
      orderCondition: [
        {
          field: "",
          type: "",
          fid: "",
          orderBy: "asc",
          calculation: ""
        }
      ]
    },
    isLimit: false,
    limitNum: 150
  }
};

// 主要执行函数
function updateHighgodbComponents() {
  print("开始执行瀚高数据库组件支持更新");
  print("=====================================");
  
  var startTime = new Date();
  var stats = {
    totalComponents: 0,
    existingHighgodb: 0,
    incompleteHighgodb: 0,
    newlyAdded: 0,
    completed: 0,
    errors: 0
  };
  
  try {
    // 1. 统计当前状态
    print("步骤 1: 分析当前组件状态");
    stats.totalComponents = db.components.count({});
    stats.existingHighgodb = db.components.count({
      "dataConfig.dataResponse.source.highgodb": { $exists: true }
    });
    stats.incompleteHighgodb = db.components.count({
      "dataConfig.dataResponse.source.highgodb": { $exists: true },
      "dataConfig.dataResponse.source.highgodb.data.fields": { $exists: false }
    });
    
    print("总组件数量: " + stats.totalComponents);
    print("已有瀚高数据库配置: " + stats.existingHighgodb);
    print("不完整的瀚高数据库配置: " + stats.incompleteHighgodb);
    print("需要新增配置: " + (stats.totalComponents - stats.existingHighgodb));
    
    // 2. 为没有瀚高数据库配置的组件添加配置
    if (stats.totalComponents > stats.existingHighgodb) {
      print("\n步骤 2: 为组件添加瀚高数据库配置");
      
      var addResult = db.components.update(
        {
          "dataConfig.dataResponse.source.highgodb": { $exists: false }
        },
        {
          $set: {
            "dataConfig.dataResponse.source.highgodb": HIGHGODB_CONFIG_TEMPLATE
          }
        },
        { multi: true }
      );
      
      stats.newlyAdded = addResult.nModified || addResult.modifiedCount || 0;
      print("新增瀚高数据库配置的组件数: " + stats.newlyAdded);

      var matchedCount = addResult.nMatched || addResult.matchedCount || 0;
      var modifiedCount = addResult.nModified || addResult.modifiedCount || 0;
      if (matchedCount !== modifiedCount) {
        print("警告: 匹配数量(" + matchedCount + ")与修改数量(" + modifiedCount + ")不一致");
      }
    } else {
      print("\n步骤 2: 跳过 - 所有组件都已有瀚高数据库配置");
    }
    
    // 3. 补全不完整的瀚高数据库配置
    if (stats.incompleteHighgodb > 0) {
      print("\n步骤 3: 补全不完整的瀚高数据库配置");
      
      var completeResult = db.components.update(
        {
          "dataConfig.dataResponse.source.highgodb": { $exists: true },
          "dataConfig.dataResponse.source.highgodb.data.fields": { $exists: false }
        },
        {
          $set: {
            "dataConfig.dataResponse.source.highgodb.data.advanced": false,
            "dataConfig.dataResponse.source.highgodb.data.sql": "",
            "dataConfig.dataResponse.source.highgodb.data.tbId": "",
            "dataConfig.dataResponse.source.highgodb.data.tbName": "",
            "dataConfig.dataResponse.source.highgodb.data.folderId": "",
            "dataConfig.dataResponse.source.highgodb.data.fields": {
              dimension: [],
              numericalValue: []
            },
            "dataConfig.dataResponse.source.highgodb.data.where": {
              enable: false,
              whereCondition: [
                {
                  composeType: "",
                  field: "",
                  type: "",
                  fid: "",
                  compare: "",
                  compareValue: []
                }
              ],
              orderCondition: [
                {
                  field: "",
                  type: "",
                  fid: "",
                  orderBy: "asc",
                  calculation: ""
                }
              ]
            }
          }
        },
        { multi: true }
      );
      
      stats.completed = completeResult.nModified || completeResult.modifiedCount || 0;
      print("补全配置的组件数: " + stats.completed);

      var matchedCount = completeResult.nMatched || completeResult.matchedCount || 0;
      var modifiedCount = completeResult.nModified || completeResult.modifiedCount || 0;
      if (matchedCount !== modifiedCount) {
        print("警告: 匹配数量(" + matchedCount + ")与修改数量(" + modifiedCount + ")不一致");
      }
    } else {
      print("\n步骤 3: 跳过 - 所有瀚高数据库配置都是完整的");
    }
    
    // 4. 验证最终结果
    print("\n步骤 4: 验证更新结果");
    var finalStats = {
      totalWithHighgodb: db.components.count({
        "dataConfig.dataResponse.source.highgodb": { $exists: true }
      }),
      completeHighgodb: db.components.count({
        "dataConfig.dataResponse.source.highgodb.data.fields": { $exists: true }
      }),
      incompleteHighgodb: db.components.count({
        "dataConfig.dataResponse.source.highgodb": { $exists: true },
        "dataConfig.dataResponse.source.highgodb.data.fields": { $exists: false }
      })
    };
    
    print("最终统计:");
    print("- 拥有瀚高数据库配置的组件: " + finalStats.totalWithHighgodb);
    print("- 完整配置的组件: " + finalStats.completeHighgodb);
    print("- 不完整配置的组件: " + finalStats.incompleteHighgodb);
    
    // 5. 记录迁移日志
    var migrationLog = {
      operation: "update_highgodb_components",
      executedAt: startTime,
      completedAt: new Date(),
      duration: new Date() - startTime,
      statistics: {
        before: {
          totalComponents: stats.totalComponents,
          existingHighgodb: stats.existingHighgodb,
          incompleteHighgodb: stats.incompleteHighgodb
        },
        after: finalStats,
        changes: {
          newlyAdded: stats.newlyAdded,
          completed: stats.completed
        }
      },
      status: "success"
    };
    
    // 确保migrations集合存在
    if (!db.migrations) {
      db.createCollection("migrations");
    }
    
    db.migrations.insert(migrationLog);
    
    print("\n更新完成!");
    print("执行时间: " + migrationLog.duration + " 毫秒");
    print("迁移日志已保存到 migrations 集合");
    
    // 6. 显示示例文档
    var sampleDoc = db.components.findOne(
      { "dataConfig.dataResponse.source.highgodb.data.fields": { $exists: true } },
      { 
        id: 1,
        comName: 1,
        comType: 1,
        "dataConfig.dataResponse.source.highgodb.data": 1
      }
    );
    
    if (sampleDoc) {
      print("\n示例更新后的组件配置:");
      print("组件ID: " + sampleDoc.id);
      print("组件名称: " + (sampleDoc.comName || "未命名"));
      print("组件类型: " + (sampleDoc.comType || "未知"));
      print("瀚高数据库配置结构完整: 是");
    }
    
  } catch (error) {
    stats.errors++;
    print("错误: 更新过程中发生异常");
    print("错误信息: " + error.toString());
    
    // 记录错误日志
    db.migrations.insert({
      operation: "update_highgodb_components",
      executedAt: startTime,
      failedAt: new Date(),
      status: "failed",
      error: error.toString(),
      statistics: stats
    });
    
    throw error;
  }
}

// 回滚函数
function rollbackHighgodbComponents() {
  print("开始回滚瀚高数据库组件配置");
  print("===============================");
  
  var rollbackResult = db.components.update(
    {
      "dataConfig.dataResponse.source.highgodb": { $exists: true }
    },
    {
      $unset: {
        "dataConfig.dataResponse.source.highgodb": ""
      }
    },
    { multi: true }
  );
  
  print("回滚完成");
  print("移除配置的组件数: " + (rollbackResult.nModified || rollbackResult.modifiedCount || 0));
  
  // 记录回滚日志
  db.migrations.insert({
    operation: "rollback_highgodb_components",
    executedAt: new Date(),
    status: "completed",
    rollbackCount: rollbackResult.nModified || rollbackResult.modifiedCount || 0
  });
}

// 查看迁移历史
function showMigrationHistory() {
  print("瀚高数据库组件迁移历史");
  print("====================");
  
  var migrations = db.migrations.find({
    operation: { $regex: "highgodb" }
  }).sort({ executedAt: -1 });
  
  migrations.forEach(function(migration) {
    print("\n时间: " + migration.executedAt.toISOString());
    print("操作: " + migration.operation);
    print("状态: " + migration.status);
    
    if (migration.statistics && migration.statistics.changes) {
      print("新增: " + (migration.statistics.changes.newlyAdded || 0));
      print("补全: " + (migration.statistics.changes.completed || 0));
    }
    
    if (migration.rollbackCount) {
      print("回滚数量: " + migration.rollbackCount);
    }
    
    if (migration.error) {
      print("错误: " + migration.error);
    }
  });
}

// 执行主函数
print("瀚高数据库组件支持更新脚本");
print("可用函数:");
print("- updateHighgodbComponents(): 执行更新");
print("- rollbackHighgodbComponents(): 回滚更新");
print("- showMigrationHistory(): 查看历史");
print("");

// 默认执行更新
updateHighgodbComponents();
