const { execSync } = require('child_process');
const fs = require('fs');

let branch;
try {
  branch = execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
  if (branch === 'HEAD') {
    try {
      // 直接获取当前分支名称
      branch = execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
      
      // 如果还是HEAD（处于分离头指针状态），则尝试从环境变量获取（CI环境常用）
      if (branch === 'HEAD') {
        if (process.env.CI_COMMIT_BRANCH) {
          branch = process.env.CI_COMMIT_BRANCH;
        } else {
          // 如果实在无法获取分支名，则使用commit hash
          branch = execSync('git rev-parse --short HEAD').toString().trim();
        }
      }
    } catch (error) {
      console.error('获取分支名称失败:', error);
      // 发生错误时使用短commit hash作为后备方案
      branch = execSync('git rev-parse --short HEAD').toString().trim();
    }
  }
} catch (error) {
  console.error('Error getting git branch:', error);
  process.exit(1);
}

let commitId, commitTime;
try {
  commitId = execSync('git log -n1 --format=format:"%H"').toString().trim();
  commitTime = execSync('git log -n1 --pretty=format:"%ad" --date=iso').toString().trim().substring(0, 19);
} catch (error) {
  console.error('Error getting git commit info:', error);
  process.exit(1);
}

const info = `伏羲后台seatom-server的git信息\nbranch: ${branch}\ncommitId: ${commitId}\ntime: ${commitTime}`;
  fs.writeFileSync('./app/version.txt', info);
  console.warn('========== Save git info done. ==========');
