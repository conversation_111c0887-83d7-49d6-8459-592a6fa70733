{"name": "seatom-server", "version": "5.0.0", "description": "rest service", "private": true, "egg": {"declarations": true}, "dependencies": {"alasql": "2.1.6", "arg": "^5.0.0", "atob": "^2.1.2", "axios": "^1.4.0", "base-64": "^1.0.0", "btoa": "^1.2.1", "compressing": "^1.5.1", "connect-history-api-fallback": "^1.6.0", "crypto": "^1.0.1", "crypto-js": "^4.1.1", "csvtojson": "^2.0.10", "dmdb": "^1.0.15238", "egg": "^2.29.3", "egg-compress": "0.0.0-beta.6", "egg-cors": "^2.2.3", "egg-jwt": "^3.1.7", "egg-mongoose": "^3.3.1", "egg-redis": "^2.4.0", "egg-scripts": "^2.11.0", "egg-socket.io": "^4.1.6", "egg-swagger-doc": "^2.3.2", "egg-validate": "^2.0.2", "fs-extra": "^10.0.0", "gm-crypto": "^0.1.8", "html-to-md": "^0.5.8", "iconv-lite": "^0.6.3", "javascript-obfuscator": "^2.19.0", "jschardet": "^3.0.0", "koa-compress": "^5.0.1", "levenshtein": "^1.0.5", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.29.1", "mongodb": "^4.1.0", "mongoose-increment": "^2.0.0", "mysql": "^2.18.1", "nanoid": "^3.3.6", "node-sql-parser": "^4.10.0", "node-xlsx": "^0.21.0", "oracledb": "5.2.0", "pg": "^8.6.0", "prom-client": "^15.0.0", "sm-crypto": "^0.2.7", "stream-wormhole": "^1.1.0", "svg-sprite": "^2.0.1", "svgo": "3.0.2"}, "devDependencies": {"autod": "^3.0.1", "autod-egg": "^1.1.0", "egg-bin": "^4.15.0", "egg-ci": "^1.11.0", "egg-mock": "^3.21.0", "eslint": "^5.13.0", "eslint-config-egg": "^7.1.0"}, "engines": {"node": ">=10.0.0"}, "scripts": {"start": "EGG_SERVER_ENV=dev egg-scripts start --daemon --title=egg-server-seatom-server", "stop": "EGG_SERVER_ENV=dev egg-scripts stop --title=egg-server-seatom-server", "start-dev": "EGG_SERVER_ENV=dev egg-scripts start --daemon --title=egg-server-seatom-server-dev", "stop-dev": "EGG_SERVER_ENV=dev egg-scripts stop --title=egg-server-seatom-server-dev", "restart-dev": "sh ./shell/restart-dev.sh", "start-test": "EGG_SERVER_ENV=test egg-scripts start --daemon --title=egg-server-seatom-server-test --sticky --workers=20 --stdout=/data/seatom-server-logs/master-stdout/master-stdout-test.log --stderr=/data/seatom-server-logs/master-stderr/master-stderr-test.log --ignore-stderr", "stop-test": "EGG_SERVER_ENV=test egg-scripts stop --title=egg-server-seatom-server-test", "restart-test": "sh ./shell/restart-test.sh", "start-prev": "EGG_SERVER_ENV=prev egg-scripts start --daemon --title=egg-server-seatom-server-prev --sticky --workers=20 --stdout=/data/seatom-server-logs/master-stdout/master-stdout-prev.log --stderr=/data/seatom-server-logs/master-stderr/master-stderr-prev.log --ignore-stderr", "stop-prev": "EGG_SERVER_ENV=prev egg-scripts stop --title=egg-server-seatom-server-prev", "restart-prev": "sh ./shell/restart-prev.sh", "start-bid": "EGG_SERVER_ENV=bid egg-scripts start --daemon --title=egg-server-seatom-server-bid --sticky", "stop-bid": "EGG_SERVER_ENV=bid egg-scripts stop --title=egg-server-seatom-server-bid", "restart-bid": "sh ./shell/restart-bid.sh", "start-test2": "EGG_SERVER_ENV=test2 egg-scripts start --daemon --title=egg-server-seatom-server-test2 --sticky", "stop-test2": "EGG_SERVER_ENV=test2 egg-scripts stop --title=egg-server-seatom-server-test2", "restart-test2": "sh ./shell/restart-test2.sh", "start-prod": "EGG_SERVER_ENV=prod egg-scripts start --daemon --title=egg-server-seatom-server-prod --sticky --workers=20  --ignore-stderr", "stop-prod": "EGG_SERVER_ENV=prod egg-scripts stop --title=egg-server-seatom-server-prod", "start-win": "egg-scripts start --env=win --title=egg-server-seatom-server-win --daemon --sticky", "stop-win": "egg-scripts stop  --env=win  --title=egg-server-seatom-server-win", "restart-prod": "sh ./shell/restart-prod.sh", "dev": "EGG_SERVER_ENV=local egg-bin dev", "debug": "EGG_SERVER_ENV=local sudo egg-bin debug", "test": "npm run lint -- --fix && npm run test-local", "test-local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "ci": "npm run lint && npm run cov", "autod": "autod", "set-config": "node set-config.js", "set-config-nginx": "node set-config-nginx.js", "ugl": "javascript-obfuscator  ./app --output ./app --exclude node_modules", "oracle": "npm install ./oracledb-src-5.2.0.tgz", "git-commit": "node git.commit.js", "update-pack": "sh ./shell/update-code.sh", "build-app": "sh ./shell/build-app.sh"}, "ci": {"version": "10"}, "repository": {"type": "git", "url": ""}, "author": "linghuam", "license": "MIT"}