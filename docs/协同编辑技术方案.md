<!--
 * @Description: 这是***页面
 * @Date: 2022-11-14 11:13:03
 * @Author: chen<PERSON><PERSON>
 * @LastEditors: chenxingyu
-->

协同编辑页面级技术方案（websocket）

1、进入我的可视化页面
2、前后端连接 websocket，连接成功后，前端监听事件，后端推送一个编辑列表回到前端

```js
// 前端，在大屏列表外面监听
socketIo.on('allScreenCoeditList', (data = {}) => {
  // ...
})

// 后端传输数据给前端
serveSocketIo.emit('allScreenCoeditList', {
  "screen-editor-1": [
    {
      "userId": 49539045,
      "userName": "AAAA",
      "screenId": "1",
      // 普通大屏
      "screenType": "pc",
      "socketId": "/editor#SP4Hqk28uwoVX9tqAAAA"
    }
  ],
  "screen-editor-2": [
    {
      "userId": 9450928430983,
      "userName": "AAAB",
      "screenId": "2",
      // 移动端小屏
      "screenType": "mobile",
      "socketId": "/editor#SP4Hqk28uwoVX9tqAAAB"
    }
  ],
  "screen-editor-4298402290": [
    {
      "userId": 1668996823931,
      "userName": "AAAC",
      "screenId": "4298402290",
      // 场景大屏
      "screenType": "scene",
      "socketId": "/editor#SP4Hqk28uwoVX9tqAAAC"
    }
  ]
})
```

3、前端进入大屏前根据数据判断是否能进入，进入大屏后给服务端发送一个消息，服务端广播再到每个客户端

```js
// 前端发消息
socketIo.emit('joinScreen', {
  screenId: 1,
  screenType: 'pc', // 不同类型不同值
  userId: 1
  userName: 1
}, (res) => {
  // success为true代表成功
   if (res.success) {

   }
})

// 服务端广播回每个客户端
this.ctx.app.io.of('/editor').emit('allScreenCoeditList', {
  "screen-editor-1": [
    {
      "userId": 49539045,
      "userName": "AAAA",
      "screenId": "1",
      // 普通大屏
      "screenType": "pc",
      "socketId": "/editor#SP4Hqk28uwoVX9tqAAAA"
    }
  ],
   "screen-editor-2": [
    {
      "userId": 9450928430983,
      "userName": "AAAB",
      "screenId": "2",
      // 移动端小屏
      "screenType": "mobile",
      "socketId": "/editor#SP4Hqk28uwoVX9tqAAAB"
    }
  ],
})
```

4、前端退出大屏，退出大屏后给服务端发送一个消息，服务端广播再到每个客户端

```js
// 前端发消息
socketIo.emit('exitScreen', {
  screenId: 1,
  screenType: 'pc', // 不同类型不同值
  userId: 1
  userName: 1
}, (res) => {
  // success为true代表成功
  if (res.success) {

  }
})

// 服务端广播到每个客户端
this.ctx.app.io.of('/editor').emit('allScreenCoeditList', {
  "screen-editor-2": [
    {
      "userId": 9450928430983,
      "userName": "AAAB",
      "screenId": "2",
      // 移动端小屏
      "screenType": "mobile",
      "socketId": "/editor#SP4Hqk28uwoVX9tqAAAB"
    }
  ]
})
```

场景大屏页面锁

1、进入大屏，前端监听 screenCoeditInfo 事件，前端根据返回的数据做页面锁的控制

```js
// 前端，进入大屏里面监听
socketIo.on('screenCoeditInfo', (data = {}) => {})


// 服务端传输数据到客户端，告诉客客户端现在有没有用户在编辑
serveSocketIo.emit('screenCoeditInfo', {
  screenId: 1,
  screenType: 'scene',
  coeditUsers: [
    {
      userId: 22,
      userName: 22,
      pageId: 2
    }
  ]
})
```

2、前端进入场景大屏页面前根据数据判断是否能进入，进入场景大屏页面后给服务端发送一个消息，服务端广播到每个客户端

```js
// 前端发消息
socketIo.emit('joinScenePage', {
  screenId: 1,
  userId: 1
  userName: 1,
  pageId: 1
}, (res) => {
  // success为true代表成功
   if (res.success) {

   }
})


// 服务端广播到每个客户端
this.ctx.app.io.of('/editor').emit('screenCoeditInfo', {
  screenId: 1,
  screenType: 'scene',
  coeditUsers: [
    {
      userId: 11,
      userName: 11,
      pageId: 1
    },
    {
      userId: 22,
      userName: 22,
      pageId: 2
    }
  ]
})
```

3、前端退出场景大屏页面，给服务端发送一个消息，服务端广播再到每个客户端

```js
// 前端发消息
socketIo.emit('exitScenePage', {
  screenId: 1,
  userId: 1
  userName: 1,
  pageId: 1
}, (res) => {
   if (res.success) {

   }
})

// 服务端广播到每个客户端
this.ctx.app.io.of('/editor').emit('screenCoeditInfo', {
    screenId: 1,
    screenType: 'scene',
    coeditUsers: [
      {
        userId: 22,
        userName: 22,
        pageId: 2
      }
    ]
  })
```

细节问题：
1、连接断线，后端处理
2、保存大屏接口，后端限制
3、用户复制链接进入大屏，可以提供一个接口检查是否有人正在编辑
4、场景大屏，有人正在编辑，不能删除
