# Seatom-server Docker 构建使用说明

## 标准 Docker 镜像构建及容器启动示例

构建 docker 镜像：`sh docker/build.sh`

伏羲大屏，Docker 容器化启动示例（生产环境按需修改 minio 地址及-v 相关的目录映射）：

```
docker run -it -d \
  --name seatom-server \
  --privileged \
  --link minio \
  -e ENDPOINT=http://minio:9000 \
  -e BUCKET="fuxi" \
  -e AWS_ACCESS_KEY_ID=minioadmin \
  -e AWS_SECRET_ACCESS_KEY=minioadmin \
  -v seatom-server-config-dir:/seatom/seatom-server/config \
  -v seatom-server-log-dir:/seatom/seatom-server/log \
  -p 7001:7001 \
  -p 1115:1115 \
  docker.art.haizhi.com/dmc/seatom-server:dev
```

以上启动方式依赖 minio 服务，本机测试，可以通过如下方式启动一个临时的 minio 容器：

```
docker run --name minio -d -p 9000:9000 -p 9001:9001 minio/minio server /data --console-address ":9001"
```

## 运行环境 Docker 镜像构建

运行环境 Docker 镜像，不含代码，只作为伏羲的运行环境，代码需要在宿主机通过-v 的方式映射到容器内的`/seatom/seatom-server`目录下。容器的启动方式和标准镜像的相同，参考标准容器的启动方式。

Docker 镜像构建：

```
cd docker
docker build -f Dockerfile-runtime -t docker.art.haizhi.com/dmc/seatom-server-runtime .
```
## Docker 镜像启动方式
docker run -it -d \
 --name seatom-server \
 --privileged \
 -v /Users/<USER>/Desktop/seatom/seatom-server/node_modules:/seatom/seatom-server/node_modules \
 -v /Users/<USER>/Desktop/seatom/seatom-server/app:/seatom/seatom-server/app \
 -v /Users/<USER>/Desktop/seatom/seatom-server/app.js:/seatom/seatom-server/app.js \
 -v /Users/<USER>/Desktop/seatom/seatom-server/config:/seatom/seatom-server/config \
 -v /Users/<USER>/Desktop/seatom/seatom-server/package.json:/seatom/seatom-server/package.json \
 -v /Users/<USER>/Desktop/seatom/seatom-server/licence.txt:/seatom/seatom-server/licence.txt \
 -v /Users/<USER>/Desktop/seatom/seatom-server/run:/seatom/seatom-server/run \
 -v /Users/<USER>/Desktop/seatom/resource-server:/seatom/seatom-server/resource-server \
 -v /Users/<USER>/Desktop/seatom/atom:/seatom/atom \
 -v /Users/<USER>/Desktop/seatom/seatom-server-logs:/seatom/seatom-server-logs \
 -p 7001:7001 \
 -p 1115:1115 \
docker.art.haizhi.com/dmc/seatom-server-standard:v2
## Docker 镜像启动方式（有minio）
docker run -it -d \
 --name seatom-server \
 --privileged \
 --link minio \
 -e ENDPOINT=http://minio:9000 \
 -e BUCKET="fuxi" \
 -e AWS_ACCESS_KEY_ID=minioadmin \
 -e AWS_SECRET_ACCESS_KEY=minioadmin \
 -v /Users/<USER>/Desktop/seatom/seatom-server/node_modules:/seatom/seatom-server/node_modules \
 -v /Users/<USER>/Desktop/seatom/seatom-server/app:/seatom/seatom-server/app \
 -v /Users/<USER>/Desktop/seatom/seatom-server/app.js:/seatom/seatom-server/app.js \
 -v /Users/<USER>/Desktop/seatom/seatom-server/config:/seatom/seatom-server/config \
 -v /Users/<USER>/Desktop/seatom/seatom-server/package.json:/seatom/seatom-server/package.json \
 -v /Users/<USER>/Desktop/seatom/seatom-server/licence.txt:/seatom/seatom-server/licence.txt \
 -v /Users/<USER>/Desktop/seatom/seatom-server/run:/seatom/seatom-server/run \
 -v /Users/<USER>/Desktop/seatom/resource-server:/seatom/seatom-server/resource-server \
 -v /Users/<USER>/Desktop/seatom/atom:/seatom/atom \
 -v /Users/<USER>/Desktop/seatom/seatom-server-logs:/seatom/seatom-server-logs \
 -p 7001:7001 \
 -p 1115:1115 \
 docker.art.haizhi.com/dmc/seatom-server-runtime:latest

以上启动方式依赖 minio 服务，本机测试，可以通过如下方式启动一个临时的 minio 容器：

```
docker run --name minio -d -p 9000:9000 -p 9001:9001 minio/minio server /data --console-address ":9001"
> 注：暂不支持ARM架构

## 上传静态资源到minio

可以借助构建的docker镜像，可以使用内部的goofys将静态资源上传到minio，操作示例如下：

```

# 1. 先启动一个临时容器，将本地目录映射到容器内的/upload 目录下

docker run -it --rm \
 --name upload \
 --link minio \
 --privileged \
 -e ENDPOINT=http://minio:9000 \
 -e AWS_ACCESS_KEY_ID=minioadmin \
 -e AWS_SECRET_ACCESS_KEY=minioadmin \
 -v local_resource_dir:/upload \
 docker.art.haizhi.com/dmc/seatom-server-runtime:latest sh

# 2. 进入到上一步启动的容器中执行`start-s3fs`将 minio 挂载到/mnt/s3 目录

# 3. 将/upload 目录下的文件复制到/mnt/s3 目录下即可

# 4. 操作完成后，将第一步创建的容器退出

```

## TODO

- [ ] ARM支持Oracle (node:16-alpine3.16版本支持resource-server图片处理模块的sharp依赖,seatom-server后台服务的截图程序使用支撑hz-screenshot库v2.0.0版本)
- [ ] seatom启动时基于配置文件判断是否将文件存储到minio，来确定是否要启动goofys服务
```
