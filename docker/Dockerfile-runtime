FROM node:16-alpine3.16

ARG GIT_COMMIT

LABEL maintainer="翟士丹@海致，<EMAIL>"
LABEL description="seatom-server"
LABEL git-commit=${GIT_COMMIT}

# 阿里云加速；使用国内时区
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
    # 安装中文字体wqy-zenhei的源
    && echo "https://mirrors.aliyun.com/alpine/edge/testing/" >> /etc/apk/repositories \
    && apk add --no-cache tzdata \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && apk del tzdata

# puppeteer依赖，谷歌浏览器及中文字体相关（支撑hz-screenshot库）
RUN apk add --no-cache \
    font-noto font-noto-emoji fontconfig \
    freetype ttf-dejavu ttf-droid ttf-freefont ttf-liberation wqy-zenhei ttf-tlwg \
    chromium \
    pngquant

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# s3fs将minio映射为本地FUSE文件系统，镜像构建参考：https://github.com/rjocoleman/docker-alpine-s3fs
# minio对接参考：https://github.com/nitisht/cookbook/blob/master/docs/s3fs-fuse-with-minio.md
ARG S3FS_VERSION=v1.91
ENV BUCKET fuxi
ENV MOUNT_DIR /mnt/s3

RUN apk add fuse curl libxml2 \
    && apk add --virtual .build-deps alpine-sdk automake autoconf libxml2-dev fuse-dev curl-dev git bash
RUN git clone https://github.91chi.fun//https://github.com/s3fs-fuse/s3fs-fuse.git \
    && cd s3fs-fuse \
    && git checkout tags/${S3FS_VERSION} \
    && ./autogen.sh \
    && ./configure --prefix=/usr \
    && make \
    && make install \
    && cd .. \
    && rm -rf s3fs-fuse \
    && touch /etc/s3cred \
    && chmod 600 /etc/s3cred \
    && apk del .build-deps \
    && rm -rf /var/cache/apk/* /tmp/*
RUN echo '#/bin/sh' > /usr/bin/start-s3fs \
    && echo 'echo "${AWS_ACCESS_KEY_ID}:${AWS_SECRET_ACCESS_KEY}" > /etc/s3cred' >> /usr/bin/start-s3fs \
    && echo 'mkdir -p ${MOUNT_DIR}' >> /usr/bin/start-s3fs \
    && echo 's3fs ${BUCKET} ${MOUNT_DIR} -o passwd_file=/etc/s3cred,use_path_request_style,url=${ENDPOINT}' >> /usr/bin/start-s3fs \
    && chmod +x /usr/bin/start-s3fs

# Oracle驱动相关，参考：https://github.com/asg1612/alpine-oracle-instantclient
RUN apk add --no-cache libaio libnsl libc6-compat
COPY instantclient_12_1.zip ./
RUN unzip instantclient_12_1.zip \
    && mv instantclient_12_1/ /usr/lib/ \
    && rm instantclient_12_1.zip \
    && ln -s /usr/lib/instantclient_12_1/libclntsh.so.12.1 /usr/lib/instantclient_12_1/libclntsh.so \
    && ln -s /usr/lib/instantclient_12_1/libocci.so.12.1 /usr/lib/instantclient_12_1/libocci.so \
    && ln -s /usr/lib/libnsl.so.2 /usr/lib/libnsl.so.1 \
    && ln -s /lib/libc.so.6 /usr/lib/libresolv.so.2 \
    && ln -s /lib64/ld-linux-x86-64.so.2 /usr/lib/ld-linux-x86-64.so.2

ENV LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/lib/instantclient_12_1
ENV OCI_LIB_DIR=/usr/lib/instantclient_12_1
ENV OCI_INC_DIR=/usr/lib/instantclient_12_1/sdk/include

# seatom-server相关（只创建seatom-server的运行环境，不含代码，需要容器外面挂载进来）
RUN mkdir -p /seatom/seatom-server \
    && npm install --registry=https://artifactory.haizhi.com/artifactory/api/npm/npm/ @seatom/cli @seatom/vue-builder -g

WORKDIR /seatom/seatom-server
VOLUME ["/seatom/seatom-server/app/", "/seatom/seatom-server/resource-server/", "/seatom/seatom-server/config/", "/seatom/seatom-server/log/"]
EXPOSE 7001 1115
CMD start-s3fs \
    && npm run start-prod \
    && cd resource-server && node index
