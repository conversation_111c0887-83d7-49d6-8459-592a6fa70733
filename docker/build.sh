#!/bin/bash
HOME="$(cd `dirname "${BASH_SOURCE-$0}"`/..; pwd)"
cd ${HOME}/docker

# 源码准备
mkdir seatom-server-code
cp -r ../app seatom-server-code/
cp -r ../app.js seatom-server-code/
cp -r ../oracledb-src-* seatom-server-code/
cp -r ../package.json seatom-server-code/
cp -r ../resource-server seatom-server-code/

# docker image构建
TAG=$(git branch | grep -F '*' | awk '{print $2}')
if [ "$TAG" = "master" ]; then
  TAG=latest
fi
OS_ARCH="$(arch)"
IMAGE_NAME="docker.art.haizhi.com/dmc/seatom-server-${OS_ARCH}:${TAG}"
DOCKERFILE=Dockerfile-arm
# i386, x86_64
if [[ ${OS_ARCH} == *86* ]]; then
  DOCKERFILE=Dockerfile
fi
docker build -f "$HOME"/docker/$DOCKERFILE --build-arg GIT_COMMIT="$(git rev-parse HEAD)" -t ${IMAGE_NAME} . 
docker push ${IMAGE_NAME}

# 清理
rm -rf seatom-server-code
cd ${HOME}
