FROM node:16-alpine3.15
ARG GIT_COMMIT

LABEL maintainer="翟士丹@海致，<EMAIL>"
LABEL description="seatom-server"
LABEL git-commit=${GIT_COMMIT}

# 阿里云加速；使用国内时区
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
    # 安装中文字体wqy-zenhei的源
    && echo "https://mirrors.aliyun.com/alpine/edge/testing/" >> /etc/apk/repositories \
    && apk add --no-cache tzdata \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && apk del tzdata

# puppeteer依赖，谷歌浏览器及中文字体相关（支撑hz-screenshot库）
RUN apk add --no-cache \
    font-noto font-noto-emoji fontconfig \
    freetype ttf-dejavu ttf-droid ttf-freefont ttf-liberation wqy-zenhei ttf-tlwg \
    chromium \
    pngquant

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Oracle驱动相关，参考：https://github.com/asg1612/alpine-oracle-instantclient
RUN apk update && apk add --no-cache libaio
COPY instantclient_12_1.zip ./
RUN unzip instantclient_12_1.zip \
    && mv instantclient_12_1/ /usr/lib/ \
    && rm instantclient_12_1.zip \
    && ln -s /usr/lib/instantclient_12_1/libclntsh.so.12.1 /usr/lib/instantclient_12_1/libclntsh.so \
    && ln -s /usr/lib/instantclient_12_1/libocci.so.12.1 /usr/lib/instantclient_12_1/libocci.so \
    && ln -s /usr/lib/libnsl.so.2 /usr/lib/libnsl.so.1 \
    && ln -s /lib/libc.so.6 /usr/lib/libresolv.so.2 \
    && ln -s /lib64/ld-linux-x86-64.so.2 /usr/lib/ld-linux-x86-64.so.2

ENV LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/lib/instantclient_12_1
ENV OCI_LIB_DIR=/usr/lib/instantclient_12_1
ENV OCI_INC_DIR=/usr/lib/instantclient_12_1/sdk/include

# seatom-server相关（只创建seatom-server的运行环境，不含代码，需要容器外面挂载进来）
COPY ./node_modules   /deploy-init/node_modules
WORKDIR /deploy-init/node_modules/
RUN cd /deploy-init/node_modules/
RUN npm i ./@seatom/cli/ -g
WORKDIR /deploy-init/node_modules/
RUN cd /deploy-init/node_modules/
RUN npm i ./@seatom/vue-builder/ -g
WORKDIR /deploy-init/node_modules/
RUN cd /deploy-init/node_modules/
RUN npm i ./pm2/ -g

RUN mkdir /deploy-init/seatom-server && cd /deploy-init/seatom-server/
CMD npm run restart-prod