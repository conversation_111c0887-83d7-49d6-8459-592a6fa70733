#!/bin/bash

# 获取上级目录的路径
parent_dir=$(dirname "$(pwd)")
now_dir=$(pwd)

# 获取当前日期，格式为YYYYMMDD
current_date=$(date +%Y%m%d)

# 基本文件夹名
base_folder="seatom-server_bak${current_date}"

# 检查上级目录中是否存在同名文件夹，并添加后缀直到找到一个不存在的文件夹名
counter=1
folder_name="${base_folder}"
while [ -d "${parent_dir}/${folder_name}" ]; do
  folder_name="${base_folder}-${counter}"
  ((counter++))
done

# 在上级目录创建文件夹
mkdir "${parent_dir}/${folder_name}"

# 输出创建的文件夹名
echo "Created folder: ${parent_dir}/${folder_name}"

# 使用find命令排除以seatom-server_bak开头的文件夹，然后将其余的文件和文件夹移动到新创建的文件夹中
find "${now_dir}" -maxdepth 1 \
! -name "$(basename "${now_dir}")" \
! -name "update-code.sh" \
! -name "seatom-server*.tgz" \
-exec mv {} "${parent_dir}/${folder_name}/" \;

# 输出完成迁移信息
echo "完成内容迁移，迁移目录为 ${parent_dir}/${folder_name}"

# 在当前目录解压seatom-server压缩包
tar -zxf seatom-server*.tgz
echo "==完成解压=="

# 删除压缩包
rm -rf seatom-server*.tgz

# 移动文件到当前目录，脚本直接覆盖
mv -f seatom-server/* .

#删除文件夹
rm -rf seatom-server

#将上级目录的licence.txt 和 config.prod.js复制过来
cp -rf "${parent_dir}/${folder_name}/licence.txt" .
cp -rf "${parent_dir}/${folder_name}/config/config.prod.js" ./config/
echo "==完成证书和配置文件移动=="

echo "==完成更新，请进入docker重启seatom-server服务=="


