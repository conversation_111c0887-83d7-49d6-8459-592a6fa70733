const base64 = require('base-64')
const userObj = {
  shscj: base64.encode(base64.encode('888888')),
  kchx: base64.encode(base64.encode('kchx2013')),
  htkj: base64.encode(base64.encode('htkjhz278')),
  adminhn: base64.encode(base64.encode('123456Aa')),
  local_admin: base64.encode(base64.encode('local_admin')), // Ykc5allXeGZZV1J0YVc0PQ==
  lzz_admin: base64.encode(base64.encode('lzz_admin')),
  admin: base64.encode(base64.encode('haizhi1234')),
  sy: base64.encode(base64.encode('888888')),
  local_admin1: base64.encode(base64.encode('local_admin1')), // Ykc5allXeGZZV1J0YVc0PQ==
  local_admin2: base64.encode(base64.encode('local_admin2')), // Ykc5allXeGZZV1J0YVc0PQ==
  local_admin3: base64.encode(base64.encode('local_admin3')), // Ykc5allXeGZZV1J0YVc0PQ==
  gdt: base64.encode(base64.encode('123456')), // Ykc5allXeGZZV1J0YVc0PQ==
  wzh: base64.encode(base64.encode('123456')),
  gw: base64.encode(base64.encode('123456')),
  dxf: base64.encode(base64.encode('123456')),
  yc: base64.encode(base64.encode('123456')),
  wcc: base64.encode(base64.encode('123456')),
  cjl: base64.encode(base64.encode('123456')),
  lbl: base64.encode(base64.encode('123456')),
  bf_suxuerui: 'YzNoeU1qa3hOVGMzTWpJPQ==',
  bf_sunbotao: 'YzJKME5UQTJOelUyTlRjPQ==',
  bf_zhangshunmin: 'ZW5OdE9UVXpNREE1T0RFPQ==',
  bf_songyanzhao: 'YzNsNk5UUTNPREUyTXpVPQ==',
  bf_liuwang: 'YkhjeU56azBNVGcyTVE9PQ==',
  bf_lilinlin: 'Ykd4c05EVTJORGc0TlRVPQ==',
  bf_chengyuanyang: 'WTNsNU9ETTBNVFkxTVRnPQ==',
  bf_baixiaoli: 'WW5oc056RTBPREk0T1RRPQ==',
  bf_songhuihui: 'YzJob09UUTRPVFE1TWpVPQ==',
  bf_liangkaijun: 'Ykd0cU9USTFOVFk1TkRFPQ==',
  bf_chenxianhao: 'WTNob056RTNORGcwT1RNPQ==',
  bf_songzhiqiao: 'YzNweE9URTJOemsxT1RFPQ==',
  bf_zhufeng: 'ZW1ZMU1UUTVPRGsyT0E9PQ==',
  bf_wenyizhe: 'ZDNsNk9ERTVOekV3TmpNPQ==',
  bf_yangsongtao: 'ZVhOME1qWXpPRFl6TlRBPQ==',
  bf_yujiayu: 'ZVdwNU1UZ3lORGN5TnpjPQ==',
  bf_dengxiang: 'WkhneE5qYzJNVGt3TkE9PQ==',
  bf_yaosaiya: 'ZVhONU1qY3dNRFkwTVRrPQ==',
  bf_duanjiawei: 'WkdwM05USTBNemd3TVRFPQ==',
  bf_liyulin: 'Ykhsc05UQTNNalUwTlRFPQ==',
  bf_zhanglun: 'ZW13eE9ETTJOek01T0E9PQ==',
  bf_shuaikeqin: 'YzJ0eE1qVTNOekF4TURRPQ==',
  bf_liuyi: 'YkhrME5EZzFOamsyTWc9PQ==',
  sd_guominghui: 'WjIxb09UUTRNakk0TURnPQ==',
  sd_guolianxiao: 'WjJ4NE1UZzBOak0wTkRnPQ==',
  sd_zhaoxin: 'ZW5nNE5Ua3dOak0xTmc9PQ==',
  sd_zhaoyan: 'ZW5rek1qUTFPRE0yTUE9PQ==',
  sd_liangchuan: 'YkdNNE9EWXlPREF6Tmc9PQ==',
  sd_xinghui: 'ZUdneE16UTFNalk0T0E9PQ==',
  sd_shengzunhui: 'YzNwb05EWXhOek0zTXpjPQ==',
  sd_songfankun: 'YzJack56RTBNemd5TmprPQ==',
  sd_lilinkang: 'Ykd4ck9ESTFOVE01TWpVPQ==',
  sd_yangshaochen: 'ZVhOak5qZ3pNemsxTXpRPQ==',
  hb_taodesen: 'ZEdSek1qVXpPRE0yTlRFPQ==',
  hb_zhangjin: 'ZW1vME5qZ3hNRFkxTmc9PQ==',
  hb_xumengling: 'ZUcxc01UTXpNREExTWpVPQ==',
  hb_chenchang: 'WTJNMk1qTXhNekF6TVE9PQ==',
  hb_huangnianci: 'YUc1ak1qYzNNalF4TVRrPQ==',
  hb_zhanghan: 'ZW1nM016Y3lOREUwTnc9PQ==',
  hb_caochao: 'WTJNME16YzNORFl6TWc9PQ==',
  hb_xuwei: 'ZUhjNE9Ua3dPVE00TVE9PQ==',
  hb_machen: 'YldNNE1EWTBOVEU1TXc9PQ==',
  hb_changzhilong: 'WTNwc05qQTBNRGt4TWpFPQ==',
  hb_xiaoyue: 'ZUhrME1UazBNalE0TWc9PQ==',
  hb_huangzhuo: 'YUhvNU1USTJNamMwT1E9PQ==',
  hb_danzhangyu: 'WkhwNU5qQTRNVE0xTXprPQ==',
  hb_liuhongkai: 'Ykdock1UTXhPVEkzTkRVPQ==',
  hb_xiezhenwei: 'ZUhwM01qRXdNVEk1T1RRPQ==',
  js_huxingfei: 'YUhobU5USTBPRE0zTnpBPQ==',
  js_yanbing: 'ZVdJMU5qWTROell4TWc9PQ==',
  js_yangliang: 'ZVd3MU1qTTFNek0zTXc9PQ==',
  js_wuhongfeng: 'ZDJobU5qWXdOemt3TkRBPQ==',
  js_duqing: 'WkhFME5EWTBNVGc0TXc9PQ==',
  js_lijun: 'YkdvNE9UTTVNekkzTUE9PQ==',
  js_wanganle: 'ZDJGc09UYzNNREExTWpVPQ==',
  xj_qiuhaibo: 'Y1doaU5qRTVNVGMyTVRVPQ==',
  xj_liuluyang: 'Ykd4NU5UY3pNRFkxT1RFPQ==',
  xj_wangyuchun: 'ZDNsak16QTVNekEyTURrPQ==',
  xj_chujin: 'WTJvNE5qQXhOREV5T1E9PQ==',
  xj_weijunnan: 'ZDJwdU56RTFPRFF4TlRVPQ==',
  sg_lihaohan: 'Ykdob05ESXdNVEV3TWprPQ==',
  sg_huangjie: 'YUdvNE16VXpNVEE0Tnc9PQ==',
  sg_fangrenfeng: 'Wm5KbU56RTBNVGMwTURVPQ==',
  sg_likanglei: 'Ykd0c05qTXlOekF4TlRBPQ==',
  sg_lisixin: 'YkhONE5qSTFORE00TURjPQ==',
  hn_yisiwei: 'ZVhOM01qa3hOalE0TnpnPQ==',
  hn_wangxiangyu: 'ZDNoNU5qRXlNalU0TWpjPQ==',
  hn_chencan: 'WTJNNE5USTVNalEzTmc9PQ==',
  hn_linguangdong: 'Ykdka01UWXhNakEwT1RNPQ==',
  hn_zhaojie: 'ZW1vNE5ESXdNRGN3Tmc9PQ==',
  hn_lijunnan: 'YkdwdU5qWTBPREkyTWpZPQ==',
  hn_chenziyan: 'WTNwNU1qYzNOVGcwTmpnPQ==',
  hn_linyuezhong: 'YkhsNk56TTVOakEzTVRrPQ==',
  hn_wuxitao: 'ZDNoME56YzFNekV3T0RRPQ==',
  hn_luoyang: 'YkhrMk1qWXhORGt5T1E9PQ==',
  hn_husimin: 'YUhOdE16UTNOelEzTmpBPQ==',
  hn_dengzhiyang: 'WkhwNU9EVTRORFF6TnpNPQ==',
  hn_zhangjiaxin: 'ZW1wNE16QXpOVE14T1RrPQ==',
  hn_zhuangjuteng: 'ZW1wME56QXdOalkyTURNPQ==',
  hn_chenchunyan: 'WTJONU1qSTFPVGMyTnpjPQ==',
  hn_chenjiandong: 'WTJwa056TXpORE0wT1RBPQ==',
  hn_zhangkailiang: 'ZW10c05UZzJNVEkzTkRNPQ==',
  hn_liuzhanyang: 'YkhwNU1qVXdNREl3TlRjPQ==',
  hn_qiuzhehong: 'Y1hwb016azROVGN3TkRFPQ==',
  hn_kuinanhui: 'YTI1b016TXlOalEyTnpnPQ==',
  hn_zouyuan: 'ZW5rNE16RTVORE00TUE9PQ==',
  hd_shijiaoshuai: 'YzJwek5qUTBORFl6T0RZPQ==',
  hd_wutianping: 'ZDNSd01UVXhNRGc1TnpnPQ==',
  hd_lihaojiang: 'YkdocU5ERTBOekF6TURJPQ==',
  hd_liuwenjing: 'YkhkcU1qTTRNREV3TmpRPQ==',
  hd_wangtenglong: 'ZDNSc05qZzVOVGd6T1RZPQ==',
  hd_chenlinxue: 'WTJ4NE9EUTJPVGc0TlRrPQ==',
  hd_tangxiaohang: 'ZEhob09UQTFNRGMzT1RjPQ==',
  hd_liuxiao: 'YkhnM01EZzNNamt4TVE9PQ==',
  hd_wenhaifeng: 'ZDJobU1qazJOVFV4TmpnPQ==',
  hd_wanghaoliang: 'ZDJoc016WTNNRGM0TnprPQ==',
  hd_pengyu: 'Y0hrMk1ETXpNalUyTXc9PQ==',
  hd_luozhitao: 'YkhwME1USXpNemd5TmpFPQ==',
  hd_baiwenhui: 'WW5kb016YzVOakl3T0RBPQ==',
  hd_zhaojinke: 'ZW1wck1UazFNREkwTWpZPQ==',
  hd_liuqianhe: 'YkhGb01Ua3dNemsxT1RRPQ==',
  hd_zhouxuetao: 'ZW5oME56RTBPVEl5TWpNPQ==',
  hd_lixin: 'YkhnNU9EWTNOVEE0T0E9PQ==',
  hd_wangshaojie: 'ZDNOcU9EQXpORFl4TmpJPQ==',
  hd_liugangqiang: 'YkdkeE5EWTNORFV5TVRVPQ==',
  hd_wangze: 'ZDNvNE16QTFOekk1TkE9PQ==',
  hd_chenxuan: 'WTNnek9ESXdNRGt3Tmc9PQ==',
  hd_wangpengfei: 'ZDNCbU1qWTVOamc1TVRZPQ==',
  hd_hetianhao: 'YUhSb05ERXlOalEzTXpFPQ==',
  hd_luoshaokai: 'YkhOck16YzNNRE0wTkRRPQ==',
  hd_lijinchao: 'Ykdwak1UazFNekExTXpJPQ==',
  hd_chenyuqi: 'WTNseE5qa3hNekUzTlRRPQ==',
  hd_wangdalong: 'ZDJSc05qUXpNek01TkRrPQ==',
  hd_chenzixuan: 'WTNwNE56UXhNemczTVRRPQ==',
  hd_zengjie: 'ZW1vek9EUXlNRFV6TUE9PQ==',
  hd_zhangaowei: 'ZW1GM016WXpOekU1TlRnPQ==',
  hd_yuanhuaping: 'ZVdod09ETTRPREk1TVRRPQ==',
  hd_dongbinrui: 'WkdKeU1qVTJNRFk0TkRnPQ==',
  hd_yezi: 'ZVhvNU56ZzROekkxTmc9PQ==',
  hd_liudong: 'YkdRek9UVTFOek14TlE9PQ==',
  hd_chenchunliang: 'WTJOc05qRXdOakkwTnpBPQ==',
  hd_pengyi: 'Y0hrME1UUTFNekl5TUE9PQ==',
  hd_zengzhixiao: 'ZW5wNE1qWTBNVGswTURrPQ==',
  jx_luowei: 'YkhjNE1ERXhNVEExTWc9PQ==',
  jx_hewenqiang: 'YUhkeE5USTBOVGc1TnpjPQ==',
  jx_tuxinling: 'ZEhoc016SXdNVFl5TmpNPQ==',
  jx_liling: 'Ykd3ME5ETXhOelk0TlE9PQ==',
  jx_zhuxingchen: 'ZW5oak16WXlNakl6TWpjPQ==',
  jx_chengpengyu: 'WTNCNU1URTVNRGc0TnpRPQ==',
  jx_hejunliang: 'YUdwc05EQTJORFV3TnpRPQ==',
  jx_zhangmincong: 'ZW0xak5UazNNamszTnpNPQ==',
  jx_zouguoxiong: 'ZW1kNE1UUXlPVFExT1RBPQ==',
  jx_jiaojiayang: 'YW1wNU5UZzJPRFkyT1RZPQ==',
  jx_luohengfeng: 'YkdobU5EVXlNRFkxTnpjPQ==',
  jx_zhouyu: 'ZW5rNE9EVXpNRE01TXc9PQ==',
  jx_chengtianxing: 'WTNSNE5ERTRPVEl5TlRjPQ==',
  jx_liyunhui: 'Ykhsb056WXlPRFkxTURZPQ==',
  jx_liuyuan: 'YkhrME9UVTNORGswT0E9PQ=='
}
const domainCity = {
  shscj: 'scj',
  kchx: 'local',
  gdt: 'local',
  adminhn: 'local',
  htkj: 'local',
  local_admin: 'local',
  lbl: 'local',
  lzz_admin: 'lzz',
  admin: 'hnst',
  sy: 'haizhi',
  local_admin1: 'local',
  local_admin2: 'local',
  local_admin3: 'local',
  bf_suxuerui: 'local',
  bf_sunbotao: 'local',
  gdt: 'local', // Ykc5allXeGZZV1J0YVc0PQ==
  wzh: 'local',
  gw: 'local',
  dxf: 'local',
  yc: 'local',
  wcc: 'local',
  cjl: 'local',
  bf_zhangshunmin: 'local',
  bf_songyanzhao: 'local',
  bf_liuwang: 'local',
  bf_lilinlin: 'local',
  bf_chengyuanyang: 'local',
  bf_baixiaoli: 'local',
  bf_songhuihui: 'local',
  bf_liangkaijun: 'local',
  bf_chenxianhao: 'local',
  bf_songzhiqiao: 'local',
  bf_zhufeng: 'local',
  bf_wenyizhe: 'local',
  bf_yangsongtao: 'local',
  bf_yujiayu: 'local',
  bf_dengxiang: 'local',
  bf_yaosaiya: 'local',
  bf_duanjiawei: 'local',
  bf_liyulin: 'local',
  bf_zhanglun: 'local',
  bf_shuaikeqin: 'local',
  bf_liuyi: 'local',
  sd_guominghui: 'local',
  sd_guolianxiao: 'local',
  sd_zhaoxin: 'local',
  sd_zhaoyan: 'local',
  sd_liangchuan: 'local',
  sd_xinghui: 'local',
  sd_shengzunhui: 'local',
  sd_songfankun: 'local',
  sd_lilinkang: 'local',
  sd_yangshaochen: 'local',
  hb_taodesen: 'local',
  hb_zhangjin: 'local',
  hb_xumengling: 'local',
  hb_chenchang: 'local',
  hb_huangnianci: 'local',
  hb_zhanghan: 'local',
  hb_caochao: 'local',
  hb_xuwei: 'local',
  hb_machen: 'local',
  hb_changzhilong: 'local',
  hb_xiaoyue: 'local',
  hb_huangzhuo: 'local',
  hb_danzhangyu: 'local',
  hb_liuhongkai: 'local',
  hb_xiezhenwei: 'local',
  js_huxingfei: 'local',
  js_yanbing: 'local',
  js_yangliang: 'local',
  js_wuhongfeng: 'local',
  js_duqing: 'local',
  js_lijun: 'local',
  js_wanganle: 'local',
  xj_qiuhaibo: 'local',
  xj_liuluyang: 'local',
  xj_wangyuchun: 'local',
  xj_chujin: 'local',
  xj_weijunnan: 'local',
  sg_lihaohan: 'local',
  sg_huangjie: 'local',
  sg_fangrenfeng: 'local',
  sg_likanglei: 'local',
  sg_lisixin: 'local',
  hn_yisiwei: 'local',
  hn_wangxiangyu: 'local',
  hn_chencan: 'local',
  hn_linguangdong: 'local',
  hn_zhaojie: 'local',
  hn_lijunnan: 'local',
  hn_chenziyan: 'local',
  hn_linyuezhong: 'local',
  hn_wuxitao: 'local',
  hn_luoyang: 'local',
  hn_husimin: 'local',
  hn_dengzhiyang: 'local',
  hn_zhangjiaxin: 'local',
  hn_zhuangjuteng: 'local',
  hn_chenchunyan: 'local',
  hn_chenjiandong: 'local',
  hn_zhangkailiang: 'local',
  hn_liuzhanyang: 'local',
  hn_qiuzhehong: 'local',
  hn_kuinanhui: 'local',
  hn_zouyuan: 'local',
  hd_shijiaoshuai: 'local',
  hd_wutianping: 'local',
  hd_lihaojiang: 'local',
  hd_liuwenjing: 'local',
  hd_wangtenglong: 'local',
  hd_chenlinxue: 'local',
  hd_tangxiaohang: 'local',
  hd_liuxiao: 'local',
  hd_wenhaifeng: 'local',
  hd_wanghaoliang: 'local',
  hd_pengyu: 'local',
  hd_luozhitao: 'local',
  hd_baiwenhui: 'local',
  hd_zhaojinke: 'local',
  hd_liuqianhe: 'local',
  hd_zhouxuetao: 'local',
  hd_lixin: 'local',
  hd_wangshaojie: 'local',
  hd_liugangqiang: 'local',
  hd_wangze: 'local',
  hd_chenxuan: 'local',
  hd_wangpengfei: 'local',
  hd_hetianhao: 'local',
  hd_luoshaokai: 'local',
  hd_lijinchao: 'local',
  hd_chenyuqi: 'local',
  hd_wangdalong: 'local',
  hd_chenzixuan: 'local',
  hd_zengjie: 'local',
  hd_zhangaowei: 'local',
  hd_yuanhuaping: 'local',
  hd_dongbinrui: 'local',
  hd_yezi: 'local',
  hd_liudong: 'local',
  hd_chenchunliang: 'local',
  hd_pengyi: 'local',
  hd_zengzhixiao: 'local',
  jx_luowei: 'local',
  jx_hewenqiang: 'local',
  jx_tuxinling: 'local',
  jx_liling: 'local',
  jx_zhuxingchen: 'local',
  jx_chengpengyu: 'local',
  jx_hejunliang: 'local',
  jx_zhangmincong: 'local',
  jx_zouguoxiong: 'local',
  jx_jiaojiayang: 'local',
  jx_luohengfeng: 'local',
  jx_zhouyu: 'local',
  jx_chengtianxing: 'local',
  jx_liyunhui: 'local',
  jx_liuyuan: 'local'
}

const userNameList = {
  shscj: 'shscj',
  kchx: '科晨洪兴',
  gdt: '国电通',
  adminhn: '湖南电力',
  htkj: '航天科技',
  local_admin: '本地',
  local_admin1: '本地1',
  local_admin2: '本地2',
  local_admin3: '本地3',
  gdt: 'local', // Ykc5allXeGZZV1J0YVc0PQ==
  wzh: 'local',
  gw: 'local',
  dxf: 'local',
  yc: 'local',
  wcc: 'local',
  cjl: 'local',
  lbl: '刘炳礼',
  bf_suxuerui: '苏学瑞',
  bf_sunbotao: '孙博韬',
  bf_zhangshunmin: '张顺民',
  bf_songyanzhao: '宋彦朝',
  bf_liuwang: '刘旺',
  bf_lilinlin: '李林林',
  bf_chengyuanyang: '程远扬',
  bf_baixiaoli: '白小莉',
  bf_songhuihui: '宋慧慧',
  bf_liangkaijun: '梁凯钧',
  bf_chenxianhao: '陈显浩',
  bf_songzhiqiao: '宋之乔',
  bf_zhufeng: '朱峰',
  bf_wenyizhe: '温伊哲',
  bf_yangsongtao: '杨松涛',
  bf_yujiayu: '于佳玉',
  bf_dengxiang: '邓祥',
  bf_yaosaiya: '姚赛娅',
  bf_duanjiawei: '段佳伟',
  bf_liyulin: '李玉林',
  bf_zhanglun: '张伦',
  bf_shuaikeqin: '帅克秦',
  bf_liuyi: '刘毅',
  sd_guominghui: '郭明辉',
  sd_guolianxiao: '郭连晓',
  sd_zhaoxin: '赵新',
  sd_zhaoyan: '赵岩',
  sd_liangchuan: '梁川',
  sd_xinghui: '邢辉',
  sd_shengzunhui: '盛尊辉',
  sd_songfankun: '宋繁坤',
  sd_lilinkang: '李林康',
  sd_yangshaochen: '杨少晨',
  hb_taodesen: '陶德森',
  hb_zhangjin: '张进',
  hb_xumengling: '徐梦玲',
  hb_chenchang: '陈昌',
  hb_huangnianci: '黄念慈',
  hb_zhanghan: '张寒',
  hb_caochao: '曹超',
  hb_xuwei: '徐巍',
  hb_machen: '马晨',
  hb_changzhilong: '昌志龙',
  hb_xiaoyue: '肖悦',
  hb_huangzhuo: '黄卓',
  hb_danzhangyu: '单张宇',
  hb_liuhongkai: '刘红凯',
  hb_xiezhenwei: '谢振威',
  js_huxingfei: '胡兴飞',
  js_yanbing: '闫兵',
  js_yangliang: '杨亮',
  js_wuhongfeng: '吴宏峰',
  js_duqing: '杜卿',
  js_lijun: '李俊',
  js_wanganle: '王安乐',
  xj_qiuhaibo: '邱海波',
  xj_liuluyang: '刘路杨',
  xj_wangyuchun: '王玉春',
  xj_chujin: '楚金',
  xj_weijunnan: '卫俊南',
  sg_lihaohan: '李昊晗',
  sg_huangjie: '黄杰',
  sg_fangrenfeng: '方任峰',
  sg_likanglei: '李康磊',
  sg_lisixin: '李思鑫',
  hn_yisiwei: '易思伟',
  hn_wangxiangyu: '王祥宇',
  hn_chencan: '陈灿',
  hn_linguangdong: '林广东',
  hn_zhaojie: '赵杰',
  hn_lijunnan: '李俊男',
  hn_chenziyan: '陈子妍',
  hn_linyuezhong: '林月忠',
  hn_wuxitao: '吴锡涛',
  hn_luoyang: '罗洋',
  hn_husimin: '胡思敏',
  hn_dengzhiyang: '邓志阳',
  hn_zhangjiaxin: '张嘉欣',
  hn_zhuangjuteng: '庄巨腾',
  hn_chenchunyan: '陈春燕',
  hn_chenjiandong: '陈健东',
  hn_zhangkailiang: '张凯亮',
  hn_liuzhanyang: '刘展洋',
  hn_qiuzhehong: '邱哲宏',
  hn_kuinanhui: '葵南辉',
  hn_zouyuan: '邹渊',
  hd_shijiaoshuai: '石教帅',
  hd_wutianping: '伍天坪',
  hd_lihaojiang: '黎浩江',
  hd_liuwenjing: '刘文婧',
  hd_wangtenglong: '王腾龙',
  hd_chenlinxue: '陈琳雪',
  hd_tangxiaohang: '唐晓航',
  hd_liuxiao: '刘肖',
  hd_wenhaifeng: '温海峰',
  hd_wanghaoliang: '王浩亮',
  hd_pengyu: '彭誉',
  hd_luozhitao: '罗智涛',
  hd_baiwenhui: '白文慧',
  hd_zhaojinke: '赵金科',
  hd_liuqianhe: '刘牵鹤',
  hd_zhouxuetao: '周雪涛',
  hd_lixin: '李欣',
  hd_wangshaojie: '王邵杰',
  hd_liugangqiang: '刘岗强',
  hd_wangze: '王泽',
  hd_chenxuan: '陈轩',
  hd_wangpengfei: '王鹏飞',
  hd_hetianhao: '贺天昊',
  hd_luoshaokai: '罗少凯',
  hd_lijinchao: '李进超',
  hd_chenyuqi: '陈玉奇',
  hd_wangdalong: '王大龙',
  hd_chenzixuan: '陈子萱',
  hd_zengjie: '曾杰',
  hd_zhangaowei: '张傲伟',
  hd_yuanhuaping: '袁华苹',
  hd_dongbinrui: '董槟瑞',
  hd_yezi: '叶梓',
  hd_liudong: '刘冬',
  hd_chenchunliang: '陈春亮',
  hd_pengyi: '彭益',
  hd_zengzhixiao: '曾智校',
  jx_luowei: '罗威',
  jx_hewenqiang: '何文强',
  jx_tuxinling: '涂欣玲',
  jx_liling: '李玲',
  jx_zhuxingchen: '祝星辰',
  jx_chengpengyu: '程鹏宇',
  jx_hejunliang: '何俊良',
  jx_zhangmincong: '章敏聪',
  jx_zouguoxiong: '邹国雄',
  jx_jiaojiayang: '焦佳阳',
  jx_luohengfeng: '罗恒丰',
  jx_zhouyu: '周瑜',
  jx_chengtianxing: '程添星',
  jx_liyunhui: '李云辉',
  jx_liuyuan: '刘源'
}

module.exports = {
  userObj,
  domainCity,
  userNameList
}
