const _ = require('lodash')
const fs = require('fs-extra')
const path = require('path');
const SeatomException = require('../exception/seatomException');
function resourcePathReplace(res) {
  const screenInfo = JSON.stringify(res.data);
  // 匹配满足条件的 URL
  let result = screenInfo.match(
    /http.*?\/public.*?(\.png|\.jpg|\.js|\.mp4|\.json|\.svg|\.csv|\.gif|\.jpeg)|\/public.*?(\.png|\.jpg|\.js|\.mp4|\.json|\.svg|\.csv|\.gif|\.jpeg)/g
  ) || [];
  // 过滤掉包含 ',' 和 '/packages/' 的 URL
  result = result.filter(item => !item.includes(',') && !item.includes('/packages/'));
  // 去重处理
  result = _.uniq(result);
  // console.log('Matched URLs:', result);
  // 转换匹配的 URL
  const transformedMap = result.reduce((map, url) => {
    const match = url.match(/\/([^\/]+?\.(png|jpg|jpeg|gif|svg|mp4|json|csv))$/);
    if (match) {
      map[url] = `/public/imgs/${match[1]}`; // 构造原始 URL 和替换后的路径映射
    }
    return map;
  }, {});
  // 替换 screenInfo 中的 URL
  let updatedScreenInfo = screenInfo;
  Object.entries(transformedMap).forEach(([original, transformed]) => {
    // 替换所有匹配的 URL
    updatedScreenInfo = updatedScreenInfo.replace(new RegExp(_.escapeRegExp(original), 'g'), transformed);
  });
  // 将替换后的内容写回 res.data
  res.data = JSON.parse(updatedScreenInfo);
  return res;
}

async function batchProcess(list, batchSize, callback) {
  for (let i = 0; i < list.length; i += batchSize) {
    const batch = list.slice(i, i + batchSize);
    // 并发处理当前批次
    // try {
      await Promise.all(batch.map(async (item) => {
        await callback(item); // 确保 callback 是异步的
      }));
    // } catch (error) {
      // throw new SeatomException(400, error.message); // 将异常抛出
    // }
  }
}

async function handleFonts(fonts, config, ctx) {
  const fontsList = fonts.filter(font => font.enable && font.fontUrl)
  for (const font of fontsList) {
    const url = font.fontUrl
    const fontResourcePath = path.resolve(
      config.resourcePath,
      `.${url.replace(/^\/public/, "")}`
    )
    if (!fs.existsSync(fontResourcePath)) {
      ctx.logger.info(`${url}不存在, ${fontResourcePath}`)
      // 如果本地不存在这个组件，则删掉图层里的组件不渲染，也不复制直接return
      // return 0
    }
    const fontTargetPath = path.resolve(config.resourcePath, '../seatom-ssr-next/dist/public/fontlist')
    if (!fs.existsSync(fontTargetPath)) {
      fs.mkdirSync(fontTargetPath, { recursive: true })
    }
    const targetFontPath = path.resolve(
      config.resourcePath,
      `../seatom-ssr-next/dist${url}`
    )
    try {
      if (fs.existsSync(targetFontPath)) {
        ctx.logger.info(`文件已存在，跳过复制: ${targetFontPath}`)
        // return targetFilePath
      }
      await fs.copy(fontResourcePath, targetFontPath, { overwrite: false })
    } catch (err) {
      ctx.logger.info(`复制字体错误，${err.message}`)
    }
    

  }

}

module.exports = {
  resourcePathReplace,
  batchProcess,
  handleFonts
}
