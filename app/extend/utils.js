const _ = require('lodash')
const fs = require('fs')
const path = require('path')
const { nanoid } = require('nanoid')
// const { keys } = require('lodash')
const moment = require('moment')
const base64 = require('base-64')
const { decipher } = require('./crypto')
const md5 = require('md5')

// 设置显示文档的属性  属性值1代表显示 属性值0代表隐藏
/**
 * 获取公共投影
 * @param {object} projection - 投影对象
 * @returns {object} - 公共投影对象
 */
function getCommonProjection(projection) {
  return _.assign(
    {
      _id: 0,
      __v: 0
    },
    projection
  )
}
function getCommonProjection(projection) {
  return _.assign(
    {
      _id: 0,
      __v: 0
    },
    projection
  )
}

function uuid(prefix) {
  return prefix ? `${prefix}_${nanoid()}` : nanoid()
}

/**
 * 旧组件ID转换为新组件ID
 * @param {string} comId 原组件ID
 * @param {number} t 时间戳
 * @returns
 */
function getNewComId(comId = '', t) {
  if (!comId || !comId.split) {
    return ''
  }
  const arr = comId.split('_')
  const a = arr[0] || ''
  const b =
    arr
      .map((val, index) => {
        if (index === 0) {
          return ''
        }
        return val
      })
      .join('') || ''
  return a + '_' + md5(b + t)
}

function isEmpty(a) {
  if (a === '') return true //检验空字符串
  if (a === 'null') return true //检验字符串类型的null
  if (a === 'undefined') return true //检验字符串类型的 undefined
  if (!a && a !== 0 && a !== '') return true //检验 undefined 和 null
  if (Array.prototype.isPrototypeOf(a) && a.length === 0) return true //检验空数组
  if (Object.prototype.isPrototypeOf(a) && Object.keys(a).length === 0)
    return true //检验空对象
  return false
}

// function randomId() {
//   return (
//     (Date.now().toString().substring(2) * 1 +
//       '' +
//       Math.floor(10 + Math.random() * 90)) *
//     1
//   ) // 防止id位数太多导致数据显示科学计数导致导入导出数据报错
// }

/**
 * 获取随机ID，根据时间排序，保证性能还是使用有序的
 * @param {number} size
 * @returns
 */
const randomId = (size = 15) => {
  let urlAlphabet = '1234567890'
  let id = String(
    Math.floor(Date.now() / 1000)
      .toString()
      .substring(2)
  )
  let i = size - id.length
  if (i < 0) {
    return Number(id)
  }
  while (i--) {
    // `| 0` is more compact and faster than `Math.floor()`.
    id += urlAlphabet[(Math.random() * urlAlphabet.length) | 0]
  }
  return Number(id)
}

/**
 * 获取随机ID，无序
 * @param {number} size
 * @returns
 */
// const randomId = (size = 15) => {
//   let timeStamp = Date.now().toString().substring(7)
//   let urlAlphabet = timeStamp + '1234567890' + timeStamp
//   let id = ''
//   let i = size
//   while (i--) {
//     // `| 0` is more compact and faster than `Math.floor()`.
//     id += urlAlphabet[(Math.random() * urlAlphabet.length) | 0]
//   }
//   return Number(id)
// }

/**
 * 获取响应体
 * @param {*} data 数据
 * @param {*} success 是否成功
 * @param {*} message 消息
 * @param {*} code 状态码：[HTTP 响应代码](https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Status)
 * @returns
 */
function getResponseBody(
  data = null,
  success = true,
  message = '',
  code = 200
) {
  return {
    data: data,
    success: !!success,
    message: message,
    code: code
  }
}

/**
 * 获取成功响应体
 * @param {any} data 数据
 * @param {string} message 消息
 * @param {number} code 状态码：[HTTP 响应代码](https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Status)
 * @returns
 */
function getResponseSuccess({ data = null, message = '请求成功', code = 200 }) {
  return {
    data: data,
    // 是否成功
    success: true,
    message: message,
    code: code
  }
}

/**
 * 获取失败响应体
 * @param {any} data 数据
 * @param {string} message 消息
 * @param {number} code 状态码：[HTTP 响应代码](https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Status)
 * @returns
 */
function getResponseError({
  data = null,
  message = '网络发生错误',
  code = 400
}) {
  return {
    data: data,
    // 是否成功
    success: false,
    message: message,
    code: code
  }
}

/**
 * 获取列表响应体
 * @param {*} param0
 * @returns
 */
function getResponseList({ list = [], page = 1, pageSize = 20, total }) {
  return {
    list,
    page,
    pageSize,
    total
  }
}

// 获取当前时区时间
function getDate() {
  const formatDate = moment().format()
  return formatDate
}

function getQueryString(param) {
  return Object.keys(param)
    .map(key => {
      const value =
        typeof param[key] === 'object' ? JSON.stringify(param[key]) : param[key]
      return `${key}=${value}`
    })
    .join('&')
}

const versionRegex = /^\d+(.\d+){2}$/
// 比较版本号
function compareVersion(version1, version2) {
  if (!version1 || !version2) throw new Error('未提供版本号！')
  if (!versionRegex.test(version1) || !versionRegex.test(version2))
    throw new Error('版本号格式有误！')
  const arr1 = version1.split('.').map(d => Number(d))
  const arr2 = version2.split('.').map(d => Number(d))
  let i = 0
  while (i < arr1.length) {
    if (arr1[i] !== arr2[i]) {
      return arr1[i] > arr2[i] ? 1 : -1
    }
    i++
  }
  return 0
}

function randomStr(n) {
  const str = 'abcdefghijklmnopqrstuvwxyz9876543210'
  let tmp = ''
  let i = 0
  const l = str.length
  for (i = 0; i < n; i++) {
    tmp += str.charAt(Math.floor(Math.random() * l))
  }
  return tmp
}

function formatString(template, args) {
  // 把所有${key}替换成args中对应key的value值，支持多层
  var reg = new RegExp('\\${[^}]+}', 'g')
  var keyList = Array.from(template.matchAll(reg), x => x[0])
  var result = template
  keyList.forEach(element => {
    // element: ${xxxxx}, key: xxxxx
    let key = element.slice(2, element.length - 1)
    let value = _.get(args, key, '')
    if (Array.isArray(value)) {
      result = result
        .replace(element, JSON.stringify(value))
        .replace(/\"\[/g, '[')
        .replace(/\]\"/g, ']')
    } else if (typeof value === 'object') {
      result = result
        .replace(element, JSON.stringify(value))
        .replace(/\"\{/g, '{')
        .replace(/\}\"/g, '}')
    } else {
      result = result.replace(element, value)
    }
  })
  return result
}
function getScreenshot(screenshotData) {
  return screenshotData
  // return screenshot({
  //   // 传递参数，调用截图
  //   viewport: {
  //     url: screenshotData.url,
  //     cookies: null,
  //     headers: {
  //       systoken: screenshotData.token,
  //       sysuserid: screenshotData.userId
  //     },
  //     localStorage: {
  //       token: screenshotData.token,
  //       userId: screenshotData.userId
  //     }
  //   },
  //   screenshot: {
  //     top: screenshotData.top ? screenshotData.top : 0,
  //     left: screenshotData.left ? screenshotData.left : 0,
  //     width: screenshotData.width || 1920,
  //     height: screenshotData.height || 1080,
  //     trigger: 'timing',
  //     timing: 10000
  //     // trigger:'fun'
  //   },
  //   output: {
  //     path: screenshotData.path,
  //     name: screenshotData.name,
  //     type: screenshotData.fileType
  //   },
  //   devTools: {
  //     enable: false
  //   }
  // })
}
// 复制并替换资源路径
function copyResource(
  str,
  resourcePath,
  screenTplFolder,
  tplName,
  mainScreenId = null,
  mainScreenName = null
) {
  // 获取系统里面的资源路径
  let res =
    str.match(
      /\public.*?(\.png|\.jpg|\.js|\.mp4|\.json|\.svg|\.csv|\.gif|\.jpeg)/g
    ) || []
  // console.log(res,'dasdsdas========')
  res = res.filter(item => {
    return item.indexOf(',') === -1
  })
  res = res.filter(item => {
    return item.indexOf('/packages/') === -1
  })

  // console.log(res,'dasdsdas========0000000000000000000')
  let newStr = str
  if (!res || res.length === 0) {
    return str
  }
  const folder = path.resolve(resourcePath, `./${screenTplFolder}`)
  const name = path.resolve(resourcePath, `./${screenTplFolder}/${tplName}`)
  if (!fs.existsSync(folder)) {
    fs.mkdirSync(folder)
  }
  if (!fs.existsSync(name)) {
    fs.mkdirSync(name)
  }
  res = _.uniq(res) // 去重
  // this.ctx.logger.info("get final export res: ", res);
  // console.log(res, '导出资源的地址')
  for (let index = 0; index < res.length; index++) {
    let item = res[index]
    let fieldname = item.split('/').pop()
    let newUrl = item.replace('public/', '/')
    let regExp = new RegExp(_.escapeRegExp(item), 'g')
    if (fs.existsSync(path.resolve(resourcePath, `./${newUrl}`))) {
      // debugger
      // console.log(path.resolve(resourcePath, `./${newUrl}`), path.resolve(resourcePath, `./${screenTplFolder}/${tplName}/${fieldname}`),'shenme guigugu')
      if (
        !fs.existsSync(
          path.resolve(
            resourcePath,
            `./${screenTplFolder}/${tplName}/${fieldname}`
          )
        )
      ) {
        fs.copyFileSync(
          path.resolve(resourcePath, `./${newUrl}`),
          path.resolve(
            resourcePath,
            `./${screenTplFolder}/${tplName}/${fieldname}`
          )
        )
      }
      // fs.copyFileSync(path.resolve(resourcePath, `./${newUrl}`), path.resolve(resourcePath, `./${screenTplFolder}/${tplName}/${fieldname}`))
      if (!mainScreenId) {
        newStr = newStr.replace(
          regExp,
          `public/${screenTplFolder}/${tplName}/${fieldname}`
        )
      } else {
        newStr = newStr.replace(
          regExp,
          `public/${screenTplFolder}/fuxi_${mainScreenId}/${tplName}/${fieldname}`
        )
      }
    }
  }
  return newStr
}
// 添加组件默认配置
const addConfig = function (obj, type) {
  _.forOwn(obj, function (value, key) {
    if (value.type === 'image') {
      obj[key].imagePath = type
    }
    if (value.children) {
      addConfig(value.children, type)
    } else {
      return
    }
  })
}

// 获取过期时间
const getExpdate = function (day) {
  const time = new Date().getTime()
  const expdate = time + day * 86400000
  const datestr = `${new Date(expdate).getFullYear()}-${
    new Date(expdate).getMonth() + 1
  }-${new Date(expdate).getDate()}`
  return datestr
}
// 获取路径中的文件名
function getFileName(path) {
  var pos1 = path.lastIndexOf('/')
  var pos2 = path.lastIndexOf('\\')
  var pos = Math.max(pos1, pos2)
  if (pos < 0) return path
  else return path.substring(pos + 1)
}
// 校验第三方接口的token以及令牌密钥
function checkSign(sign, fuxiToken, fuxiPlatform) {
  if (fuxiPlatform !== 'thirdPlatform') {
    return false
  }
  const sign1 = decipher(fuxiToken).split('&&')[0]
  if (sign1 !== sign) {
    return false
  }
  return true
}

// 日志模型
const getLoggerModel = ({ message = '', data }) => {
  return JSON.stringify({
    message: message,
    data: data
  })
}

// 根据图层分组id获取组件id
function getCompsByLayer(tree, id) {
  let compIds = []
  if (!tree || !tree.length) return compIds
  tree.forEach(node => {
    if (id) {
      if (node.id === id) {
        compIds = compIds.concat(getCompsByLayer(node.children))
      } else if (node.type === 'group') {
        compIds = compIds.concat(getCompsByLayer(node.children, id))
      }
    } else {
      if (node.type === 'group') {
        compIds = compIds.concat(getCompsByLayer(node.children))
      } else {
        compIds.push(node.id)
      }
    }
  })
  return compIds
}
// 初始化下转数据
function initDrillData(data, linkObj) {
  const levels = linkObj.links.map(l => l.fieldValue).filter(d => !!d)
  const sums = linkObj.sums.filter(s => !!s)
  if (!levels.length) {
    return data
  }
  var result = []
  const drillField = '下钻字段' // 新增的下钻字段名称
  data.forEach(item => {
    const path = []
    levels.forEach(level => {
      path.push(item[level])
      const obj = {
        ...item,
        _id: path.join('-'),
        _parentId: path.slice(0, -1).join('-'),
        [drillField]: item[level]
      }
      result.push(obj)
    })
  })

  var groupData = _.groupBy(result, '_id')
  const arr = []
  Object.values(groupData).forEach(list => {
    var obj = list.reduce((last, curr, i) => {
      if (i === 0) {
        last = { ...curr }
      }
      sums.forEach(key => {
        if (i === 0) {
          last[key] = 0
        }
        last[key] += +curr[key]
      })
      return last
    }, {})
    arr.push(obj)
  })
  return arr
}
function initDrill(data, linkObj, path) {
  const res = initDrillData(data, linkObj)
  return drilldownByPath(res, linkObj, path)
}
// 下转触发时返回的数据

/**
 * 根据指定的路径对数据进行下钻操作
 * @param {Array} data - 需要下钻的数据
 * @param {Object} linkObj - 链接对象，包含排序信息
 * @param {Array} path - 路径，用于指定下钻的维度
 * @returns {Array} - 过滤后的下钻数据
 */
function drilldownByPath(data, linkObj, path) {
  // 根据path下钻数据 path：Array
  const drillDownData = initDrillData(data, linkObj)
  let filterData = drillDownData.filter(item => {
    return item._parentId === path.join('-')
  })
  filterData = filterData.map(item => _.omit(item, ['_id', '_parentId']))
  if (filterData.length) {
    const { sortType, sortField } = linkObj
    if (sortField && sortType) {
      // 下钻排序
      filterData = filterData.sort((a, b) => {
        if (sortType === 'asc') {
          return a[sortField] - b[sortField]
        } else {
          return b[sortField] - a[sortField]
        }
      })
    }
  }
  return filterData
}
/**
 * 列表转表格数据类型
 * @param {Array} list
 * @returns
 */
const listToTable = (list = []) => {
  if (!Array.isArray(list)) {
    return list
  }
  // heads
  const h = []
  // bodys
  const b = []
  for (let index = 0; index < list.length; index++) {
    const obj = list[index]

    // 找到表头key
    if (index === 0) {
      for (const key in obj) {
        h.push(key)
      }
    }

    for (let i = 0; i < h.length; i++) {
      const key = h[i]

      if (!b[index]) {
        b[index] = []
      }

      if (Array.isArray(obj[key])) {
        b[index].push(listToTable(obj[key]))
      } else {
        b[index].push(obj[key])
      }
    }
  }

  return {
    h,
    b
  }
}

/**
 * 获取当时时间之前几天的时间
 */
function getDayAgo(day) {
  const today = new Date()
  return new Date(today.getTime() - day * 24 * 60 * 60 * 1000)
}
/**
 * 逻辑删除函数
 */
const deletefilter = function (filter) {
  if (!filter) {
    return {
      isDelete: {
        $ne: true
      }
    }
  } else {
    return {
      ...filter,
      isDelete: {
        $ne: true
      }
    }
  }
}
module.exports = {
  getCommonProjection,
  uuid,
  getNewComId,
  getResponseBody,
  getResponseSuccess,
  getResponseError,
  getResponseList,
  getDate,
  getQueryString,
  compareVersion,
  randomStr,
  formatString,
  getScreenshot,
  copyResource,
  addConfig,
  randomId,
  isEmpty,
  getExpdate,
  getFileName,
  checkSign,
  getLoggerModel,
  getCompsByLayer,
  initDrillData,
  drilldownByPath,
  initDrill,
  listToTable,
  getDayAgo,
  deletefilter
}
