// 定时更新缓存
const Subscription = require('egg').Subscription
const fs = require('fs-extra')
const path = require('path')
const { cipher, decipher } = require('../extend/crypto')
const { getResponseBody } = require('../extend/utils')
class UpdateRedis extends Subscription {
  static get schedule() {
    return {
      // cron: '59 59 23 * * *', //每天23:59:59s执行
      interval: '600s',
      type: 'worker',
      disable: false
    }
  }
  async subscribe() {
    const { ctx, config } = this
    const env = ctx.app.config.env
    let tasklist = await ctx.service.cacheservice.hgetall(`tasklist_${env}`)
    for (const key in tasklist) {
      if (Object.hasOwnProperty.call(tasklist, key)) {
        const tasklistObj = JSON.parse(tasklist[key])
        const bodyParam = tasklistObj.list
        const headerParam = tasklistObj.headerParam
        let resData = []
        bodyParam.forEach(value => {
          resData.push(
            new Promise(async (resolve, reject) => {
              try {
                const config = Object.assign(value, headerParam)
                const res = await ctx.service.datastorage.getData(config)
                resolve(res)
              } catch (e) {
                resolve(e)
              }
            })
          )
        })
        const ret = await Promise.all(resData)
        const returnValue = ret.map(item => {
          return getResponseBody(item)
        })
        delete tasklist[key]
        await ctx.service.cacheservice.hdel(`tasklist_${env}`, key)
        await ctx.service.cacheservice.set(key, returnValue, 60000)
      }
    }
  }
}

module.exports = UpdateRedis
