// 备份指定数据库表
const Subscription = require('egg').Subscription
const fs = require('fs')
const path = require('path')
const { MongoClient } = require('mongodb')
const moment = require('moment')
class BackUpDBJson extends Subscription {
  static get schedule() {
    return {
      cron: '00 00 01 * * *', // 每天凌晨1点定时
      // interval: '100s',
      type: 'worker',
      disable: false,
      env: ['prev']
    }
  }
  async subscribe() {
    try {
      const mongoConfig = this.ctx.app.config.mongoose
      // const user = mongoConfig.client.options.user
      // const password = mongoConfig.client.options.pass
      const host = mongoConfig.client.url.split(':')[1].split('//')[1]
      const port = mongoConfig.client.url.split(':')[2].split('/')[0]
      const database = mongoConfig.client.url.split('/')[3]
      // const authSource = mongoConfig.client.options.auth.authSource
      // const uri = `mongodb://${user}:${password}@${host}:${port}/${database}`
      const uri = `mongodb://${host}:${port}/${database}`
      // const client = new MongoClient(uri, { authSource: authSource })
      const client = new MongoClient(uri)
      await client.connect()
      const db = client.db(this.config.database)
      const treelist = [
        { name: 'fonts' },
        { name: 'systemfilters' },
        { name: 'comthemes' },
        { name: 'guides' },
        { name: 'themeschemes' },
        { name: 'resources' },
        { name: 'label' }
      ]
      for (const tree of treelist) {
        let collection
        if (tree.name === 'resources') {
          collection = await db
            .collection(tree.name)
            .find({ resourceLibraryCode: 'system' })
            .toArray() // 内容
        } else {
          collection = await db.collection(tree.name).find({}).toArray() // 内容
        }
        //文件夹路径
        const bakDir = path.resolve(
          this.config.resourcePath,
          `../node_modules/bak`
        )
        if (!fs.existsSync(bakDir)) {
          fs.mkdirSync(bakDir, { recursive: true })
        }
        let bakDirPath = path.join(bakDir, tree.name + '.json') //文件名
        this.ctx.logger.info('bakDirPath', bakDirPath)
        fs.writeFileSync(bakDirPath, JSON.stringify(collection))
      }
      client.close()
    } catch (e) {
      this.ctx.logger.info('bakupdb error:', e)
      console.error(e)
    }
  }
}

module.exports = BackUpDBJson
