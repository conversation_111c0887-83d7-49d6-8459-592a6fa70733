// 定时更新缓存
const Subscription = require('egg').Subscription
const { getResponseBody } = require('../extend/utils')
class cleanRedis extends Subscription {
  static get schedule() {
    return {
      // cron: '59 59 23 * * *', //每天23:59:59s执行
      interval: '24h',
        // cron: '0 0 * * *',
      type: 'worker',
      immediate: true
    }
  }
  async subscribe() {
    const { ctx } = this
    const currentTime = Date.now()
    // const minTime = currentTime - (1000 * 60)
    const minTime = currentTime - ( 1000 * 60 * 60 * 24 ) 
    const keys = await ctx.service.cacheservice.getKeys()
    if(keys) {
        keys.forEach(async (item)=>{
            const score = await ctx.service.cacheservice.zscore(item)
            if(score < minTime) {
                await ctx.service.cacheservice.del(item)
                await ctx.service.cacheservice.collectDel(item)
            }
        })
    }
    ctx.logger.info('执行定时任务')
}
    
}

module.exports = cleanRedis
