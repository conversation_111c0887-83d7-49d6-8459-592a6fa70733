const LabelModel = {
  id: {
    type: 'number',
    required: true,
    description: '标签id，创建时由前端生成'
  },
  name: {
    type: 'string',
    required: true,
    description: '标签名称，创建时由前端生成'
  },
  categoryId: {
    type: 'number',
    required: true,
    description: '分类id，创建时由前端生成'
  },
  categoryName: {
    type: 'string',
    required: true,
    description: '分类名称，创建时由前端生成'
  },
  compList: {
    type: 'array',
    required: false,
    default: [],
    itemType: 'ComponentLabelModel',
    description: '组件类型列表，表示哪些组件类型属于此标签'
  }
}

const ComponentLabelModel = {
  compId: {
    type: 'string',
    require: true,
    description: '组件类型'
  },
  compName: {
    type: 'string',
    require: true,
    description: '组件名称'
  }
}

const ComponentLabelContent = {
  categoryId: {
    type: 'number',
    required: true,
    description: '分类id，创建时由前端生成'
  },
  categoryName: {
    type: 'string',
    required: true,
    description: '分类名称，创建时由前端生成'
  },
  tags: {
    type: 'array',
    required: true,
    itemType: 'ComponentLabelContentItem',
    description: '标签列表'
  }
}

const ComponentLabelContentItem = {
  id: {
    type: 'number',
    required: true,
    description: '标签id，创建时由前端生成'
  },
  name: {
    type: 'string',
    required: true,
    description: '标签名称，创建时由前端生成'
  },
  checked: {
    type: 'boolean',
    require: true,
    description: '表示组件当前是否属于此标签'
  }
}

module.exports = {
  LabelModel,
  ComponentLabelModel,
  ComponentLabelContentItem,
  ComponentLabelContent,
  // 成功响应体
  LabelSuccessResponse: {
    success: {
      type: 'boolean',
      example: true,
      description: '成功'
    },
    code: { type: 'number', example: 200, description: '状态码' },
    message: { type: 'string', example: '请求成功', description: '消息' },
    data: {
      type: 'array',
      itemType: 'ComponentLabelContent',
      description: '返回数据'
    }
  },
  ComponentSuccessResponse: {
    success: {
      type: 'boolean',
      example: true,
      description: '成功'
    },
    code: { type: 'number', example: 200, description: '状态码' },
    message: { type: 'string', example: '请求成功', description: '消息' },
    data: {
      type: 'array',
      itemType: 'ComponentLabelModel',
      description: '返回数据'
    }
  }
}