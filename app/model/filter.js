module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const FilterSchema = new Schema({
    id: { type: Number, default: () => randomId() },
    screenId: { type: Number, required: true },
    name: { type: String, required: true },
    content: { type: String },
    callbackKeys: [String], // 回调字段,
    ctype: { type: String }, // 过滤器二级分类
    type: {
      type: String,
      enum: ['custom', 'system', 'template'],
      default: 'custom'
    }, // custom | system
    systemParams: [
      {
        source: {
          type: String,
          enum: ['field', 'callback', 'form', 'cascade'],
          default: 'field'
        },
        key: { type: String },
        name: { type: String },
        type: { type: String, enum: ['string', 'array'], default: 'string' },
        value: { type: Array },
        formType: { type: String },
        options: { type: Array }
      }
    ]
  })

  // 自动添加一个自增 id
  // FilterSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Filter'});

  return mongoose.model('Filter', FilterSchema)
}
