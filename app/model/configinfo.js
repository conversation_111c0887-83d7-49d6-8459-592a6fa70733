module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema

  const configinfo = new Schema(
    {
      showRenameDiloag: { type: Boolean, default: false }, // 是否弹出重命名弹窗
      showVoiceControl: { type: Boolean, default: false }, // 是否展示语音操控
      eventList: {   // 语音操控词
        open: { type: Array, default: ['看看', '看一下', '打开', '点击', '双击', '查看'] },
        focus: { type: Array, default: ['滑过'] },
        defocus: { type: Array, default: ['移除'] },
        closeWords: { type: Array, default: ['关闭', '关掉', '关掉内容', '关闭内容'] },
      }
    }
  )
  return mongoose.model('ConfigInfo', configinfo)
}
