module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { getDate } = require('../extend/utils')
  const { randomId } = require('../extend/utils')
  const UserSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() }, //随机id
      userId: { type: String, required: true }, //用户id
      entId: { type: String, default: '' }, //企业域id
      token: [],
      userName: { type: String, default: '' }, // 用户名
      sign: { type: String, default: '' },
      createdAt: { type: String }, // 创建时间
      updatedAt: { type: String }, // 更新时间
      isDmcLogin: { type: Number } //是否是dmc登录 1 dmc登录 2 local本地登录
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )
  // UserSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'User'});
  return mongoose.model('User', UserSchema)
}
