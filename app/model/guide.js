module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const { getDate } = require('../extend/utils')
  const GuideSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      name: { type: String }, //文件夹/文档名称
      parentID: { type: Number },
      sort: { type: Number },
      doc: [
        {
          id: { type: String },
          type: { type: Number, required: true }, // 文档分类 0，文档 1，视频,3 视频加文档
          title: { type: String }, // 文档或者视频标题
          mark: [], // 标签数组
          keyword: { type: String, default: '' },
          videoPath: { type: String }, // 视频端路径
          coverStyle: { type: Number }, // 封面图片存放位置
          content: { type: String }, // 文档内容
          userId: { type: String }, // 作者
          createdAt: { type: String }, // 创建日期
          updatedAt: { type: String } //更新日期
        }
      ]
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )
  return mongoose.model('guide', GuideSchema)
}
