module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const { randomId } = require('../extend/utils')
  const { getDate } = require('../extend/utils')
  const DraftSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      userId: { type: String }, // 作者
      docId: { type: String },
      content: { type: String }, // 草稿文档内容
      createdAt: { type: String }, // 创建日期
      updatedAt: { type: String } //更新日期
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )
  return mongoose.model('draft', DraftSchema)
}
