module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { getDate } = require('../extend/utils')
  const { randomId } = require('../extend/utils')
  const ResourceSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      userId: { type: String },
      resourceType: {
        type: String,
        enum: [
          'video',
          'picture',
          'tplVideo',
          'tplPicture',
          'themeVideo',
          'themePicture',
          'skeletonPicture',
          'js',
          'app',
          'document',
          'docfile',
          'icon'
        ],
        required: true
      },
      resourceLibrary: { type: String, required: true },
      resourceLibraryCode: {
        type: String,
        enum: ['system', 'personal', 'private'],
        required: true
      },
      resourceFolder: { type: String },
      resourceList: [
        {
          resourceId: { type: String },
          resourceUrl: { type: String },
          ecryptUrl: { type: String },
          resourceName: { type: String },
          description: { type: String, default: '' }
        }
      ],
      createdAt: { type: String },
      updatedAt: { type: String }
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )
  // ResourceSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Resource'});
  return mongoose.model('Resource', ResourceSchema)
}
