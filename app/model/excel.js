const { uuid } = require('../extend/utils');
module.exports = app => {
    const mongoose = app.mongoose;
    const Schema = mongoose.Schema;
  
    const SheetFieldSchema = new Schema({
      fieldName: { type: String },
      fieldType: { type: String, enum: ['string', 'number', 'date'] },
    });
  
    const SheetSchema = new Schema({
      sheetId: { type: String, required: true, unique: true, default: uuid() },
      sheetName: { type: String },
      sheetFields: [SheetFieldSchema],
      sheetCollection: { type: String },
    });
  
    const ExcelSchema = new Schema({
      excelId: { type: String, required: true, unique: true, default: uuid() },
      excelName: { type: String },
      createTime: { type: Date, default: Date.now },
      sheetList: [SheetSchema],
      id: { type: String }, // 这个是和datastorage关联的id 方便进行删除操作
    }, {
        // 设置 collection 名称为 excelBase
        collection: 'excelBase'
      });
  
    return mongoose.model('Excel', ExcelSchema);
  };