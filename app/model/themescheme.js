module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const ThemeSchemesSchema = new Schema({
    id: { type: Number, default: () => randomId() },
    name: { type: String, required: true },
    colorList: []
  })
  return mongoose.model('ThemeScheme', ThemeSchemesSchema)
}
