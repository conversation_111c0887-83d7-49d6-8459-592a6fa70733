module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const ProjectSchema = new Schema({
    id: { type: Number, default: () => randomId() },
    workspaceId: { type: Number, required: true },
    name: { type: String, required: true },
    type: { type: Number, default: 1, enum: [0, 1] } // 项目类型，0-默认；1-自定义 有可能是未分组是0自定义是1
  })
  // 自动添加一个自增 id
  // ProjectSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Project'});

  return mongoose.model('Project', ProjectSchema)
}
