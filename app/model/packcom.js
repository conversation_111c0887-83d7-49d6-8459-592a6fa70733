module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const PackcomSchema = new Schema({
    id: { type: Number, default: () => randomId() },
    ispack: { type: Boolean, default: false }, // 是否正在打包
    comjson: { type: String, required: true }
  })
  // 自动添加一个自增 id
  // ProjectSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Project'});

  return mongoose.model('Packcom', PackcomSchema)
}
