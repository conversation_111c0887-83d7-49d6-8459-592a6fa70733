module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const CalculatefieldSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      workspaceId: { type: Number, required: true },
      tbId: { type: String, required: true },
      fields: [
        {
          fid: { type: String, required: true }, //计算字段id，scfk_随机八位字符串
          name: { type: String, required: true }, // 计算字段名称
          formula: { type: String, required: true }, // 计算字段公式
          data_type: {
            type: String,
            enum: ['string', 'date', 'number'],
            default: 'string'
          }, // 字段类型
          field_type: { type: Number, enum: [1, 2], default: 1 }, //1计算字段, 2分组字段
          param: {} // 参数，留给分组字段
        }
      ]
    },
    { timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' } }
  )

  // 自动添加一个自增 id
  // CalculatefieldSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Calculatefield'});

  return mongoose.model('Calculatefield', CalculatefieldSchema)
}
