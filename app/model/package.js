module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const { getDate } = require('../extend/utils')
  const PackagesSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      name: { type: String, required: true },
      version: { type: String, required: true },
      alias: { type: String },
      icon: { type: String },
      type: { type: String },
      presetChildren: [String], // 预设子组件列表
      children: [String], // 支持的子组件名称数组
      width: { type: Number, default: 300 },
      height: { type: Number, default: 150 },
      config: { type: String },
      platform: { type: String, default: 'common' },
      path: { type: String, required: true },
      hidden: { type: Boolean, default: false },
      createdAt: { type: String },
      updatedAt: { type: String }
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )

  // 自动添加一个自增 id
  // PackagesSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Package'});

  return mongoose.model('Package', PackagesSchema)
}
