module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const { getDate } = require('../extend/utils')
  const LayergroupSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      screenId: { type: Number, required: true },
      layerTree: [{}],
      createdAt: { type: String }, // 创建日期
      updatedAt: { type: String } //更新日期
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )

  // 自动添加一个自增 id
  // FilterSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Filter'});

  return mongoose.model('Layergroup', LayergroupSchema)
}
