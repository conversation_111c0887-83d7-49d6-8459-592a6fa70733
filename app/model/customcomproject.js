module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const { getDate } = require('../extend/utils')
  const CustomcomprojectsSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      name: { type: String, default: '' },
      createdAt: { type: String },
      updatedAt: { type: String }
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )

  // 自动添加一个自增 id
  // CustomcomprojectsSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Customcomproject'});

  return mongoose.model('Customcomproject', CustomcomprojectsSchema)
}
