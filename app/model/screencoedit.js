/*
 * @Description: 大屏协同关联表
 * @Date: 2022-11-09 16:42:46
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */
module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const { getDate } = require('../extend/utils')
  const { randomId } = require('../extend/utils')
  const ScreencoeditSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      // 协同ID
      coeditId: { type: Number, required: true },
      // 创建人
      createUserId: { type: String, required: true },
      // 协同大屏ID
      coeditScreenId: { type: Number, required: true },
      // 协同用户ID
      coeditUserId: { type: String, required: true },
      // 协同角色
      coeditRole: {
        type: String, // viewers-查看者，collaborators-协作者
        enum: ['viewers', 'collaborators'],
        required: true
      },
      // 分组ID
      projectId: { type: Number, required: true },
      // 工作空间ID
      workspaceId: { type: Number, required: true },
      createdAt: { type: String }, // 创建时间
      updatedAt: { type: String } // 更新时间
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )

  return mongoose.model('Screencoedit', ScreencoeditSchema)
}
