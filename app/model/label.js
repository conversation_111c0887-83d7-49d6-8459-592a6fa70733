module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const LabelSchema = new Schema({
    id: { type: String, required: true, unique: true, index: true },
    name: { type: String, required: true },
    categoryId: { type: String, required: true },
    categoryName: { type: String, required: true },
    compList: { type: Array, default: [] }
  })
  return mongoose.model('Label', LabelSchema)
}