module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { getDate } = require('../extend/utils')
  const { randomId } = require('../extend/utils')
  const ScreentplSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      name: { type: String },
      type: {
        type: String,
        required: true,
        default: 'pc',
        enum: ['pc', 'mobile']
      },
      level: { type: Number, enum: [0, 1], default: 1 }, // 0-空白模板，1-其他模板
      screenId: { type: Number },
      description: { type: String },
      size: { type: String },
      tag: [],
      config: {
        width: { type: Number, default: 1920 },
        height: { type: Number, default: 1080 },
        backgroundColor: { type: String, default: 'rgba(0,0,0,0)' },
        backgroundImage: { type: String, default: '' },
        scaleType: {
          type: String,
          default: 'full_screen',
          enum: [
            'full_screen',
            'full_width',
            'full_height',
            'full_height_scroll',
            'no_scale'
          ]
        },
        gridSpace: { type: Number, default: 1 },
        thumbnail: { type: String, default: '' }
      },
      screenType: { type: String },
      createdAt: { type: String },
      updatedAt: { type: String }
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )

  // 自动添加一个自增 id
  // ScreentplSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Screentpl'});

  return mongoose.model('Screentpl', ScreentplSchema)
}
