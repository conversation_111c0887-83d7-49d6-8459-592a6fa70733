module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const { randomId } = require('../extend/utils')
  const moment = require('moment')
  const dateformat = require('../utils/dateformat')
  const HistoryScreenSchema = new Schema(
    {
      historyScreenId: { type: Number, default: () => randomId() },
      screenId: { type: Number }, // 主大屏的id，比如是某个大屏的历史版本
      connectScreenId: { type: Number }, // 关联大屏的id，真实的screenConfig从这里获取
      versionNo: { type: Number, default: () => 1 },
      versionName: { type: String },
      versionDesc: { type: String },
      createAt: { type: Date, default: moment().format('YYYY-MM-DD HH:mm:ss') }
    }
  )
  return mongoose.model('Historyscreen', HistoryScreenSchema)
}
  