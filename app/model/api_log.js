module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const ApiLogSchema = new Schema(
    {
      // 请求相关信息
      requestUrl: { type: String, required: true },
      userName: { type: String, required: true },
      screenName: { type: String, required: true },
      // 响应相关信息
      responseStatus: { type: Number, required: true },
      responseTime: { type: Number, required: true }, // 单位：毫秒
      // 错误信息
      error: { type: String },
      // 时间信息
      requestTime: { type: Date, default: Date.now },
    },
    {
      collection: 'api_logs',
    }
  );

  return mongoose.model('ApiLog', ApiLogSchema);
}; 