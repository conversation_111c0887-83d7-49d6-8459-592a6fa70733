const { DATA_TYPES } = require('../extend/constant')
const { getDate } = require('../extend/utils')
module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema

  const ComponentsSchema = new Schema(
    {
      id: { type: String, required: true, unique: true },
      screenId: { type: Number, required: true, index: true },
      pageId: { type: String },
      sceneId: { type: String },
      type: {
        type: String,
        required: true,
        enum: ['com', 'subCom', 'group'],
        default: 'com'
      },
      indicatorContainer: { type: Boolean, default: false }, // 默认显示
      comName: { type: String }, // 组件名称
      comType: { type: String }, // 组件类型
      alias: { type: String },
      orginName: { type: String },
      version: { type: String },
      parent: { type: String }, // 父组件id
      children: [String], // 子组件id数组
      icon: { type: String },
      show: { type: Boolean, default: true }, // 默认显示
      needCommon: { type: Boolean, default: false }, // 是否需要通用事件 默认true
      config: {}, // 配置解析后对象
      callbacks: [],
      ordinary: [], // 组件的公共事件传参
      controlConfig: { type: String }, // 原始控件配置项
      dataConfig: {
        // 数据配置
        fields: [
          {
            // 数据字段
            name: { type: String }, // 字段名
            type: {
              type: String,
              enum: ['string', 'number', 'date', 'boolean', 'object']
            }, // 字段类型
            description: { type: String }, // 字段描述
            optional: { type: Boolean, default: false } // 标示这个字段是否必填
          }
        ],
        fieldMapping: [
          // 字段映射
          {
            source: String,
            target: String,
            description: String,
            type: {
              type: String,
              enum: ['string', 'number', 'date', 'boolean', 'object']
            }, // 字段类型
            status: { type: Number, enum: [0, 1, 2], default: 0 } // 0:未匹配，1:失败，2:成功
          }
        ],
        dataResponse: {
          // 数据响应配置
          controlMode: { type: Boolean, default: false }, // 是否受控模式
          autoUpdate: {
            // 是否自动更新
            enable: { type: Boolean, default: false },
            time: { type: Number, default: 600 } // 自动更新时间间隔，单位：秒
          },
          dataExport: {
            enable: { type: Boolean, default: false },
          },
          notRequest: { type: Boolean, default: false },
          sourceType: {
            type: String,
            enum: DATA_TYPES.types,
            default: DATA_TYPES.default
          }, // 数据源类型
          source: DATA_TYPES.types.slice(1).reduce((obj, type) => {
            // Attention: 不同的数据源类型需要的配置字段不同，mongoose框架不支持往mongo中动态添加字段，因此不得已只能提前初始化好各个类型的表结构
            // 每新增一个数据源，需要维护这个列表
            switch (type) {
              case 'excel':
                obj[type] = {
                  data: {
                    sourceId: { type: 'string', default: '' },
                    sql: { type: 'string', default: '' },
                    tbId: { type: 'string', default: '' },
                    tbName: { type: 'string', default: '' },
                    folderId: { type: 'string', default: '' },
                    fields: {
                      dimension: { type: Array, default: [] },
                      numericalValue: { type: Array, default: [] }
                    },
                    where: {
                      enable: { type: Boolean, default: false },
                      whereCondition: [
                        {
                          composeType: { type: String },
                          field: { type: String, default: '' }, // 字段名
                          type: { type: String, default: '' }, // 字段类型
                          fid: { type: String, default: '' }, // 字段id
                          compare: { type: String }, // 比较
                          compareValue: { type: Array, default: [] } // 比较的值
                        }
                      ],
                      orderCondition: [
                        {
                          field: { type: String, default: '' }, // 字段名
                          type: { type: String, default: '' }, // 字段类型
                          fid: { type: String, default: '' }, // 字段id
                          orderBy: { type: String, enum: ['desc', 'asc'] },
                          calculation: { type: String, default: '' }
                        }
                      ]
                    },
                    isLimit: { type: Boolean, default: false },
                    limitNum: { type: Number, default: 150 }
                  }
                }
                break
              case 'mysql':
                obj[type] = {
                  data: {
                    sourceId: { type: 'string', default: '' },
                    advanced: { type: 'boolean', default: false },
                    sql: { type: 'string', default: '' },
                    tbId: { type: 'string', default: '' },
                    tbName: { type: 'string', default: '' },
                    folderId: { type: 'string', default: '' },
                    fields: {
                      dimension: { type: Array, default: [] },
                      numericalValue: { type: Array, default: [] }
                    },
                    where: {
                      enable: { type: Boolean, default: false },
                      whereCondition: [
                        {
                          composeType: { type: String },
                          field: { type: String, default: '' }, // 字段名
                          type: { type: String, default: '' }, // 字段类型
                          fid: { type: String, default: '' }, // 字段id
                          compare: { type: String }, // 比较
                          compareValue: { type: Array, default: [] } // 比较的值
                        }
                      ],
                      orderCondition: [
                        {
                          field: { type: String, default: '' }, // 字段名
                          type: { type: String, default: '' }, // 字段类型
                          fid: { type: String, default: '' }, // 字段id
                          orderBy: { type: String, enum: ['desc', 'asc'] },
                          calculation: { type: String, default: '' }
                        }
                      ]
                    },
                    isLimit: { type: Boolean, default: false },
                    limitNum: { type: Number, default: 150 }
                  }
                }
                break
              case 'dmdb':
                obj[type] = {
                  data: {
                    sourceId: { type: 'string', default: '' },
                    sql: { type: 'string', default: '' },
                    tbId: { type: 'string', default: '' },
                    tbName: { type: 'string', default: '' },
                    folderId: { type: 'string', default: '' },
                    fields: {
                      dimension: { type: Array, default: [] },
                      numericalValue: { type: Array, default: [] }
                    },
                    where: {
                      enable: { type: Boolean, default: false },
                      whereCondition: [
                        {
                          composeType: { type: String },
                          field: { type: String, default: '' }, // 字段名
                          type: { type: String, default: '' }, // 字段类型
                          fid: { type: String, default: '' }, // 字段id
                          compare: { type: String }, // 比较
                          compareValue: { type: Array, default: [] } // 比较的值
                        }
                      ],
                      orderCondition: [
                        {
                          field: { type: String, default: '' }, // 字段名
                          type: { type: String, default: '' }, // 字段类型
                          fid: { type: String, default: '' }, // 字段id
                          orderBy: { type: String, enum: ['desc', 'asc'] },
                          calculation: { type: String, default: '' }
                        }
                      ]
                    },
                    isLimit: { type: Boolean, default: false },
                    limitNum: { type: Number, default: 150 }
                  }
                }
                break
              case 'mongodb':
                obj[type] = {
                  data: {
                    sourceId: { type: 'string', default: '' },
                    tbName: { type: 'string', default: '' },
                    sql: { type: String, default: '' },
                    isLimit: { type: Boolean, default: false },
                    limitNum: { type: Number, default: 150 }
                  }
                }
                break
              case 'postgresql':
                obj[type] = {
                  data: {
                    sourceId: { type: 'string', default: '' },
                    advanced: { type: 'boolean', default: false },
                    sql: { type: 'string', default: '' },
                    tbId: { type: 'string', default: '' },
                    tbName: { type: 'string', default: '' },
                    folderId: { type: 'string', default: '' },
                    fields: {
                      dimension: { type: Array, default: [] },
                      numericalValue: { type: Array, default: [] }
                    },
                    where: {
                      enable: { type: Boolean, default: false },
                      whereCondition: [
                        {
                          composeType: { type: String },
                          field: { type: String, default: '' }, // 字段名
                          type: { type: String, default: '' }, // 字段类型
                          fid: { type: String, default: '' }, // 字段id
                          compare: { type: String }, // 比较
                          compareValue: { type: Array, default: [] } // 比较的值
                        }
                      ],
                      orderCondition: [
                        {
                          field: { type: String, default: '' }, // 字段名
                          type: { type: String, default: '' }, // 字段类型
                          fid: { type: String, default: '' }, // 字段id
                          orderBy: { type: String, enum: ['desc', 'asc'] },
                          calculation: { type: String, default: '' }
                        }
                      ]
                    },
                    isLimit: { type: Boolean, default: false },
                    limitNum: { type: Number, default: 150 }
                  }
                }
                break
                case 'gbase8s':
                  obj[type] = {
                    data: {
                      sourceId: { type: 'string', default: '' },
                      advanced: { type: 'boolean', default: false },
                      sql: { type: 'string', default: '' },
                      tbId: { type: 'string', default: '' },
                      tbName: { type: 'string', default: '' },
                      folderId: { type: 'string', default: '' },
                      fields: {
                        dimension: { type: Array, default: [] },
                        numericalValue: { type: Array, default: [] }
                      },
                      where: {
                        enable: { type: Boolean, default: false },
                        whereCondition: [
                          {
                            composeType: { type: String },
                            field: { type: String, default: '' }, // 字段名
                            type: { type: String, default: '' }, // 字段类型
                            fid: { type: String, default: '' }, // 字段id
                            compare: { type: String }, // 比较
                            compareValue: { type: Array, default: [] } // 比较的值
                          }
                        ],
                        orderCondition: [
                          {
                            field: { type: String, default: '' }, // 字段名
                            type: { type: String, default: '' }, // 字段类型
                            fid: { type: String, default: '' }, // 字段id
                            orderBy: { type: String, enum: ['desc', 'asc'] },
                            calculation: { type: String, default: '' }
                          }
                        ]
                      },
                      isLimit: { type: Boolean, default: false },
                      limitNum: { type: Number, default: 150 }
                    }
                  }
                  break
                case 'highgodb':
                  obj[type] = {
                    data: {
                      sourceId: { type: 'string', default: '' },
                      advanced: { type: 'boolean', default: false },
                      sql: { type: 'string', default: '' },
                      tbId: { type: 'string', default: '' },
                      tbName: { type: 'string', default: '' },
                      folderId: { type: 'string', default: '' },
                      fields: {
                        dimension: { type: Array, default: [] },
                        numericalValue: { type: Array, default: [] }
                      },
                      where: {
                        enable: { type: Boolean, default: false },
                        whereCondition: [
                          {
                            composeType: { type: String },
                            field: { type: String, default: '' }, // 字段名
                            type: { type: String, default: '' }, // 字段类型
                            fid: { type: String, default: '' }, // 字段id
                            compare: { type: String }, // 比较
                            compareValue: { type: Array, default: [] } // 比较的值
                          }
                        ],
                        orderCondition: [
                          {
                            field: { type: String, default: '' }, // 字段名
                            type: { type: String, default: '' }, // 字段类型
                            fid: { type: String, default: '' }, // 字段id
                            orderBy: { type: String, enum: ['desc', 'asc'] },
                            calculation: { type: String, default: '' }
                          }
                        ]
                      },
                      isLimit: { type: Boolean, default: false },
                      limitNum: { type: Number, default: 150 }
                    }
                  }
                  break
              case 'oracle':
                obj[type] = {
                  data: {
                    sourceId: { type: 'string', default: '' },
                    advanced: { type: 'boolean', default: false },
                    tbName: { type: 'string', default: '' },
                    fields: {
                      dimension: { type: Array, default: [] },
                      numericalValue: { type: Array, default: [] }
                    },
                    where: {
                      enable: { type: Boolean, default: false },
                      whereCondition: [
                        {
                          composeType: { type: String },
                          field: { type: String, default: '' }, // 字段名
                          type: { type: String, default: '' }, // 字段类型
                          compare: { type: String }, // 比较
                          compareValue: { type: Array, default: [] } // 比较的值
                        }
                      ],
                      orderCondition: [
                        {
                          field: { type: String, default: '' }, // 字段名
                          type: { type: String, default: '' }, // 字段类型
                          orderBy: { type: String, enum: ['desc', 'asc'] },
                          calculation: { type: String, default: '' },
                          type: { type: String, default: '' }
                        }
                      ]
                    },
                    sql: { type: String, default: '' }, // 高级模式的sql
                    isLimit: { type: Boolean, default: false },
                    limitNum: { type: Number, default: 150 }
                  }
                }
                break
              case 'api':
                obj[type] = {
                  data: {
                    sourceId: { type: 'string', default: '' },
                    baseUrl: { type: 'string', default: '' },
                    method: { type: 'string', default: '' },
                    headers: { type: 'string', default: '{}' },
                    path: { type: 'string', default: '' },
                    params: { type: 'string', default: '' },
                    body: { type: 'string', default: '{}' },
                    reqFromBack: { type: Boolean, default: false },
                    needCookie: { type: Boolean, default: false },
                    isLimit: { type: Boolean, default: false },
                    limitNum: { type: Number, default: 150 },
                    func: {
                      type: 'string',
                      default:
                        '// 可在此处生成自定义的动态js参数\n// 此处可使用callbackArgs.xxx引用回调参数\n// 必须返回对象形式数据，即 return {...}\n// 动态参数可在header/body/参数里通过 "${_var.xxx}" 引用\n\nreturn {}'
                    }
                  }
                }
                break
              case 'dmc':
                obj[type] = {
                  data: {
                    sourceId: { type: 'string', default: '' },
                    tbId: { type: 'string', default: '' },
                    tbName: { type: 'string', default: '' },
                    folderId: { type: 'string', default: '' },
                    fields: {
                      dimension: { type: Array, default: [] }, //维度字段
                      numericalValue: { type: Array, default: [] } //数值字段
                    },
                    where: {
                      enable: { type: Boolean, default: false },
                      whereCondition: [
                        {
                          composeType: { type: String }, // 条件组合方式
                          field: { type: String, default: '' }, // 字段名
                          type: { type: String, default: '' }, // 字段类型
                          fid: { type: String, default: '' }, // 字段id
                          compare: { type: String }, // 比较
                          compareValue: { type: Array, default: [] } // 比较的值
                        }
                      ],
                      orderCondition: [
                        {
                          field: { type: String, default: '' }, // 字段名
                          type: { type: String, default: '' }, // 字段类型
                          fid: { type: String, default: '' }, // 字段id
                          orderBy: { type: String, enum: ['desc', 'asc'] },
                          calculation: { type: String, default: '' }
                        }
                      ]
                    },
                    isLimit: { type: Boolean, default: false },
                    limitNum: { type: Number, default: 150 }
                  }
                }
                break
                case 'bdp':
                  obj[type] = {
                    data: {
                      sourceId: { type: 'string', default: '' },
                      tbId: { type: 'string', default: '' },
                      tbName: { type: 'string', default: '' },
                      folderId: { type: 'string', default: '' },
                      fields: {
                        dimension: { type: Array, default: [] }, //维度字段
                        numericalValue: { type: Array, default: [] } //数值字段
                      },
                      where: {
                        enable: { type: Boolean, default: false },
                        whereCondition: [
                          {
                            composeType: { type: String }, // 条件组合方式
                            field: { type: String, default: '' }, // 字段名
                            type: { type: String, default: '' }, // 字段类型
                            fid: { type: String, default: '' }, // 字段id
                            compare: { type: String }, // 比较
                            compareValue: { type: Array, default: [] } // 比较的值
                          }
                        ],
                        orderCondition: [
                          {
                            field: { type: String, default: '' }, // 字段名
                            type: { type: String, default: '' }, // 字段类型
                            fid: { type: String, default: '' }, // 字段id
                            orderBy: { type: String, enum: ['desc', 'asc'] },
                            calculation: { type: String, default: '' }
                          }
                        ]
                      },
                      isLimit: { type: Boolean, default: false },
                      limitNum: { type: Number, default: 150 }
                    }
                  }
                  break
              case 'websocket':
                obj[type] = {
                  data: {
                    sourceId: { type: String, default: '' }, // 源数据
                    duration: { type: Number, default: 10 }, // websocket所选时间断 如10
                    durationType: { type: String, default: 'minute' } // 时间类型 如 时、分、秒
                  }
                }
                break
              case 'dashboard':
                obj[type] = {
                  data: {
                    sourceId: { type: String, default: '' }, // 源数据
                    name: { type: String, default: '' },
                    isLimit: { type: Boolean, default: false },
                    limitNum: { type: Number, default: 150 }
                  }
                }
                break
              case 'datacontainer':
                obj[type] = {
                  data: {
                    dataContainerComId: { type: String, default: '' }, // 关联的数据容器组件id
                    isLimit: { type: Boolean, default: false },
                    limitNum: { type: Number, default: 150 }
                  }
                }
                break
              default:
                obj[type] = {
                  data: {
                    sourceId: { type: 'string', default: '' },
                    isLimit: { type: Boolean, default: false },
                    limitNum: { type: Number, default: 150 }
                  }
                }
            }
            return obj
          }, {}),
          filters: {
            // 数据过滤器
            enable: { type: Boolean, default: true }, // 是否启用
            list: [
              {
                id: { type: Number }, // 过滤器id
                name: { type: String },
                enable: { type: Boolean, default: true } // 是否启用
              }
            ]
          },
          // tips: {
          //   open: { type: Boolean, default: false },
          //   type: { type: String, default: 'empty' },
          //   field: '',
          //   condition: { type: String, default: '==' },
          //   value: { type: String, default: '0' },
          //   info: { type: String, default: '暂无数据' },
          //   background: { type: String, default: '#fff' },
          //   color: { type: String, default: '#999' }
          // },
          tips: {
            open: { type: Boolean, default: false },
            type: { type: String, default: 'empty' },
            field: '',
            condition: { type: String, default: '==' },
            value: { type: String, default: '0' },
            info: { type: String, default: '暂无数据' },
            background: { type: String, default: '#fff' },
            color: { type: String, default: '#999' },
            conditions: [
              {
                type: { type: String, default: 'empty' },
                field: { type: String, default: '' },
                condition: { type: String, default: '==' },
                value: { type: String, default: '0' },
                info: { type: String, default: '暂无数据' },
                font: {},
                background: {}
              }
            ]
          }
        }
      },
      interactionConfig: {
        // 交互配置
        callbackParams: [
          // 回调参数数组
          {
            id: { type: String }, // id 格式：'callback_2dub9jeezd'
            name: { type: String, default: '回调' },
            fieldValue: { type: String }, // 字段值
            variableName: { type: String } // 变量名
          }
        ],

        linkAge: [
          // 联动配置
          {
            triggerId: { type: String }, // 触发动作的组件id
            immediate: { type: Boolean, default: false }, // 是否是立即触发的事件
            sourceModule: {
              type: String,
              enum: ['simpleModule', 'seniorModule'],
              default: 'simpleModule'
            }, //精简还是高级模式
            trigger: { type: String }, // 事件动作
            source: {
              type: String,
              enum: ['static', 'server'],
              default: 'static'
            }, // 表示联动数据来源 前端/后端
            composeType: { type: String }, // 后端条件组合方式
            linkageComList: [
              {
                componentId: { type: String },
                componentName: { type: String }
              }
            ],
            linkMode: {
              type: String,
              enum: ['single', 'multiple'],
              default: 'multiple'
            }, // 联动模式
            coms: [
              {
                id: { type: String },
                name: { type: String }
              }
            ], // 组合联动的组件
            linkageConfig: [
              {
                id: { type: String }, // id 格式：'callback_2dub9jeezd'
                name: { type: String, default: '' },
                fieldType: { type: String, default: '' }, // 字段类型
                fieldValue: { type: String }, // 字段值
                componentList: [
                  {
                    compare: { type: String, default: 'equal' }, // 类型
                    sourceType: { type: String, enum: ['static', 'server'] }, // 表示联动数据来源 前端/后端
                    componentId: { type: String },
                    componentName: { type: String },
                    fieldValue: { type: String },
                    fieldId: { type: String },
                    fieldType: { type: String },
                    fieldRequestType: { type: String } // 请求类型
                  }
                ]
              }
            ]
          }
        ],
        // 下钻设置
        drillDown: [
          {
            triggerId: { type: String }, // 触发动作的组件id
            trigger: { type: String },
            fieldValue: { type: String },
            links: [
              {
                id: { type: String },
                fieldValue: { type: String }
              }
            ],
            sums: [], // 求和字段
            drillType: { type: String, default: 'static' },
            linkType: { type: String },
            parentField: { type: String },
            parentFieldVal: { type: String },
            openType: { type: String, default: 'default' },
            sortField: { type: String, default: '' },
            sortType: { type: String, default: '' },
            width: { type: Number },
            height: { type: Number }
          }
        ],
        events: [
          // 自定义事件
          {
            id: { type: String }, // id 格式：:"event_nlk1jh96m"
            immediate: { type: Boolean, default: false }, // 是否是立即触发的事件
            name: { type: String, default: '事件' }, // 名称
            trigger: { type: String }, // 事件类型
            triggerId: { type: String }, // 触发事件的组件 id
            conditionType: {
              type: String,
              default: 'all',
              enum: ['all', 'one']
            }, // 条件类型 all-所有，one-单个
            conditions: [
              // 条件
              {
                id: { type: String }, // id 格式：condition_hdjlpsx5om
                name: { type: String, default: '条件' }, // 名称
                type: {
                  type: String,
                  default: 'field',
                  enum: ['field', 'custom']
                }, // 类型
                field: { type: String }, // 字段名
                compare: {
                  type: String,
                  default: '==',
                  enum: [
                    '==',
                    '!=',
                    '<',
                    '>',
                    '<=',
                    '>=',
                    'include',
                    'exclude',
                    '>(date)',
                    '<(date)',
                    '>=(date)',
                    '<=(date)',
                    'contain',
                    'notContain',
                    'matchOnStart',
                    'matchOnEnd',
                    'notNull',
                    'null'
                  ]
                }, // 比较符
                expected: { type: String, default: '' }, // 预期值
                code: { type: String, default: '' } // 代码
              }
            ],
            fieldMap: [
              {
                // 事件和行为参数映射
                description: { type: String }, // 字段描述
                source: { type: String }, // actions 参数
                target: { type: String } // 事件参数
              }
            ],
            scripts: [
              {
                name: { type: String, required: true, default: '' },
                code: { type: String, required: true, default: '' }
              }
            ], // 自定义事件脚本
            componentId: [String], // 接收事件的组件 id
            comConfig: {
              treeData: {},
              configObj: {},
              show: { type: Boolean, default: true },
              attr: {},
              updateConfig: {}
            }, // 接受组件的配置信息
            componentScope: {
              type: String,
              default: 'current',
              enum: ['current', 'global']
            }, // 组件类型，当前 or 全局。注意：已弃用!!!!!!!!!!
            action: { type: String }, // 动作
            isConnect: { type: Boolean, default: false }, // 是否关联
            connectCompId: [String], // 关联的组件id数组
            jumpInfo: {
              jumpIsLocal: { type: Boolean, default: false }, // 页内跳转
              jumpUrl: { type: String }, // 跳转链接
              paramsList: [['']],
              mappingName: ['']
            },
            eventAnimation: [{}], // 动作动画
            executionOrder: {
              type: String,
              default: 'await',
              enum: ['current', 'global', 'await']
            },
            sendReqErrorTips: { type: Boolean, default: false },
            eventSend: [
              {
                source: { type: String },
                type: { type: String },
                reqId: { type: String },
                trigger: { type: String },
                data: {
                  api: {}
                },
                componentId: [],
                action: { type: String }, // 动作
                jumpInfo: {
                  jumpIsLocal: { type: Boolean, default: false },
                  jumpUrl: { type: String },
                  paramsList: [['']],
                  mappingName: ['']
                },
                scripts: [],
                alert: {
                  // 弹窗提示
                  content: { type: String, default: '' },
                  title: { type: String, default: '' },
                  buttonText: { type: String, default: '' }
                }
              }
            ], // 组件自定义请求
            sendReqAllAfter: {
              componentId: [],
              action: { type: String }, // 动作
              jumpInfo: {
                jumpIsLocal: { type: Boolean, default: false },
                jumpUrl: { type: String },
                paramsList: [['']],
                mappingName: ['']
              },
              scripts: [],
              alert: {
                // 弹窗提示
                content: { type: String, default: '' },
                title: { type: String, default: '' },
                buttonText: { type: String, default: '' }
              }
            },
            locatedCompId: { type: String } // 锚点跳转到组件 id
          }
        ]
      },
      events: [
        // 组件原始事件配置
        {
          name: { type: String }, // 事件名
          description: { type: String }, // 事件描述
          immediate: { type: Boolean, default: false }, // 是否是立即触发的事件
          default: { type: Boolean, default: false }, // 是否是默认事件
          params: [
            // 事件参数说明
            {
              name: { type: String }, // 参数名
              type: {
                type: String,
                default: 'string',
                enum: ['string', 'number', 'bool', 'date', 'object']
              }, // 参数类型
              description: { type: String } // 参数描述
            }
          ]
        }
      ],
      actions: [
        // 组件原始行为（方法）配置
        {
          name: { type: String }, // 方法名
          description: { type: String }, // 方法描述
          params: [
            // 方法接收参数说明
            {
              name: { type: String }, // 参数名
              type: {
                type: String,
                default: 'string',
                enum: ['string', 'number', 'bool', 'date', 'object']
              }, // 参数类型
              description: { type: String } // 参数描述
            }
          ]
        }
      ],
      animation: {
        // 动画配置
        animationIn: [{}], // 入场动画配置
        animationKeep: [{}], // 进行中
        animationOut: [{}], // 出场动画配置
        animationEvent: [{}] // 发生事件时的动画
      },
      staticData: [{}], // 静态数据
      requirePath: { type: String }, // 引用路径
      attr: {
        w: { type: Number, default: 300 },
        h: { type: Number, default: 150 },
        x: { type: Number, default: 0 },
        y: { type: Number, default: 0 },
        deg: { type: Number, default: 0 }, // 旋转角度
        opacity: { type: Number, default: 1 }, // 透明度
        lock: { type: Boolean, default: false }, // 是否锁定
        enable3D: { type: Boolean, default: false }, // 是否开启 3D 变换
        perspective: { type: Number, default: 800 }, // 3d透视距离
        flipH: { type: Number, default: 0 }, // 水平翻转
        flipV: { type: Number, default: 45 }, // 垂直翻转
        cursor: { type: String, default: 'default' } // 鼠标样式
      },
      other: {
        themeUrl: { type: String },
        isScreentpl: { type: Boolean, default: false },
        isTheme: { type: Boolean, default: false },
        configType: { type: String, default: 'advanced' },
        comThemeId: { type: Number, default: 0 },
        isDmcIndicator: { type: Boolean, default: false }
      },
      isDelete: { type: Boolean, default: false },
      packageCreateDate: { type: String },
      createdAt: { type: String },
      updatedAt: { type: String }
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )

  return mongoose.model('Component', ComponentsSchema)
}
