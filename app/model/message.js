/*
 * @Description: 消息
 * @Date: 2022-11-07 10:28:13
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */
module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId, getDate } = require('../extend/utils')
  const MessageSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      // 收消息的用户id
      userId: { type: String, required: true },
      // 消息类型
      type: {
        type: String,
        required: true,
        // usershare-复制共享消息，common-普通消息，screenCoedit-协同编辑消息
        enum: ['usershare', 'common', 'screenCoedit'],
        default: 'common'
      },
      content: {
        // 用户分享复制大屏
        usershare: {
          usershareId: { type: Number }
        },
        common: {
          commonContent: { type: String, default: '' }
        },
        // 协同编辑
        screenCoedit: {
          // 协同ID
          coeditId: { type: Number },
          // 动作add-添加到协同，update-更新协同角色，remove-移除协同
          action: {
            type: String,
            enum: ['add', 'update', 'remove'],
            default: 'add'
          },
          // 协同大屏ID
          coeditScreenId: { type: Number },
          // 创建者ID
          createUserId: { type: String, default: '' },
          // 协作用户ID
          coeditUserId: { type: String, default: '' },
          // 协同编辑角色
          coeditRole: {
            type: String,
            // viewers-查看者，collaborators-协作者
            enum: ['viewers', 'collaborators'],
            default: 'viewers'
          }
        }
      },
      isRead: { type: Boolean, default: false },
      createdAt: { type: String },
      updatedAt: { type: String }
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )

  // 自动添加一个自增 id
  // MessageSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Message'});

  return mongoose.model('Message', MessageSchema)
}
