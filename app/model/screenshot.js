module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const ScreenshotSchema = new Schema({
    id: { type: Number, default: () => randomId() },
    relationId: { type: String, required: true },
    screenshotUrl: { type: String, required: true }
  })
  // ScreenshotSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Screenshot'});
  return mongoose.model('Screenshot', ScreenshotSchema)
}
