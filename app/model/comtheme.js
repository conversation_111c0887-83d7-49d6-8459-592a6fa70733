module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const { getDate } = require('../extend/utils')
  const ComThemeSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      alias: { type: String, required: true },
      comType: { type: String, required: true },
      themeType: {
        type: String,
        required: true,
        default: 'style',
        enum: ['style', 'function']
      },
      themeSchemeId: { type: Number },
      config: {},
      name: { type: String, required: true },
      icon: { type: String },
      width: { type: String },
      height: { type: String },
      isDefault: { type: Boolean, default: false }, // 是否是组件默认主题
      createdAt: { type: String },
      updatedAt: { type: String }
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )

  // 自动添加一个自增 id
  // ComThemeSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'ComTheme'});

  return mongoose.model('ComTheme', ComThemeSchema)
}
