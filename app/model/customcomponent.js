module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const { getDate } = require('../extend/utils')
  const CustomcomponentsSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      customcomprojectId: { type: Number },
      customIcon: { type: String },
      compConfig: {
        name: { type: String, required: true },
        version: { type: String, required: true },
        chartConfig: {
          cn_name: { type: String, required: true },
          type: { type: String, required: true },
          icon: { type: String, default: 'custom' },
          show: { type: Boolean, default: false },
          width: { type: Number },
          height: { type: Number },
          children: {},
          parent: {},
          data: {
            source: [],
            fields: []
          },
          config: {},
          callbacks: []
        }
      },
      instanceCode: {
        HTML: {
          language: { type: String, default: '' },
          content: { type: String, default: '' },
          resources: []
        },
        CSS: {
          language: { type: String, default: '' },
          content: { type: String, default: '' },
          resources: []
        },
        JavaScript: {
          language: { type: String, default: '' },
          content: { type: String, default: '' },
          initContent: { type: String, default: '' },
          resizeContent: { type: String, default: '' },
          resources: []
        }
      },
      editorWidth: { type: Number },
      echartsCompId: { type: String, default: '' },
      iframeWidth: { type: Number },
      currentTemp: { type: String, default: '' },
      createdAt: { type: String },
      updatedAt: { type: String }
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )

  // 自动添加一个自增 id
  // CustomcomponentsSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Customcomponent'});

  return mongoose.model('Customcomponent', CustomcomponentsSchema)
}
