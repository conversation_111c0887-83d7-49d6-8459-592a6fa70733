module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { getDate } = require('../extend/utils')
  const { randomId } = require('../extend/utils')
  const IndicatorDmcSchema = new Schema(
    {
      id: { type: String, default: () => String(randomId()) },
      indicatorName: { type: String }, // 关联的指标平台指标名字
      indicatorDmcId: { type: String }, // 关联的指标平台指标id
      name: { type: String },
      componentId: { type: String },
      config: {
        backgroundColor: { type: String, default: 'rgba(0,0,0,0)' },
        backgroundImage: { type: String, default: '' }
      },
      userId: { type: String },
      userName: { type: String },
      createdAt: { type: String },
      updatedAt: { type: String }
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )
  return mongoose.model('IndicatorDmc', IndicatorDmcSchema)
}
