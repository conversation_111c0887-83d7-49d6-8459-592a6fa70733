const { DATA_TYPES } = require('../extend/constant')
const { getDate } = require('../extend/utils')
const { randomId } = require('../extend/utils')
module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const VoicecontrolSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() }, //随机id
      screenId: { type: String, required: true }, // 大屏id
      comId: { type: String, required: true, index: true }, //组件id
      comName: { type: String, required: true }, // 组件名称
      alias: { type: String }, // 组件别名
      aliasList: [], // 别名
      eventsConfig: [
        {
          relatedEvents: '', // 关联事件
          eventWords: [], // 事件词
          type: {
            type: String,
            enum: ['manual', 'datasource', 'ai']
          }, //词源类型
          field: '', // 数据源字段
          valueList: [], // 数据源的值
          aiValueList: [],
          params: '', //事件参数
          isComponentWord: { type: Boolean, default: false } //是否绑定为组件词
        }
      ],
      isDelete: { type: Boolean, default: false },
      createdAt: { type: String },
      updatedAt: { type: String }
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )

  return mongoose.model('Voicecontrol', VoicecontrolSchema)
}
