module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const UserBehaviorSchema = new Schema(
    {
      operateType: { type: String, default: '' }, // 操作类型
      details: { type: String, default: '' }, // 操作内容
      status: { type: Number }, // 状态
      user_name: { type: String, default: '' }, // 操作人
      c_time: { type: Date }, // 时间
      user_id: { type: String }, // 用户id
      organization: { type: String }, // 机构名
      organizationID: { type: String }, // 机构代码
      group_id: { type: String } // 组织id
    }
  )

  return mongoose.model('UserBehavior', UserBehaviorSchema)
}