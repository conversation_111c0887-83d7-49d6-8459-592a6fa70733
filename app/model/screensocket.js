module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const ScreensocketSchema = new Schema({
    id: { type: Number, default: () => randomId() },
    screenId: { type: Number, required: true },
    password: { type: String, required: true },
    isConnect: { type: Boolean, default: false },
    isWaitConnect: { type: Boolean, default: false },
    acceptSocketid: {},
    sendSocketid: {},
    waitSocketid: {},
    socketidList: []
  })

  // 自动添加一个自增 id
  // ScreensocketSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Screensocket'});

  return mongoose.model('Screensocket', ScreensocketSchema)
}
