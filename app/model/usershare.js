module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const UsershareSchema = new Schema({
    id: { type: Number, default: () => randomId() },
    userId: { type: String, required: true },
    userName: { type: String, required: true },
    shareUserId: { type: String, required: true },
    screenId: { type: Number, required: true },
    screenName: { type: String, required: true },
    isAccept: { type: Boolean, default: false }
  })

  // 自动添加一个自增 id
  // UsershareSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Usershare'});

  return mongoose.model('Usershare', UsershareSchema)
}
