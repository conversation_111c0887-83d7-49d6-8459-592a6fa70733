module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const { getDate } = require('../extend/utils')
  const FontSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      userId: { type: String },
      fontName: { type: String, required: true },
      fontUrl: { type: String },
      size: { type: String },
      enable: { type: Boolean, default: true }, // 是否启用
      systemFont: { type: Boolean, default: false }, // 是否为系统字体
      description: { type: String, default: '' },
      createdAt: { type: String },
      updatedAt: { type: String }
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )
  // FontSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Font'});
  return mongoose.model('Font', FontSchema)
}
