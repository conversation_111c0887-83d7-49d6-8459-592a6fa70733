module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { randomId } = require('../extend/utils')
  const TemplatesSchema = new Schema({
    id: { type: Number, default: () => randomId() },
    name: { type: String, required: true },
    cn_name: { type: String, required: true },
    version: { type: String, required: true, default: '1.0.0' }
  })

  // 自动添加一个自增 id
  // TemplatesSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Template'});

  return mongoose.model('Template', TemplatesSchema)
}
