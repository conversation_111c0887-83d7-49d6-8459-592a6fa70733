module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { DATA_TYPES } = require('../extend/constant')
  const { randomId } = require('../extend/utils')
  const DataStorageSchema = new Schema({
    id: { type: Number, default: () => randomId() },
    screenId: { type: Number },
    workspaceId: { type: Number, required: true },
    type: {
      type: String,
      enum: DATA_TYPES.types,
      required: true,
      default: DATA_TYPES.default
    }, // 数据源类型
    name: { type: String, required: true },
    description: { type: String },
    config: {}, // 其他配置信息
    socketAddress: { type: String } //特殊字段 websocket 添加地址
  })

  // 自动添加一个自增 id
  // DataStorageSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'DataStorage'});

  return mongoose.model('DataStorage', DataStorageSchema)
}
