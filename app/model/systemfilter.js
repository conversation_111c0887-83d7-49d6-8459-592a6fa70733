module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const { randomId } = require('../extend/utils')
  const { getDate } = require('../extend/utils')
  const SystemFilterSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      name: { type: String, required: true },
      type: { type: String },
      content: { type: String },
      description: { type: String, default: '' },
      params: [
        {
          source: {
            type: String,
            enum: ['field', 'callback', 'cascade', 'form'],
            default: 'field'
          },
          key: { type: String },
          name: { type: String },
          type: { type: String, enum: ['string', 'array'], default: 'string' },
          formType: { type: String },
          options: { type: Array }
        }
      ],
      createdAt: { type: String }, // 创建时间
      updatedAt: { type: String } // 更新时间
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )

  return mongoose.model('SystemFilter', SystemFilterSchema)
}
