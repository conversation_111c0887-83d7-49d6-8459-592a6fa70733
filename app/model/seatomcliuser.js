module.exports = app => {
    const mongoose = app.mongoose
    const Schema = mongoose.Schema
    const { getDate } = require('../extend/utils')
    const { randomId } = require('../extend/utils')
    const SeatomCliUserSchema = new Schema(
      {
        id: { type: Number, default: () => randomId() }, //随机id
        userId: { type: String, required: true }, //用户id
        userName: { type: String, default: '' }, // 用户名
        passWord: { type: String, default: '' }, // 密码
        role: { type: Number, default: 2 }, //用户角色: 0: 超级管理员, 1: 管理员, 2: 普通开发者
        token: [],
      },
      {
        timestamps: { currentTime: () => getDate() }
      }
    )
    // UserSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'User'});
    return mongoose.model('SeatomCliUser', SeatomCliUserSchema)
  }
  