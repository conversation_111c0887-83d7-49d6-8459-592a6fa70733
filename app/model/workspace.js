module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { getDate } = require('../extend/utils')
  const { randomId } = require('../extend/utils')
  const WorkSpaceSchema = new Schema(
    {
      id: { type: Number, default: () => randomId() },
      name: { type: String, required: true },
      type: {
        // 工作空间类型，0-默认；1-自定义
        type: Number,
        default: 1,
        enum: [0, 1]
      },
      screenCount: {
        type: Number,
        default: 100
      },
      userId: { type: String },
      createdAt: { type: String },
      updatedAt: { type: String }
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )

  // 自动添加一个自增 id
  // WorkSpaceSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Workspace'});

  return mongoose.model('Workspace', WorkSpaceSchema)
}
