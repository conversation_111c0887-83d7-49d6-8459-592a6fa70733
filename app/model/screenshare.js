module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema

  const ScreenShareSchema = new Schema({
    screenId: { type: Number, required: true }, // 大屏id
    shareToken: { type: String }, // 分享token
    isPublic: { type: Boolean, default: false }, // 是否发布
    shareUrl: { type: String }, // 分享链接
    needPassword: { type: Boolean, default: false }, // 是否需要密码
    needLoading: { type: Boolean, default: true }, // 是否需要loading
    needAuth: { type: Boolean, default: false }, // 是否需要密码
    sharePassword: { type: String, default: '' }, // 分享密码
    enableCache: { type: Boolean, default: false }, // 是否开启数据缓存
    renderType: { type: String, default: 'static' } // 渲染方式
  })

  return mongoose.model('ScreenShare', ScreenShareSchema)
}
