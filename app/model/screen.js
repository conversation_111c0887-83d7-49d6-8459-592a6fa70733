module.exports = app => {
  const mongoose = app.mongoose
  const Schema = mongoose.Schema
  const AutoIncrement = require('../extend/auto-increment')
  const { getDate, uuid } = require('../extend/utils')
  const { randomId } = require('../extend/utils')
  const ScreenSchema = new Schema(
    {
      id: { type: Number, default: () => randomId(), unique: true },
      coeditId: { type: Number }, // 协同编辑ID
      parentId: { type: String }, // 关联的大屏id，处理之前弹窗的问题 后续当作面板的父大屏的id
      relationCompId: { type: String }, // 关联的组件id，处理新的弹窗问题
      workspaceId: { type: Number, required: true, index: true },
      projectId: { type: Number, required: true, index: true },
      name: { type: String, required: true },
      screenType: {
        type: String,
        required: true,
        default: 'common',
        enum: ['scene', 'common', 'child']
      },
      variableList: [
        {
          name: { type: String },
          content: { type: String },
          type: { type: String },
          description: { type: String }
        }
      ],
      type: {
        type: String,
        required: true,
        default: 'pc',
        enum: ['pc', 'mobile']
      },
      templateId: { type: Number, default: 1 },
      themeSchemeId: { type: Number, default: 0 },
      isScreentpl: { type: Boolean, default: false }, // 是否作为大屏模版
      isDynamicScreen: { type: Boolean, default: false }, // 是否作为动态大屏
      isHistoryScreen: { type: Boolean, default: false }, // 是否是某个大屏的历史大屏
      isConnect: { type: Boolean, default: false }, //是否开启大屏多端控制
      voiceControl: { type: Boolean, default: false }, //是否开启大屏语音控制
      sceneConfig: [
        {
          sceneId: { type: String },
          sceneName: { type: String },
          sceneOrder: { type: Number },
          sceneBackground: { type: String },
          backgroundColor: { type: String, default: '' },
          backgroundRepeat: {
            type: String,
            default: 'fill',
            enum: ['contain', 'cover', 'repeat', 'fill']
          },
          pageList: [
            {
              pageId: { type: String },
              pageName: { type: String },
              pageOrder: { type: Number },
              pageBackground: { type: String },
              backgroundColor: { type: String, default: '' },
              backgroundRepeat: {
                type: String,
                default: 'fill',
                enum: ['contain', 'cover', 'repeat', 'fill']
              }
            }
          ]
        }
      ],
      config: {
        width: { type: Number, default: 1920 },
        height: { type: Number, default: 1080 },
        backgroundParticlesType: { type: String, default: 'STYLE_NONE' },
        bgType: {
          type: String,
          default: 'built_in',
          enum: ['built_in', 'custom']
        },
        mergeParams:{
          enableMerge: { type:Boolean, default: true},
          mergeWay: {type: String, default: 'count'},
          mergeNumber: {type: Number, default: 10}
        },
        mergeFolder: [{}],
        backgroundColor: { type: String, default: 'rgba(49, 50, 57, 1)' },
        backThumbnail: { type: Boolean, default: false },
        backgroundImage: { type: String, default: '' },
        backgroundParticlesCustom: {
          size: [],
          speed: [],
          opacity: [],
          number: { type: Number, default: 100 },
          color: [{ value: { type: String, default: '#ffffff' } }]
        },
        scale: { type: Number, default: 1, min: 0, max: 1 },
        scaleType: {
          type: String,
          default: 'full_height_scroll',
          enum: [
            'full_screen',
            'full_width',
            'full_height',
            'full_height_scroll',
            'no_scale'
          ]
        },
        gridSpace: { type: Number, default: 1 },
        thumbnail: { type: String, default: '' },
        backgroundRepeat: {
          type: String,
          default: 'fill',
          enum: ['contain', 'cover', 'repeat', 'fill']
        },
        ruler_h_lines: { type: [] },
        ruler_v_lines: { type: [] },
        globalFilterParams: {
          // 全局滤镜配置
          enable: { type: Boolean, default: false }, // 是否启用
          hue: { type: Number, min: 0, max: 360, default: 0 }, // 色相
          saturate: { type: Number, min: 0, max: 200, default: 100 }, // 饱和度
          brightness: { type: Number, min: 0, max: 200, default: 100 }, // 明度
          contrast: { type: Number, min: 0, max: 200, default: 100 }, // 对比度
          opacity: { type: Number, min: 0, max: 100, default: 100 }, // 不透明度
          grayscale: { type: Number, min: 0, max: 100, default: 0 } // 灰度
        },
        skeleton: {
          folderId: { type: Number, default: -1 },
          fileId: { type: String, default: '' },
          url: { type: String, default: '' }
        }
      },
      layerTree: { type: [{}] },
      filter: [],
      permissionDataConfig: {
        // 权限数据源
        dataResponse: {
          // 用户权限api数据源设置
          sourceType: { type: String, default: 'api' },
          source: {
            api: {
              data: {
                sourceId: { type: String, default: '' },
                baseUrl: { type: String, default: '' },
                method: { type: String, default: '' },
                headers: { type: String, default: '{}' },
                path: { type: String, default: '' },
                params: { type: String, default: '' },
                body: { type: String, default: '{}' },
                reqFromBack: { type: Boolean, default: false },
                needCookie: { type: Boolean, default: false }
              }
            }
          }
        },
        mappingRelation: {
          // 用户权限映射字段
          id: { type: String, default: '' },
          label: { type: String, default: '' },
          permission: { type: String, default: '' }
        },
        tips: {
          background: {
            image: {
              repeat: { type: String, default: 'no-repeat' },
              positionY: { type: String, default: 'center' },
              positionX: { type: String, default: 'center' },
              size: { type: String, default: 'contain' },
              url: { type: String, default: '' }
            },
            gradient: {
              deg: { type: Number, default: 90 },
              end: { type: String, default: 'rgba(31, 113, 255, 0.1)' },
              start: { type: String, default: 'rgba(31, 113, 255, 0.6)' }
            },
            pure: { type: String, default: 'rgba(32, 188, 255, 0.15)' },
            type: { type: String, default: 'pure' },
            show: { type: Boolean, default: true }
          },
          font: {
            fontSize: { type: Number, default: 16 },
            color: { type: String, default: '#999' },
            fontFamily: { type: String, default: 'Microsoft YaHei' },
            fontWeight: { type: String, default: '700' }
          },
          info: { type: String, default: '暂无数据' },
          type: { type: String, default: 'hide' } // hide--隐藏 tips--提示 disable--禁用
        },
        userPermissionList: [] // 用户权限列表
      },
      screenIcons: {
        // 合并的icon
        mergeSvg: { type: String, default: 'mergeSvg' },
        iconList: []
      },
      shareCollection: {},
      createdAt: { type: String },
      updatedAt: { type: String }
    },
    {
      timestamps: { currentTime: () => getDate() }
    }
  )

  // 自动添加一个自增 id
  // ScreenSchema.plugin(AutoIncrement, {fieldName: 'id', modelName:'Screen'});

  return mongoose.model('Screen', ScreenSchema)
}
