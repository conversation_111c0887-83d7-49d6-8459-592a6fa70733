const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class SeatomCliUsersService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Seatomcliuser.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Seatomcliuser.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params) {
    const res = await this.ctx.model.Seatomcliuser.create(params)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Seatomcliuser.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Seatomcliuser.update(filter, params, options)
    return res
  }
  async updateMany(filter, update) {
    const res = await this.ctx.model.Seatomcliuser.updateMany(filter, update)
    return res
  }
}

module.exports = SeatomCliUsersService
