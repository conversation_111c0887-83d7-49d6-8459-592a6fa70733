/*
 * @Description: 组件包
 * @Date: 2022-09-29 16:40:28
 * @Author: chen<PERSON>yu
 * @LastEditors: chenxingyu
 */
const Service = require('egg').Service
const { getCommonProjection, compareVersion } = require('../extend/utils')
const path = require('path')
const fs = require('fs-extra')

class PackagesService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Package.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  // 根据组件名称获取最新版本组件
  async findLatestVersion({ name }, projection, options) {
    const res = await this.ctx.model.Package.find(
      { name },
      getCommonProjection(projection),
      options
    )
    res.sort((a, b) => {
      return -compareVersion(a.version, b.version)
    })
    return res[0]
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Package.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params) {
    const res = await this.ctx.model.Package.create(params)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Package.deleteOne(params)
    return res
  }
  async deleteOnePkg({ name, version }) {
    const { config, ctx } = this
    const pkgPath = path.resolve(config.resourcePath, './packages')
    const filePath = path.resolve(pkgPath, `${name}@${version}`)
    const zipPath = path.resolve(pkgPath, `${name}@${version}.tar.gz`)
    if (fs.existsSync(filePath)) {
      fs.removeSync(filePath)
    }
    if (fs.existsSync(zipPath)) {
      fs.removeSync(zipPath)
    }
    const res = await ctx.model.Package.deleteOne({ name, version })
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Package.update(filter, params, options)
    return res
  }

  /**
   * 获取组件包的版本名称列表
   * @returns
   */
  async getPkgVersionNames() {
    const list = await this.ctx.service.package.find()

    let formatPath = ''
    const pkgVersionNames = {}
    list.map(item => {
      if (!formatPath) {
        const pkgVersionName = `${item.name}@${item.version}`
        formatPath = item.path
          .replace(pkgVersionName, '{pkgVersionName}')
          .replace(pkgVersionName.replace('@', '.'), '{fileName}')
      }

      if (!pkgVersionNames[item.name]) {
        pkgVersionNames[item.name] = []
      }

      pkgVersionNames[item.name].push(item.version)
    })

    const res = {
      formatPath,
      pkgVersionNames
    }
    return res
  }

  /**
   * 缓存组件包map
   * @returns
   */
  async cachePackageMap(data) {
    const { config } = this
    const cachePackageMapKey = `packageMap${config.env}`
    this.ctx.service.cacheservice.set(cachePackageMapKey, data, 15768000)
  }

  /**
   * 获取缓存的组件包map
   * @returns
   */
  async getCachePackageMap() {
    const { config } = this
    const cachePackageMapKey = `packageMap${config.env}`
    return await this.ctx.service.cacheservice.get(cachePackageMapKey)
  }
}

module.exports = PackagesService
