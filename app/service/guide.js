const Service = require('egg').Service
const { getCommonProjection, uuid } = require('../extend/utils')
const path = require('path')
const fs = require('fs-extra')
class GuideService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Guide.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Guide.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Guide.create(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Guide.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Guide.update(filter, params, options)
    return res
  }

  async upload(fileData) {
    const { config } = this
    const filefolder = fileData.filefolder
    const file = fileData.file
    let filePath, newUrl
    const fileNameArr = file.filename.split('.')
    const fileType = fileNameArr[fileNameArr.length - 1] // 获取文件格式
    const v4 = uuid()
    filePath = path.resolve(
      config.resourcePath,
      `./${filefolder}/${v4}.${fileType}`
    )
    newUrl = `/public/${filefolder}/${v4}.${fileType}`
    if (!fs.existsSync(filePath)) {
      await fs.moveSync(file.filepath, filePath)
    }
    const res = {
      url: newUrl, //返回文件的路径
      fileName: file.filename
    }
    return res
  }
}

module.exports = GuideService
