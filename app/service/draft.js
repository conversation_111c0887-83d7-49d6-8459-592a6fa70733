const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class DraftService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Draft.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Draft.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Draft.create(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Draft.deleteOne(params)
    return res
  }
  async deleteMany(params, options) {
    const res = await this.ctx.model.Draft.deleteMany(params, options)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Draft.updateOne(filter, params, options)
    return res
  }
}

module.exports = DraftService
