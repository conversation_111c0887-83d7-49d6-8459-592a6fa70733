const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class UserBehaviorService extends Service {

  async count(filter) {
    const res = await this.ctx.model.UserBehavior.countDocuments(filter)
    return res
  }

  async create(params) {
    const res = await this.ctx.model.UserBehavior.create(params)
    return res
  }

  async paginfind(filter, projection, page, pageSize) {
    const start = (page - 1) * pageSize
    const res = await this.ctx.model.UserBehavior.find(
      filter,
      getCommonProjection(projection)
    )
    .skip(start)
    .limit(pageSize)
    return res
  }
}

module.exports = UserBehaviorService
