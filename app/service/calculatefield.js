const Service = require('egg').Service
const { getCommonProjection, randomStr } = require('../extend/utils')
const {
  replaceNameToFid,
  dmcErrorCodeTranslate,
  buildGroupFieldSql
} = require('../extend/sql-utils')
const SeatomException = require('../exception/seatomException')
const { ERROR_CODES } = require('../extend/constant')
const dbconns = require('../helper/datasource')

class CalculateFieldsService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Calculatefield.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Calculatefield.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Calculatefield.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Calculatefield.update(
      filter,
      params,
      options
    )
    return res
  }

  async createField(params, options) {
    // params.field.fid已存在时更新字段，不存在时新建
    const workspaceId = params.workspaceId
    const tbId = params.tbId
    if (!workspaceId || !tbId) {
      throw new SeatomException(
        ERROR_CODES.PARAMETER_IS_REQUIRED,
        '缺失workspaceId或tbId'
      )
    }

    // 目前只有dmc有计算字段，后续如果其他类型数据源有需要再把校验方法移到datasource里
    const dbconn = new dbconns['dmc'](params, this)
    const dmcFields = await dbconn.getFieldList({
      tbid: params.tbId,
      needCalculate: 0
    })
    const dmcFieldsMap = {}
    // 重名校验
    dmcFields.forEach(element => {
      element.fields.forEach(field => {
        if (field.name == params.field.name) {
          throw new SeatomException(
            ERROR_CODES.FIELD_NAME_REPEAT,
            '计算字段名称重复'
          )
        }
        dmcFieldsMap[field.name] = field.fid
      })
    })

    // 公式合法性校验
    let formula = params.field.formula
    let realFormula
    if (params.field.field_type == 1) {
      // 计算字段公式
      if (!formula) {
        throw new SeatomException(ERROR_CODES.SQL_ERROR, '计算字段公式不能为空')
      }
      if (formula.length > 500) {
        throw new SeatomException(ERROR_CODES.SQL_ERROR, '公式长度超出限制')
      }
      realFormula = replaceNameToFid(formula, dmcFieldsMap)
    } else if (params.field.field_type == 2) {
      // 组装分组字段公式
      // todo: 目前是直接拼接成源字段id的形式，评估是否像计算字段一样需要统一成以字段名形式再进行转换
      realFormula = buildGroupFieldSql(params.field.param)
      params.field.formula = realFormula
    }

    this.ctx.logger.info('create calculate field real formula: ', realFormula)
    // todo: 非法字符校验
    // todo: tassadar valid-field接口
    const tassadarUrl = this.config.dmcAddress.tassadar
    const shortUrl = '/field/valid'
    let createFlag = true // 标记是创建还是更新，新建字段就不触发对component的扫描更新了,true新建false更新
    const result = await this.ctx.curl(tassadarUrl + shortUrl, {
      method: 'post',
      dataType: 'json',
      // headers: this.cookie,
      timeout: 20000,
      data: {
        tb_id: params.tbId,
        is_row: 1,
        user_id: params.sysuserid,
        aggregator: realFormula
      }
    })
    this.ctx.logger.info('计算字段入参信息', {
      tb_id: params.tbId,
      is_row: 1,
      user_id: params.sysuserid,
      aggregator: realFormula
    })
    if (result.data.status != '0') {
      this.ctx.logger.error('field/valid访问失败，错误信息： ', result)
      throw new SeatomException(
        ERROR_CODES.SQL_ERROR,
        dmcErrorCodeTranslate(result.data.status)
      )
    }
    var filter = { workspaceId: workspaceId, tbId: tbId }
    const calculateFieldData = await this.ctx.model.Calculatefield.findOne(
      filter
    )
    if (calculateFieldData) {
      let fieldList = calculateFieldData.fields || []
      for (let i = 0; i < fieldList.length; i++) {
        if (fieldList[i].fid == params.field.fid) {
          // id相同，说明是更新，全部替换成新的配置，然后跳过本次重名校验
          createFlag = false
          fieldList[i] = params.field
          break
        }
        if (fieldList[i].name == params.field.name) {
          throw new SeatomException(
            ERROR_CODES.FIELD_NAME_REPEAT,
            '计算字段名称重复'
          )
        }
      }
      // 如果是更新字段，需要把component里记录的字段信息更新
      if (!createFlag) {
        this.updateComponentSelectedFields(workspaceId, tbId, params.field)
      }
      if (!params.field.fid) {
        const fid = 'scfk_' + randomStr(8)
        params.field.fid = fid
        fieldList.push(params.field)
      }
      let result = await this.ctx.model.Calculatefield.update(filter, {
        fields: fieldList
      })
      return result
    }
    // 没有添加过计算字段则此次一定是新建
    const fid = 'scfk_' + randomStr(8)
    params.field.fid = fid
    filter['fields'] = [params.field]
    const res = await this.ctx.model.Calculatefield.create(filter)
    return res
  }

  async getFieldList(params, options) {
    const workspaceId = params.workspaceId
    const tbId = params.tbId
    if ((workspaceId !== 0 && !workspaceId) || !tbId) {
      throw new SeatomException(
        ERROR_CODES.PARAMETER_IS_REQUIRED,
        '缺失workspaceId或tbId'
      )
    }
    const projection = {
      'fields._id': 0
    }
    const res = await this.ctx.model.Calculatefield.findOne(
      { workspaceId: params.workspaceId, tbId: params.tbId },
      projection
    )
    if (res) {
      return res.fields
    }
    return []
  }

  async deleteField(params, options) {
    const workspaceId = params.workspaceId
    const tbId = params.tbId
    if (!workspaceId || !tbId) {
      throw new SeatomException(
        ERROR_CODES.PARAMETER_IS_REQUIRED,
        '缺失workspaceId或tbId'
      )
    }

    // 校验被删除的字段是否被其他图表依赖，存在依赖不允许删除
    const screenList = await this.ctx.model.Screen.find(
      { workspaceId },
      { id: 1 }
    )
    const screenIdList = screenList.map(element => {
      return element.id
    })
    const componentList = await this.ctx.model.Component.find(
      {
        screenId: screenIdList,
        'dataConfig.dataResponse.source.dmc.data.tbId': tbId
      },
      getCommonProjection
    )
    let errorstr = []
    for (let index = 0; index < componentList.length; index++) {
      const element = componentList[index]
      let field = element.dataConfig.dataResponse.source.dmc.data.fields
      let selectedFields = field.dimension.concat(field.numericalValue)
      for (let i = 0; i < selectedFields.length; i++) {
        const selectedField = selectedFields[i]
        if (selectedField.fid == params.fid) {
          const screenName = await this.ctx.model.Screen.findOne({
            id: element.screenId
          })
          this.ctx.logger.info(
            `计算字段被大屏“${screenName.name}”的“${element.alias}”组件依赖`
          )
          errorstr.push(`大屏“${screenName.name}”的“${element.alias}”组件依赖`)
        }
      }
    }
    if (errorstr.length !== 0) {
      throw new SeatomException(
        ERROR_CODES.FIELD_HAS_DEPENDENCE,
        '计算字段被' + errorstr.join(',')
      )
    }
    // componentList.forEach((element, index) => {
    //   let field = element.dataConfig.dataResponse.source.dmc.data.fields;
    //   let selectedFields = field.dimension.concat(field.numericalValue);
    //   selectedFields.forEach( async(selectedField) => {
    //     console.log(selectedField.fid == params.fid,selectedField.fid,params.fid,'dasdasdasdada')
    //     if (selectedField.fid == params.fid) {
    //       const screenName = await this.ctx.model.Screen.findOne({ id: element.screenId});
    //       this.ctx.logger.info("计算字段被组件 ", element.id,screenName, " 所依赖，无法删除");
    //       throw new SeatomException(ERROR_CODES.FIELD_HAS_DEPENDENCE, `计算字段被大屏“${screenName.name}”的“${element.comName}”组件依赖`);
    //     }
    //   })
    // })

    let filter = { workspaceId: workspaceId, tbId: tbId }
    const res = await this.ctx.model.Calculatefield.findOne(filter)
    let fieldList = res.fields
    fieldList = fieldList.filter(function (item) {
      return item.name != params.name
    })
    let result = await this.ctx.model.Calculatefield.update(filter, {
      fields: fieldList
    })
    return result
  }

  async updateComponentSelectedFields(workspaceId, tbId, field) {
    // 在修改计算字段的时候，同步修改所有component中该计算字段的信息
    // field是新字段信息
    // todo: component表需要补workspaceId字段，否则只能走workspace->screens->component这套关联逻辑，贼麻烦
    // 其实有了workspaceId也很麻烦。。。是不是可以考虑维护一个列表记录计算字段的使用情况，直接从列表去找对应的component，字段依赖校验也需要
    const screenList = await this.ctx.model.Screen.find(
      { workspaceId },
      { id: 1 }
    )
    const screenIdList = screenList.map(element => {
      return element.id
    })
    const componentList = await this.ctx.model.Component.find(
      {
        screenId: screenIdList,
        'dataConfig.dataResponse.source.dmc.data.tbId': tbId
      },
      getCommonProjection
    )
    if (!componentList) {
      return
    }
    componentList.forEach(component => {
      let fields = component.dataConfig.dataResponse.source.dmc.data.fields
      let updateFlag = false //是否需要更新，避免多次写库
      for (let i = 0, len = fields.dimension.length; i < len; i++) {
        if (
          fields.dimension[i].fid == field.fid &&
          fields.dimension[i] != field
        ) {
          updateFlag = true
          fields.dimension[i] = field
        }
      }
      for (let i = 0, len = fields.numericalValue.length; i < len; i++) {
        if (fields.numericalValue[i].fid == field.fid) {
          updateFlag = true
          let newNumericalValueField = field
          newNumericalValueField.calculation =
            fields.numericalValue[i].calculation
          fields.numericalValue[i] = newNumericalValueField
        }
      }

      if (updateFlag) {
        this.ctx.model.Component.update(
          { screenId: component.screenId, id: component.id },
          { 'component.dataConfig.dataResponse.source.dmc.data.fields': fields }
        )
      }
    })
  }
}

module.exports = CalculateFieldsService
