/*
 * @Description: db1
 * @Date: 2022-11-11 16:56:29
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */
const Service = require('egg').Service
class CacheServiceDb1 extends Service {
  async set(key, value, seconds) {
    const { redis } = this.app
    value = JSON.stringify(value)
    if (!seconds) {
      await redis.get('db1').set(key, value)
    } else {
      await redis.get('db1').set(key, value, 'EX', seconds)
    }
  }

  async get(key) {
    const { redis } = this.app
    let data = await redis.get('db1').get(key)
    if (!data) return
    data = JSON.parse(data)
    return data
  }
}

module.exports = CacheServiceDb1
