const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
const fs = require('fs-extra')
const path = require('path')
class CustomComponentsService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Customcomponent.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Customcomponent.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Customcomponent.create(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Customcomponent.deleteOne(params)
    return res
  }
  async deleteMany(params, options) {
    const res = await this.ctx.model.Customcomponent.deleteMany(params, options)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Customcomponent.update(
      filter,
      params,
      options
    )
    return res
  }
  async packCom(data, templatePath) {
    const htmlData = data.instanceCode.HTML.content
    const cssData = data.instanceCode.CSS.content
    const jsData = data.instanceCode.JavaScript.content
    const jsResources = data.instanceCode.JavaScript.resources
    const cssResources = data.instanceCode.CSS.resources
    const initContent = data.instanceCode.JavaScript.initContent
    const resizeContent = data.instanceCode.JavaScript.resizeContent
    const echartsCompId = data.echartsCompId
    const resourcesCss = cssResources
      .filter(item => {
        return item.type == 'add'
      })
      .map(item => item.url)
    const resourcesUrl = jsResources
      .filter(item => {
        return item.type == 'add'
      })
      .map(item => item.url)
    const resizeMap = {
      dom: '',
      echarts: ''
    }

    const mainvue = `<template>
        <div class="my-chart">
          ${htmlData}
        </div>
      </template>
      
      <script>
      import echarts from "echarts"
      import $ from "jquery"
      export default {
        data() {
          return {
            name: '{comCnName}'
          };
        },
        created () {
          this.customLoad = new Promise(async (resolve) => {
            await this.loadJS(${JSON.stringify(resourcesUrl)})
            await this.loadCss(${JSON.stringify(resourcesCss)})
            resolve()
          })
        },
        methods: {
          init () {
            ${
              data.currentTemp === 'ECharts'
                ? "this.myChart = echarts.init(this.$refs['" +
                  echartsCompId +
                  "'])"
                : ''
            }
            ${initContent}
          },
          render () {
            this.customLoad && this.customLoad.then(() => {
              if (this.myChart && this.myChart.dispose) {
                this.myChart.dispose()
                this.init()
              }
              const vm = this
              vm.refs = this.$refs
              if (${ data.currentTemp === 'ECharts' } && !this.myChart) return
              ${jsData}
              ${
                data.currentTemp === 'ECharts'
                  ? 'this.myChart.setOption(option);'
                  : ''
              }
            })
          },
          resize ({ width, height }) {
            ${resizeContent}
          },
          clear () {
          },
          destroy () {
          },
          loadCss (url) {
            url.map(item => {
            var link = document.createElement('link')
            link.rel = "stylesheet";
            link.href = item
            document.getElementsByTagName('head')[0].appendChild(link);
            })
          },
          loadJS( urls, callback ) {
            return urls.reduce((promise, url) => {
              return promise.then(() => {
                return new Promise((resolve, reject) => {
                  const script = document.createElement('script');
                  script.type = 'text/javascript';
                  script.onload = function() {
                    if (callback) callback();
                    resolve();
                  };
                  script.onerror = reject;
                  script.src = url;
                  document.getElementsByTagName('head')[0].appendChild(script);
                });
              });
            }, Promise.resolve());
          }
        }
      }
      </script>
      <style lang="scss" scoped>
      .my-chart {
        ::v-deep {
          ${cssData}
        }
      }
      </style>`
    const packageJson = JSON.stringify(data.compConfig, null, '\t')
    await fs.writeFileSync(`${templatePath}/package.json`, packageJson, err => {
      if (err) {
        chalkLog.log(err)
      }
    })
    await fs.writeFileSync(`${templatePath}/src/main.vue`, mainvue, err => {
      if (err) throw err
    })
    // return packageJson;
  }
}

module.exports = CustomComponentsService
