const Service = require('egg').Service

class ConfigInfoService extends Service {

  async find() {
    const res = await this.ctx.model.Configinfo.find()

    if (!res.length) {
      const newUser = await this.ctx.model.Configinfo.create({});
      return newUser;
    }

    return res[0]
  }

  async update(filter, params, options) {
    const res = await this.ctx.model.Configinfo.updateMany(filter, params, options)
    return res
  }

}

module.exports = ConfigInfoService