const Service = require('egg').Service
const _ = require('lodash')
const {
  getCommonProjection,
  uuid,
  randomStr,
  copyResource,
  isEmpty
} = require('../extend/utils')

class LabelService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Label.find(
      filter,
      getCommonProjection(projection),
      options)
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Label.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Label.create(params, options)
    return res
  }
  async insertMany(docs, options) {
    const res = await this.ctx.model.Label.insertMany(docs, options)
    return res
  }
  async deleteMany(params, options) {
    const res = await this.ctx.model.Label.deleteMany(params, options)
    return res
  }
  async deleteOne(params, options) {
    const res = await this.ctx.model.Label.deleteOne(params, options)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Label.update(filter, params, options)
    return res
  }
}

module.exports = LabelService