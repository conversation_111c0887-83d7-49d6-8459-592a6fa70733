const Service = require('egg').Service
const { getCommonProjection, randomStr } = require('../extend/utils')
const { EXPORT_COMTYPE } = require('../extend/constant')
const { getResponseBody, uuid } = require('../extend/utils')

class HistoryScreenService extends Service {
  async list() {
    const { ctx } = this
    const { screenId } = ctx.query
    const res = await ctx.model.Historyscreen.find({ screenId }, getCommonProjection())
    return res
  }

  async deleteMany(params) {
    const { ctx } = this
    const { screenId } = params
    // 获取删除的大屏的全部历史版本信息
    const historyInfoList = await ctx.model.Historyscreen.find({ screenId }, getCommonProjection())
    // 遍历历史大屏信息，对于其中的每一个screen都删除
    for (const historyInfo of historyInfoList) {
      await ctx.service.screen.delete({ id: historyInfo.connectScreenId })
    }
    // 再删除该大屏在historyscreen表中的信息
    await ctx.model.Historyscreen.deleteMany({ screenId })
    return
  }

  async delete() {
    const { ctx } = this
    const { historyScreenId } = ctx.query
    const historyInfo = await ctx.model.Historyscreen.findOne({ historyScreenId })
    const { connectScreenId } = historyInfo
    // 删除对应的大屏
    await ctx.service.screen.delete({ id: connectScreenId })
    // 删除historyScreen表里的信息
    const res = await ctx.model.Historyscreen.deleteMany({ historyScreenId })
    console.log('res', res);
    return res
  }

  async getVersionNo() {
    const { ctx } = this
    const { screenId } = ctx.query
    let res = await ctx.model.Historyscreen.find({ screenId }, getCommonProjection())
    res = res.sort((a, b) => {
      return b.versionNo - a.versionNo
    })
    let maxVersionNo
    if (res.length > 0) {
      maxVersionNo = res[0].versionNo
    } else {
      maxVersionNo = 0
    }
    const result = maxVersionNo + 1
    return { versionNo: result }
  }

  async publish() { 
    const { ctx } = this
    const { screenId, versionName, versionDesc } = ctx.request.body
    const screenInfo = await ctx.service.screen.findOne({ id: screenId })
    const { workspaceId, projectId } = screenInfo
    let { versionNo } = ctx.request.body 
    if(!versionNo){
      // 如果versionNo不存在，则走getVersionNo的逻辑
      let res = await ctx.model.Historyscreen.find({ screenId }, getCommonProjection)
      res = res.sort((a, b) => {
        return b.versionNo - a.versionNo
      })
      let maxVersionNo
      if (res.length > 0) {
        maxVersionNo = res[0].versionNo
      } else {
        maxVersionNo = 0
      }
      versionNo = maxVersionNo + 1
    }
    const name = uuid()
    // 走复制的逻辑，新建一个大屏，复制原大屏的config，但是isHistoryScreen为true
    const t = new Date().getTime()
    const isScreentpl = false
    const tag = []
    // 获取大屏原组件数据
    const comInfoOrigin = await ctx.service.component.find(
      { screenId },
      { _id: 0 }
    )
    // 大屏原id与现id之间的映射
    const screenIdMapping = {}
    const newScreenIdList = []
    for (let index = 0; index < comInfoOrigin.length; index++) {
      const comInfoOriginItem = comInfoOrigin[index]
      if (EXPORT_COMTYPE.COPYCOMTYPE.includes(comInfoOriginItem.comType)) {
        for (let i = 0; i < comInfoOriginItem.config.screens.length; i++) {
          const quoteScreenId = comInfoOriginItem.config.screens[i].id
          const screenInfoNow = await ctx.service.screen.copy({
            screenIdOrigin: quoteScreenId,
            workspaceId,
            projectId,
            name: `${name}${comInfoOriginItem.alias}`,
            isScreentpl,
            tag,
            t
          })
          screenIdMapping[quoteScreenId] = screenInfoNow.id
          if (screenInfoNow.id) {
            newScreenIdList.push(Number(screenInfoNow.id))
          }
        }
      }
    }
    const screenInfoNow = await ctx.service.screen.copy({
      screenIdOrigin: screenId,
      workspaceId,
      projectId,
      name,
      screenIdMapping,
      isScreentpl,
      tag,
      isCreateTpl: true,
      isHistoryScreen: true,
      t
    })
    await ctx.service.screen.updateMany(
      { id: { $in: newScreenIdList } },
      { parentId: screenInfoNow.id }
    )
    // console.log('screenInfoNow', screenInfoNow);
    const connectScreenId = screenInfoNow.id
    // 在histotoryScreen表中新建
    const res = await ctx.model.Historyscreen.create({ screenId, connectScreenId, versionNo, versionName, versionDesc })
    
    return res
  }

  async editInfo() {
    const { ctx } = this
    const { historyScreenId, versionName, versionDesc } = ctx.request.body
    const res = await ctx.model.Historyscreen.updateOne({ historyScreenId }, { versionName, versionDesc })
    return res
  }

  async recoverHistoryVersion() {
    const { ctx } = this
    const { historyScreenId } = ctx.query
    const historyInfo = await ctx.model.Historyscreen.findOne({ historyScreenId })
    const { screenId, connectScreenId } = historyInfo
    const screenInfo = await ctx.service.screen.findOne({ id: screenId })
    const { workspaceId, projectId } = screenInfo
    // 这里拿到的screenId是待恢复的大屏Id，注意区分
    console.log('screenId', screenId);
    const t = new Date().getTime()
    const comInfoOrigin = await ctx.service.component.find(
      { screenId },
      { _id: 0 }
    )
    const screenIdMapping = {}
    const newScreenIdList = []
    for (let index = 0; index < comInfoOrigin.length; index++) {
      const comInfoOriginItem = comInfoOrigin[index]
      if (EXPORT_COMTYPE.COPYCOMTYPE.includes(comInfoOriginItem.comType)) {
        for (let i = 0; i < comInfoOriginItem.config.screens.length; i++) {
          const quoteScreenId = comInfoOriginItem.config.screens[i].id
          // 相较于对每个引用面板大屏进行恢复，
          // 不如对于每个引用面板大屏直接复制一份，然后删掉原来的组件对应的大屏比较好
          const screenInfoNow = await ctx.service.screen.copy({
            screenIdOrigin: quoteScreenId,
            workspaceId,
            projectId,
            name: `${comInfoOriginItem.alias}`,
            isScreentpl: false,
            tag: [],
            t
          })
          await ctx.service.screen.delete({ id: quoteScreenId })
          screenIdMapping[quoteScreenId] = screenInfoNow.id
          if (screenInfoNow.id) {
            newScreenIdList.push(Number(screenInfoNow.id))
          }
        }
      }
    }
    const screenInfoNow = await ctx.service.historyscreen.recover({
      screenIdOrigin: screenId,
      screenIdNew: connectScreenId,
      screenIdMapping,
      t
    })
    await ctx.service.screen.updateMany(
      { id: { $in: newScreenIdList } },
      { parentId: screenId }
    )
    return screenInfoNow
  }

  async recover(params) {
    const { ctx } = this
    const { screenIdOrigin, screenIdNew, t } = params
    const projection = {
      id: 0,
      name: 0,
      workspaceId: 0,
      projectId: 0,
      updatedAt: 0,
      createdAt: 0,
      layerTree: 0,
      _id: 0,
      __v: 0,
      // 协同ID不要复制
      coeditId: 0,
      shareCollection: 0
    }
    // 把A还原为B，本质上是把B的东西复制给A，前提是A大屏组件全部清掉
    // 把origin的内容清掉后 new复制到origin
    // 删掉A大屏的全部组件
    const comInfoOrigin = await ctx.model.Component.updateMany(
      { screenId: screenIdOrigin },
      { isDelete: true }
    )
    // 获取B大屏的信息
    const screenInfoTarget = await ctx.service.screen.findOne(
      { id: screenIdNew },
      projection
    )
    // 将B的组件批量复制到A
    const comParams = {
      screenIdOrigin:screenIdNew,
      copyScreenId: screenIdOrigin,
      isScreentpl: false,
      screenIdMapping: params.screenIdMapping,
      type: 'copy',
      t
    }
    const layerTree = await this.ctx.service.component.bacthCopy(comParams)
    screenInfoTarget.layerTree = layerTree
    screenInfoTarget.isScreentpl = false
    screenInfoTarget.isHistoryScreen = false
    const newScreenInfo = await this.ctx.service.screen.findOneAndUpdate(
      { id: screenIdOrigin },
      screenInfoTarget,
      {
        new: true
      }
    )
    return newScreenInfo
  }

}
module.exports = HistoryScreenService