const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class ResourcesService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Resource.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Resource.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Resource.create(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Resource.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Resource.update(filter, params, options)
    return res
  }
}

module.exports = ResourcesService
