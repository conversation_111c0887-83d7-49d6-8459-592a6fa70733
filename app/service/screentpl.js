const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class ScreentplService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Screentpl.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params) {
    const res = await this.ctx.model.Screentpl.create(params)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Screentpl.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Screentpl.update(filter, params, options)
    return res
  }
}

module.exports = ScreentplService
