const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class ProjectService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Project.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  findToPromise(filter, projection, options) {
    return this.ctx.model.Project.find(
      filter,
      getCommonProjection(projection),
      options
    )
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Project.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params) {
    const res = await this.ctx.model.Project.create(params)
    return res
  }
  async insertMany(docs, options) {
    const res = await this.ctx.model.Project.insertMany(docs, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Project.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Project.update(filter, params, options)
    return res
  }
}

module.exports = ProjectService
