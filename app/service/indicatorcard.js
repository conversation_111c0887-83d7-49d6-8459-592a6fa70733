const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class IndicatorCardService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Indicatorcard.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Indicatorcard.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params) {
    const res = await this.ctx.model.Indicatorcard.create(params)
    return res
  }
  async delete(params, options) {
    const res = await this.ctx.model.Indicatorcard.deleteMany(params, options)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Indicatorcard.update(
      filter,
      params,
      options
    )
    return res
  }
}

module.exports = IndicatorCardService
