/*
 * @Description: 协同编辑server
 * @Date: 2022-11-09 14:27:17
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */
'use strict'
const Service = require('egg').Service
const {
  getResponseSuccess,
  getResponseError,
  getResponseList,
  getLoggerModel,
  getCommonProjection
} = require('../extend/utils')
const { randomId } = require('../extend/utils')
const { screenDataTransfer } = require('../utils/screen')
const {
  getScreenCoeditRoomsRedisKey,
  getScreenCoeditRootRoom
} = require('../utils/coedit')
const _ = require('lodash')
class ScreencoeditService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Screencoedit.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Screencoedit.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }

  async findOneAndUpdate(conditions, doc, options) {
    const res = await this.ctx.model.Screencoedit.findOneAndUpdate(
      conditions,
      doc,
      options
    )
    return res
  }

  async create(params, options) {
    const res = await this.ctx.model.Screencoedit.create(params, options)
    return res
  }

  async insertMany(params, options) {
    const res = await this.ctx.model.Screencoedit.insertMany(params, options)
    return res
  }

  async delete(params) {
    const res = await this.ctx.model.Screencoedit.deleteOne(params)
    return res
  }

  async deleteMany(conditions) {
    const res = await this.ctx.model.Screencoedit.deleteMany(conditions)
    return res
  }

  async update(filter, params, options) {
    const res = await this.ctx.model.Screencoedit.update(
      filter,
      params,
      options
    )
    return res
  }
  async save(params) {
    const { ctx, app } = this
    const body = params
    // 参数校验
    const errors = app.validator.validate(
      {
        // 协同ID
        coeditId: {
          type: 'number',
          required: false
        },
        // 协同大屏ID
        coeditScreenId: {
          type: 'number',
          required: true
        }
      },
      body
    )
    // 参数校验
    if (errors && errors.length) {
      return getResponseError({
        message: JSON.stringify(errors)
      })
    }

    const coeditId = body.coeditId
    const coeditScreenId = body.coeditScreenId
    const createUserId = ctx.header.sysuserid
    // 是否是更新
    const isUpdate = !!coeditId

    //添加用户分组功能start
    let userlist = body.userList || []
    const rolelist = body.roleList || []
    const grouplist = body.groupList || []
    for (let g = 0; g < grouplist.length; g++) {
      const group = grouplist[g]
      const data = await ctx.service.user.getDmcGroupUserList(group.group_id)
      let groupUserList = data.map(item => {
        return {
          role: group.role,
          userName: item.name,
          userId: item.user_id
        }
      })
      userlist = userlist.concat(groupUserList)
    }

    for (let r = 0; r < rolelist.length; r++) {
      const role = rolelist[r]
      const data = await ctx.service.user.getDmcRoleUserList(role.role_id)
      let roleUserList = data.map(item => {
        return {
          role: role.role,
          userName: item.name,
          userId: item.user_id
        }
      })
      userlist = userlist.concat(roleUserList)
    }
    userlist = _.uniqBy(userlist, 'userId')

    //添加用户分组功能start

    const coeditUsers = userlist || []
    const shareCollection = {
      userList: body.userList || [],
      roleList: body.roleList || [],
      groupList: body.groupList || []
    }
    await ctx.service.screen.update(
      {
        id: coeditScreenId
      },
      {
        shareCollection
      }
    )
    // 通过用户ID获取未分组
    const getDefaultProjectByUserId = async userId => {
      const key = `defaultProject-${userId}`

      let defaultProject = await ctx.service.cacheservice.get(key)

      if (defaultProject) {
        return defaultProject
      }

      // 通过userId查找工作目录ID
      let currWorkspace = await ctx.service.workspace.findOne({
        userId: userId
      })

      if (!currWorkspace) {
        currWorkspace = await ctx.service.workspace.createOrigin({
          name: userId + '的工作空间',
          userId: userId,
          type: 0
        })
      }

      // 查找出未分组
      defaultProject = await ctx.service.project.findOne({
        type: 0,
        workspaceId: currWorkspace.id
      })

      // 兼容找不到的情况，创建
      if (!defaultProject) {
        defaultProject = await ctx.service.project.create({
          type: 0,
          name: '未分组',
          workspaceId: currWorkspace.id
        })
      }
      ctx.service.cacheservice.set(key, defaultProject, 86400)

      return defaultProject
    }

    // 通过用户信息，获取协同编辑每一项
    const getScreenCoeditItemByUser = async ({ user, coeditId }) => {
      const defaultProject = await getDefaultProjectByUserId(user.userId)
      return {
        coeditId,
        coeditScreenId,
        createUserId,
        coeditUserId: user.userId,
        coeditRole: user.role,
        projectId: defaultProject.id,
        workspaceId: defaultProject.workspaceId
      }
    }

    // 通过 coeditUsers 获取新的协同编辑列表
    const getNewScreenCoeditListByCoeditUsers = async ({
      coeditUsers,
      coeditId
    }) => {
      const addList = []
      const promises = []

      for (let index = 0; index < coeditUsers.length; index++) {
        const user = coeditUsers[index]
        promises.push(
          getScreenCoeditItemByUser({
            user,
            coeditId
          }).then(screenCoeditItem => {
            addList.push(screenCoeditItem)
          })
        )
      }

      await Promise.all(promises)

      return addList
    }

    // 更新大屏coeditId
    const updateScreenCoeditId = async (screeenId, coeditId) => {
      // 更新screen表的coeditId字段
      return await ctx.service.screen.findOneAndUpdate(
        {
          id: screeenId
        },
        {
          coeditId: coeditId
        }
      )
    }

    try {
      // 新建
      if (!isUpdate) {
        if (!coeditUsers || coeditUsers.length === 0) {
          // ctx.body = getResponseBody(
          //   null,
          //   false,
          //   '该角色或组织下没有添加用户',
          //   402
          // )
          return getResponseError({ message: '该角色或组织下没有添加用户' })
        }
        const coeditId = randomId()

        const addList = await getNewScreenCoeditListByCoeditUsers({
          coeditUsers,
          coeditId
        })

        // 批量创建协同编辑
        await ctx.service.screencoedit.insertMany(addList)

        // 更新screen表的coeditId字段
        await updateScreenCoeditId(coeditScreenId, coeditId)

        // 添加协同编辑消息
        ctx.service.screencoedit.insertScreenCoeditMsgList({
          coeditId,
          coeditScreenId,
          createUserId,
          screenCoeditList: addList,
          action: 'add'
        })
      } else {
        // 更新
        // 根据 coeditId 找出协同编辑列表
        const list = await ctx.service.screencoedit.find({
          coeditId: coeditId
        })

        // 找出新增的用户
        const newCoeditUsers = []

        // 找出需要更新的列表
        const updateList = []

        for (let index = 0; index < coeditUsers.length; index++) {
          const user = coeditUsers[index]
          const i = list.findIndex(item => {
            return item.coeditUserId === user.userId
          })

          if (i === -1) {
            // 新增的用户
            newCoeditUsers.push(user)
          } else if (list[i].coeditRole !== user.role) {
            // 如果角色改变，则要更新
            updateList.push({
              ...list[i].toObject(),
              coeditRole: user.role
            })
          }
        }

        // promises
        const promises = []

        // 新增动作
        const addAction = async () => {
          // 执行数据库新增
          const addList = await getNewScreenCoeditListByCoeditUsers({
            coeditUsers: newCoeditUsers,
            coeditId: coeditId
          })
          // 批量创建协同编辑
          promises.push(ctx.service.screencoedit.insertMany(addList))

          // 添加协同编辑消息
          ctx.service.screencoedit.insertScreenCoeditMsgList({
            coeditId,
            coeditScreenId,
            createUserId,
            screenCoeditList: addList,
            action: 'add'
          })
        }

        // 更新动作
        const updateAction = async () => {
          for (let index = 0; index < updateList.length; index++) {
            const item = updateList[index]
            promises.push(
              // 更新协同编辑的数据
              ctx.service.screencoedit.updateMany(
                {
                  coeditId: coeditId,
                  coeditUserId: item.coeditUserId
                },
                {
                  $set: {
                    coeditRole: item.coeditRole
                  }
                }
              )
            )
          }

          // 添加协同编辑消息
          ctx.service.screencoedit.insertScreenCoeditMsgList({
            coeditId,
            coeditScreenId,
            createUserId,
            screenCoeditList: updateList,
            action: 'update'
          })
        }

        // 删除动作
        const removeAction = () => {
          // 找到要删除的列表
          const removeList = []
          for (let index = 0; index < list.length; index++) {
            const item = list[index]
            const i = coeditUsers.findIndex(user => {
              return item.coeditUserId === user.userId
            })
            if (i === -1) {
              removeList.push(item)
            }
          }

          // 执行数据库删除
          if (removeList.length) {
            const removeCoeditUserIds = removeList.map(item => {
              return item.coeditUserId
            })

            const remove = async () => {
              return new Promise((resolve, reject) => {
                // 删除协同编辑
                ctx.service.screencoedit
                  .deleteMany({
                    coeditId: coeditId,
                    coeditUserId: { $in: removeCoeditUserIds }
                  })
                  .then(async () => {
                    // 删除之后判断是否还有协同的用户
                    const count = await ctx.service.screencoedit.count({
                      coeditId
                    })

                    // 没有协同用户了
                    if (count === 0) {
                      // 更新screen表的coeditId字段为null
                      await updateScreenCoeditId(coeditScreenId, null)
                      resolve()
                      return
                    }

                    resolve()
                  })
                  .catch(() => {
                    reject()
                  })
              })
            }

            // 添加到promise
            promises.push(remove())

            // 添加协同编辑消息
            ctx.service.screencoedit.insertScreenCoeditMsgList({
              coeditId,
              coeditScreenId,
              createUserId,
              screenCoeditList: removeList,
              action: 'remove'
            })
          }
        }

        // 协同编辑新增
        if (newCoeditUsers.length) {
          await addAction()
        }

        // 协同编辑更新
        if (updateList.length) {
          updateAction()
        }
        // 协同编辑删除
        removeAction()

        await Promise.all(promises)
      }

      return getResponseSuccess({ data: null })
    } catch (error) {
      this.ctx.logger.info(
        getLoggerModel({
          message: '/screencoedit/save error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
      return getResponseError({ data: error.toString() })
    }
  }
  async updateMany(filter, params, options) {
    const res = await this.ctx.model.Screencoedit.updateMany(
      filter,
      params,
      options
    )
    return res
  }

  async count(conditions) {
    const res = await this.ctx.model.Screencoedit.count(conditions)
    return res
  }

  /**
   * 通过coeditId获取coeditInfo
   * @param {number} coeditId 协同ID
   * @return
   */
  async getCoeditInfoByCoeditId({ coeditId }) {
    const screenCoeditProjection = {
      _id: 0,
      __v: 0,
      createdAt: 0,
      updatedAt: 0
    }

    // 根据协同ID找出协同列表
    const screenCoeditList = await this.ctx.service.screencoedit.find(
      {
        coeditId
      },
      screenCoeditProjection
    )

    if (!screenCoeditList.length) {
      return null
    }

    // 找出协同的用户列表
    const coeditUsers = screenCoeditList.map(item => {
      return {
        coeditUserId: item.coeditUserId,
        coeditRole: item.coeditRole
      }
    })

    return {
      createUserId: screenCoeditList[0].createUserId,
      coeditUsers
    }
  }

  // 通过协同列表 获取 协同大屏列表
  async getCoeditScreensScreenCoeditList({
    screenCoeditList,
    projectId,
    filter = {}
  }) {
    const userId = this.ctx.request.header.sysuserid

    // 找出协同编辑列表的所有大屏id
    const coeditScreenIds = screenCoeditList.map(item => {
      return item.coeditScreenId
    })
    // 找出协同编辑的大屏列表
    const coeditScreens = await this.ctx.service.screen.find(
      {
        id: { $in: coeditScreenIds },
        ...filter
      },
      {
        _id: 0,
        __v: 0
      }
    )

    const promises = []
    // 给每一个协同的大屏加上协同信息
    coeditScreens.forEach((screeenItem, index) => {
      screeenItem = screeenItem.toObject()

      if (!projectId) {
        const currentCoedit =
          screenCoeditList.find(item => {
            return (
              Number(item.coeditScreenId) === Number(screeenItem.id) &&
              userId === item.coeditUserId
            )
          }) || {}

        projectId = currentCoedit.projectId || ''
      }

      promises.push(
        this.getCoeditInfoByCoeditId({
          coeditId: screeenItem.coeditId
        }).then(coeditInfo => {
          screeenItem.coeditInfo = coeditInfo

          // 更新projectId到我的
          if (projectId) {
            screeenItem.projectId = projectId
          }
          coeditScreens[index] = screeenItem
        })
      )
    })

    await Promise.all(promises)

    return coeditScreens
  }

  /**
   * 添加协同编辑消息
   * @param {number} coeditId 协同ID
   * @param {number} coeditScreenId 协同大屏ID
   * @param {array} createUserId 创建用户ID
   * @param {array} screenCoeditList 协同列表
   * @param {string} action 动作，add-添加到协同编辑，update-更新协同编辑，remove-移除协同编辑
   */
  async insertScreenCoeditMsgList({
    coeditId,
    coeditScreenId,
    createUserId,
    screenCoeditList = [],
    action = 'add'
  }) {
    const msgList = []
    for (const item of screenCoeditList) {
      msgList.push({
        type: 'screenCoedit',
        userId: item.coeditUserId,
        content: {
          screenCoedit: {
            coeditId,
            action,
            coeditScreenId,
            createUserId,
            coeditUserId: item.coeditUserId,
            coeditRole: item.coeditRole
          }
        }
      })
    }

    this.ctx.service.message.insertMany(msgList)
  }
}

module.exports = ScreencoeditService
