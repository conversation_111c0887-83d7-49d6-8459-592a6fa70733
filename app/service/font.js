const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class FontsService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Font.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Font.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Font.create(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Font.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Font.update(filter, params, options)
    return res
  }
}

module.exports = FontsService
