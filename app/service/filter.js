const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class FiltersService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Filter.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Filter.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Filter.create(params, options)
    return res
  }
  async insertMany(docs, options) {
    const res = await this.ctx.model.Filter.insertMany(docs, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Filter.deleteOne(params)
    return res
  }
  async deleteMany(params, options) {
    const res = await this.ctx.model.Filter.deleteMany(params, options)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Filter.update(filter, params, options)
    return res
  }
}

module.exports = FiltersService
