const Service = require('egg').Service
const _ = require('lodash')
const {
  getCommonProjection,
  getResponseBody,
  uuid,
  getNewComId,
  randomStr,
  copyResource,
  isEmpty,
  randomId,
  deletefilter
} = require('../extend/utils')
const { EXPORT_COMTYPE } = require('../extend/constant')
const parseConfig = require('../extend/parse-config')
const {
  getNewTipsConditionsModel,
  getNewTipsModel
} = require('../utils/component')
class ComponentsService extends Service {
  async findcomList(filter, projection, options) {
    const res = await this.ctx.model.Component.find(
      deletefilter(filter),
      projection,
      options
    )
    return res
  }
  async originFind(filter, projection, options) {
    const res = await this.ctx.model.Component.find(
      deletefilter(filter),
      getCommonProjection(projection),
      options
    )
    return res
  }
  originFindToPromise(filter, projection, options) {
    return this.ctx.model.Component.find(
      deletefilter(filter),
      getCommonProjection(projection),
      options
    )
  }
  async find(filter, projection, options) {
    const res = await this.ctx.model.Component.find(
      deletefilter(filter),
      getCommonProjection(projection),
      options
    )
    for (let index = 0; index < res.length; index++) {
      const comData = res[index]
      if (comData && comData.dataConfig) {
        const tips = comData.dataConfig.dataResponse.tips
        if (!tips) {
          res[index].dataConfig.dataResponse.tips = getNewTipsModel()
        } else if (!tips.conditions) {
          res[index].dataConfig.dataResponse.tips.conditions = [
            getNewTipsConditionsModel(tips)
          ]
        } else if (tips.conditions.length === 0) {
          res[index].dataConfig.dataResponse.tips.conditions.push(
            getNewTipsConditionsModel(tips)
          )
        }
      }
    }
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Component.findOne(
      deletefilter(conditions),
      getCommonProjection(projection),
      options
    )
    if (
      res &&
      res.dataConfig &&
      res.dataConfig.dataResponse.tips.conditions.length === 0
    ) {
      const tips = res.dataConfig.dataResponse.tips
      res.dataConfig.dataResponse.tips.conditions.push(
        getNewTipsConditionsModel(tips)
      )
    }
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Component.create(params, options)
    return res
  }
  async insertMany(docs, options) {
    const res = await this.ctx.model.Component.insertMany(docs, options)
    return res
  }
  async deleteOne(params, options) {
    // const res = await this.ctx.model.Component.deleteOne(params, options)
    const res = await this.ctx.model.Component.update(
      params,
      { isDelete: true },
      options
    )
    return res
  }
  async deleteMany(params, options, deleteIds) {
    // const deleteIdlist = deleteIds || []
    // for (let i = 0; i < deleteIdlist.length; i++) {
    //   const item = deleteIdlist[i]
    //   const comInfo = await this.ctx.service.component.findOne({ id: item })
    //   if (
    //     comInfo &&
    //     comInfo.comType &&
    //     EXPORT_COMTYPE.COPYCOMTYPE.includes(comInfo.comType)
    //   ) {
    //     const screens = comInfo.config.screens || []
    //     if (screens.length) {
    //       for (let index = 0; index < screens.length; index++) {
    //         const element = screens[index].id
    //         await this.ctx.service.screen.delete({ id: element })
    //       }
    //     }
    //   }
    // }
    // const res = await this.ctx.model.Component.deleteMany(params, options)
    const res = await this.ctx.model.Component.updateMany(
      params,
      { isDelete: true },
      options
    )
    await this.ctx.model.Voicecontrol.updateMany(
      { comId: params.id, screenId: params.screenId },
      { isDelete: true }
    )
    // await this.ctx.model.Voicecontrol.deleteMany({ comId: params.id }, options)
    return res
  }

  async update(filter, params, options) {
    const res = await this.ctx.model.Component.update(filter, params, options)
    return res
  }
  // 批量操作，https://mongoosejs.com/docs/api/model.html#model_Model.bulkWrite
  async bulkWrite(opts, options) {
    const res = await this.ctx.model.Component.bulkWrite(opts, options)
    return res
  }
  async upgrade(comVersion) {
    const { ctx } = this
    const oldComponent = await ctx.service.component.findOne({
      id: comVersion.id
    })
    const comName = oldComponent.comName
    const comDefine = await ctx.service.package.findOne({
      name: comName,
      version: comVersion.version
    })
    if (!comDefine) {
      // ctx.body = getResponseBody(null, false, '没有对应版本的组件', 400)
      return {
        data: {
          name: comName,
          version: comVersion.version
        },
        msg: '没有对应版本的组件'
      }
    }
    const comConfig = JSON.parse(comDefine.config)
    let upgradeData
    let upgradeDataConfig = _.defaultsDeep(
      oldComponent.config,
      parseConfig(comConfig.config)
    )
    upgradeData = {
      version: comVersion.version,
      requirePath: comDefine.path,
      config: upgradeDataConfig, //将新旧组件的配置进行合并，旧的对象属性不变，只添加新的对象里面新增的属性。
      controlConfig: JSON.stringify(comConfig.config),
      events: comConfig.events,
      actions: comConfig.actions,
      callbacks: comConfig.callbacks,
      icon: comDefine.icon,
      packageCreateDate: comDefine.updatedAt,
      'other.themeUrl': comDefine.icon
    }
    await ctx.service.component.update({ id: comVersion.id }, upgradeData)
    const comData = await ctx.service.component.findOne({ id: comVersion.id })
    return comData
  }
  async exportUpgradeComs(screenId) {
    const { ctx } = this
    const projection = {
      _id: 0,
      __v: 0,
      createdAt: 0,
      updatedAt: 0,
      projectId: 0,
      workspaceId: 0
    }
    const comInfo = await ctx.service.component.find({ screenId }, projection)
    // const comInfo = _.cloneDeep(comInfolist)
    const list = await ctx.service.package.find()
    const comversion = list.reduce((map, v) => {
      const key = `${v.name}`
      map[key] = v.version
      return map
    }, {})
    for (let c = 0; c < comInfo.length; c++) {
      const com = comInfo[c]
      if (com.version === comversion[com.comName]) {
        continue
      }
      const oldComponent = _.cloneDeep(com)
      const comName = oldComponent.comName
      const lastVersion = comversion[com.comName]
      const comDefine = await ctx.service.package.findOne({
        name: comName,
        version: lastVersion
      })
      if (!comDefine) {
        ctx.body = getResponseBody(null, false, '没有对应版本的组件', 400)
        return
      }
      const comConfig = JSON.parse(comDefine.config)
      comInfo[c].version = lastVersion
      comInfo[c].requirePath = comDefine.path
      comInfo[c].config = _.defaultsDeep(
        oldComponent.config,
        parseConfig(comConfig.config)
      )
      comInfo[c].controlConfig = JSON.stringify(comConfig.config)
      comInfo[c].events = comConfig.events
      comInfo[c].actions = comConfig.actions
      comInfo[c].callbacks = comConfig.callbacks
      comInfo[c].icon = comDefine.icon
      comInfo[c].packageCreateDate = comDefine.updatedAt
      comInfo[c].other.themeUrl = comDefine.icon
    }
    return comInfo
  }

  async exportAllScreen(filter, projection, options, resourcePath) {
    const screenRes = await this.ctx.model.Screen.findOne(
      filter,
      getCommonProjection(projection),
      options
    )
    const comRes = await this.ctx.model.Component.find(
      deletefilter(filter),
      getCommonProjection(projection),
      options
    )
    const filterRes = await this.ctx.model.filter.find(
      filter,
      getCommonProjection(projection),
      options
    )
    const info = {
      screenRes,
      comRes,
      filterRes
    }
    const screen_id = `screen_${screenRes.id}`
    let strInfo = JSON.stringify(info)
    const filePath = path.resolve(
      resourcePath,
      `./screenData/${screen_id}/info_${screen_id}.txt`
    )
    await fs.writeFileSync(filePath, JSON.stringify(info), err => {
      if (err) throw err
    })
    return info
  }

  //遍历树  铺平
  treeToList(datas, tree) {
    let trees = tree
    for (var i in datas) {
      if (datas[i].type === 'com') {
        trees.push(datas[i])
      }
      // 遍历项目满足条件后的操作
      if (datas[i].children) {
        //存在子节点就递归
        this.treeToList(datas[i].children, trees)
      }
    }
    return trees
  }

  // 检查 添加移动端模块面板组件
  checkAddMobileModulePanelLayer(datas, compareDatas, result = []) {
    for (var i in datas) {
      if (
        datas[i].type === 'group' &&
        datas[i].comId &&
        datas[i].comId.startsWith('interaction-container-modulepanel')
      ) {
        const index = compareDatas.findIndex(item => {
          return item.id === datas[i].comId
        })
        if (index === -1) {
          result.push({
            id: datas[i].comId,
            type: 'com'
          })
        }
      }
      // 遍历项目满足条件后的操作
      if (datas[i].children) {
        //存在子节点就递归
        this.checkAddMobileModulePanelLayer(
          datas[i].children,
          compareDatas,
          result
        )
      }
    }
    return result
  }

  changeTree(objAry, key, value) {
    //遍历树 ，修改值
    if (objAry != null) {
      objAry.forEach(item => {
        if (item.type === 'com') {
          Object.assign(item, {
            [key]: value[item[key]]
          })
        }
        if (item.type === 'group' && item.comId) {
          Object.assign(item, {
            comId: value[item['comId']]
          })
        }
        this.changeTree(item.children, key, value)
      })
    }
  }

  filterTree(data) {
    var newTree = data.filter(x => x.grade !== 1)
    newTree.forEach(x => x.children && (x.children = filterTree(x.children)))
    return newTree
  }

  /**
   * 批量复制组件
   * @param {Object} params
   * @param {Number} params.workspaceId 工作空间ID
   * @param {Number} params.copyScreenId 复制后的大屏ID（新）
   * @param {Object} params.screenIdMapping 面板大屏ID映射关系
   * @param {String} params.type 类型，import-导入，copy-复制
   * @param {Object} params.screenInfo 大屏数据
   * @param {Array} params.comList 组件列表
   * @param {Array} params.layerTree 图层数据
   * @param {Array} params.filterInfo 过滤器数据
   * @param {Array} params.datastorage 数据源数据
   * @param {Number} params.screenIdOrigin 原大屏ID
   * @param {Boolean} params.isScreentpl 是否模板
   * @param {Boolean} params.t 时间戳
   * @returns {Array} comlayerTree 新图层树结构
   */
  async bacthCopy(params) {
    const workspaceId = params.workspaceId
    const copyScreenId = params.copyScreenId
    const screenIdMapping = params.screenIdMapping
    const isImport = params.type === 'import'

    let screenInfo,
      layerTree = [],
      filterInfo = [],
      datastorage = [],
      screenIdOrigin,
      comList = []
    if (isImport) {
      screenInfo = params.screenInfo
      comList = params.comList
      layerTree = screenInfo.layerTree
      filterInfo = params.filterInfo
      datastorage = params.datastorage
    } else {
      screenIdOrigin = params.screenIdOrigin
      screenInfo = await this.ctx.service.screen.findOne({ id: screenIdOrigin })
      layerTree = screenInfo.layerTree
      filterInfo = await this.ctx.service.filter.find(
        { screenId: screenIdOrigin },
        { screenId: 0 }
      )
    }

    // 导入数据源  基于模版创建和copy不需要数据源的创建映射
    let datastorageMapping = await this.ctx.service.component.datastorageMap({
      workspaceId,
      datastorage
    })

    // copy生成新的过滤器
    let filterIdMapping = await this.ctx.service.component.filterIdMap({
      copyScreenId,
      filterInfo
    })

    setImmediate(() => {
      this.ctx.logger.info(
        'get final bacthCopy screenIdMapping: ',
        screenIdMapping
      )

      this.ctx.logger.info(
        'get final bacthCopy datastorageMapping: ',
        datastorageMapping
      )
      this.ctx.logger.info(
        'get final bacthCopy filterIdMapping: ',
        filterIdMapping
      )
    })

    let comIdMapping = {} //新旧组件id的映射关系
    if (layerTree.length === 0) {
      return []
    }
    let comlayerTree = _.cloneDeep(layerTree) // 将图层复制进行comid替换

    const layerList = this.treeToList(layerTree, []) // 将组件图层数据铺平

    // 检查 添加移动端模块面板组件
    if (screenInfo.type === 'mobile') {
      const mobileModulePanelLayers = this.checkAddMobileModulePanelLayer(
        layerTree,
        layerList,
        []
      )
      layerList.push(...mobileModulePanelLayers)
    }

    // 从数据库批量获取组件
    if (!isImport) {
      const layerIds = layerList.map(layer => {
        return layer.id
      })

      if (layerIds && layerIds.length) {
        comList = await this.ctx.service.component.find({
          id: {
            $in: layerIds
          }
        })
      }
    }

    // 需要新建的组件文档
    const insertComponentDocs = []

    for (let i = 0; i < layerList.length; i++) {
      const layer = layerList[i]
      // 过滤组件
      let componentData = comList.filter(item => {
        return item.id === layer.id
      })[0]
      // 如果从数据库获取则有toObject方法
      componentData =
        componentData && componentData.toObject
          ? componentData.toObject()
          : componentData

      // 如果是模版时进行静态资源copy
      if (params.isScreentpl) {
        componentData = JSON.parse(
          copyResource(
            JSON.stringify(componentData),
            this.config.resourcePath,
            'system',
            'tpl'
          )
        )
      }
      if (!componentData) {
        continue
      }
      componentData.screenId = copyScreenId
      // 生成新的组件ID
      componentData.id = getNewComId(componentData.id, params.t)

      comIdMapping[layer.id] = componentData.id

      // 添加到文档列表
      insertComponentDocs.push(componentData)

      if (componentData.children && componentData.children.length) {
        for (let index = 0; index < componentData.children.length; index++) {
          const item = componentData.children[index]
          let clildcom
          if (isImport) {
            const clildcomlist = comList.filter(i => {
              return i.id === item
            })
            clildcom = clildcomlist[0]
          } else {
            clildcom = await this.ctx.service.component.findOne({ id: item })
            clildcom = clildcom.toObject ? clildcom.toObject() : clildcom
          }

          clildcom.screenId = copyScreenId
          // 生成新的组件ID
          clildcom.id = getNewComId(clildcom.id, params.t)

          // 添加到文档列表
          insertComponentDocs.push({
            id: clildcom.id,
            screenId: clildcom.screenId,
            ...clildcom
          })

          comIdMapping[item] = clildcom.id
        }
      }
    }

    // 更新组件关联的ID（事件关联组件ID、过滤器ID、数据源ID、大屏ID等等）
    const updateComponentDocs =
      await this.ctx.service.component.updateComMapping({
        copyScreenId,
        filterIdMapping,
        comIdMapping,
        datastorageMapping,
        screenIdMapping,
        componentDocList: insertComponentDocs,
        t: params.t
      })

    // 批量导入组件
    await this.ctx.service.component.insertMany(updateComponentDocs)

    this.changeTree(comlayerTree, 'id', comIdMapping)

    return comlayerTree
  }
  // 过滤器映射
  async filterIdMap(params) {
    let filterIdMapping = {} //新旧过滤器的映射关系
    if (params.filterInfo && params.filterInfo.length > 0) {
      // 需要插入的过滤器文档
      const insertFilterDocs = []

      for (let index = 0; index < params.filterInfo.length; index++) {
        const element = params.filterInfo[index]

        const newFilterId = randomId()
        // 添加到插入列表
        insertFilterDocs.push({
          id: newFilterId,
          screenId: params.copyScreenId,
          name: element.name,
          content: element.content,
          callbackKeys: element.callbackKeys,
          type: element.type || 'custom',
          systemParams: element.systemParams || []
        })

        filterIdMapping[element.id] = newFilterId
      }

      await this.ctx.service.filter.insertMany(insertFilterDocs)
    }
    return filterIdMapping
  }
  // 数据源映射
  async datastorageMap(params) {
    let datastorageMapping = {} //数据源映射问题
    if (params.datastorage && params.datastorage.length > 0) {
      // 需要插入的数据源文档
      const insertDatastorageDocs = []

      for (let index = 0; index < params.datastorage.length; index++) {
        if (!params.datastorage[index]) {
          continue
        }
        const element = params.datastorage[index]

        const newId = randomId()
        insertDatastorageDocs.push({
          id: newId,
          workspaceId: params.workspaceId,
          type: element.type,
          name: element.name,
          description: element.description,
          config: element.config
        })

        datastorageMapping[element.id] = newId
      }

      await this.ctx.service.datastorage.insertMany(insertDatastorageDocs)
    }
    return datastorageMapping
  }

  // 组件ID映射，更新传递进来的componentDocList的对象数组数据
  async updateComMapping(params) {
    const newComDataList = params.componentDocList || []

    // 获取新的组件ID
    const getNewComIdByOldId = oldId => {
      // return params.comIdMapping[oldId]
      return getNewComId(oldId, params.t)
    }

    // 获取新的的数据源ID
    const getNewSourceIdByOldId = oldId => {
      return params.datastorageMapping[oldId]
    }

    // 获取新的的过滤器ID
    const getNewFilterIdByOldId = oldId => {
      return params.filterIdMapping[oldId]
    }

    // 获取新的的大屏ID
    const getNewScreenIdByOldId = oldId => {
      return params.screenIdMapping[oldId]
    }

    // 判断是否是组件ID
    const isComId = comId => {
      return comId && comId.indexOf('groups_') === -1
    }

    for (let index = 0; index < newComDataList.length; index++) {
      const newCom = newComDataList[index].toObject
        ? newComDataList[index].toObject()
        : newComDataList[index]
      const interactionConfig = newCom.interactionConfig
      const triggerId = newCom.id

      // 对于事件的复制映射
      if (interactionConfig.events.length) {
        for (let i = 0; i < interactionConfig.events.length; i++) {
          interactionConfig.events[i].id = 'event_' + randomStr(10)
          interactionConfig.events[i].triggerId = triggerId

          const locatedCompId = interactionConfig.events[i].locatedCompId
          if (locatedCompId) {
            interactionConfig.events[i].locatedCompId =
              getNewComIdByOldId(locatedCompId)
          }

          for (
            let a = 0;
            a < interactionConfig.events[i].componentId.length;
            a++
          ) {
            const oldComId = interactionConfig.events[i].componentId[a]
            if (isComId(oldComId)) {
              interactionConfig.events[i].componentId[a] =
                getNewComIdByOldId(oldComId)
            }
          }
          for (
            let b = 0;
            b < interactionConfig.events[i].connectCompId.length;
            b++
          ) {
            const oldComId = interactionConfig.events[i].connectCompId[b]
            if (isComId(oldComId)) {
              interactionConfig.events[i].connectCompId[b] =
                getNewComIdByOldId(oldComId)
            }
          }
        }
      }

      // 对于联动的组件映射
      if (interactionConfig.linkAge && interactionConfig.linkAge.length) {
        for (let i = 0; i < interactionConfig.linkAge.length; i++) {
          interactionConfig.linkAge[i].triggerId = triggerId
          if (
            interactionConfig.linkAge[i].linkageComList &&
            interactionConfig.linkAge[i].linkageComList.length
          ) {
            for (
              let index = 0;
              index < interactionConfig.linkAge[i].linkageComList.length;
              index++
            ) {
              const oldComId =
                interactionConfig.linkAge[i].linkageComList[index].componentId
              if (isComId(oldComId)) {
                interactionConfig.linkAge[i].linkageComList[index].componentId =
                  getNewComIdByOldId(oldComId)
              }
            }
          }
          if (
            interactionConfig.linkAge[i].coms &&
            interactionConfig.linkAge[i].coms.length
          ) {
            for (
              let index = 0;
              index < interactionConfig.linkAge[i].coms.length;
              index++
            ) {
              const oldId = interactionConfig.linkAge[i].coms[index].id
              if (isComId(oldId)) {
                interactionConfig.linkAge[i].coms[index].id =
                  getNewComIdByOldId(oldId)
              }
            }
          }
          if (
            interactionConfig.linkAge[i].linkageConfig &&
            interactionConfig.linkAge[i].linkageConfig.length
          ) {
            for (
              let j = 0;
              j < interactionConfig.linkAge[i].linkageConfig.length;
              j++
            ) {
              if (
                interactionConfig.linkAge[i].linkageConfig[j].componentList &&
                interactionConfig.linkAge[i].linkageConfig[j].componentList
                  .length
              ) {
                for (
                  let k = 0;
                  k <
                  interactionConfig.linkAge[i].linkageConfig[j].componentList
                    .length;
                  k++
                ) {
                  const oldComId =
                    interactionConfig.linkAge[i].linkageConfig[j].componentList[
                      k
                    ].componentId
                  if (isComId(oldComId)) {
                    interactionConfig.linkAge[i].linkageConfig[j].componentList[
                      k
                    ].componentId = getNewComIdByOldId(oldComId)
                  }
                }
              }
            }
          }
        }
      }
      // 对于下钻组件的映射
      if (interactionConfig.drillDown && interactionConfig.drillDown.length) {
        for (let i = 0; i < interactionConfig.drillDown.length; i++) {
          interactionConfig.drillDown[i].triggerId = triggerId
        }
      }
      // 对于子组件的映射
      const children = newCom.children
      if (children && children.length) {
        for (let i = 0; i < children.length; i++) {
          newCom.children[i] = getNewComIdByOldId(children[i])
        }
      }

      if (newCom.parent) {
        newCom.parent = getNewComIdByOldId(newCom.parent)
      }
      // 对过滤器进行映射配置
      const newComFiltersInfo = newCom.dataConfig.dataResponse.filters.list

      if (newComFiltersInfo.length > 0 && !isEmpty(params.filterIdMapping)) {
        for (let index = 0; index < newComFiltersInfo.length; index++) {
          newComFiltersInfo[index].id = getNewFilterIdByOldId(
            newComFiltersInfo[index].id
          )
        }
      }

      // 对数据源进行映射配置
      if (
        newCom.dataConfig.dataResponse.sourceType === 'csv_file' &&
        !isEmpty(params.datastorageMapping)
      ) {
        newCom.dataConfig.dataResponse.source.csv_file.data.sourceId =
          getNewSourceIdByOldId(
            newCom.dataConfig.dataResponse.source.csv_file.data.sourceId
          )
      } else if (
        newCom.dataConfig.dataResponse.sourceType === 'json' &&
        !isEmpty(params.datastorageMapping)
      ) {
        newCom.dataConfig.dataResponse.source.json.data.sourceId =
          getNewSourceIdByOldId(
            newCom.dataConfig.dataResponse.source.json.data.sourceId
          )
      } else if (
        newCom.dataConfig.dataResponse.sourceType === 'datacontainer'
      ) {
        const datacontainerData =
          newCom.dataConfig.dataResponse.source.datacontainer.data
        newCom.dataConfig.dataResponse.source.datacontainer.data.dataContainerComId =
          getNewComIdByOldId(datacontainerData.dataContainerComId)
      }
      // 对引用面板，动态面板，弹窗的大屏进行映射配置
      if (
        EXPORT_COMTYPE.COMTYPE.includes(newCom.comType) &&
        !isEmpty(params.screenIdMapping)
      ) {
        for (let i = 0; i < newCom.config.screens.length; i++) {
          newCom.config.screens[i].id =
            getNewScreenIdByOldId(newCom.config.screens[i].id) ||
            newCom.config.screens[i].id
          await this.ctx.service.screen.update(
            { id: newCom.config.screens[i].id },
            { relationCompId: newCom.id }
          )
        }
      }
    }

    return newComDataList
  }
}

module.exports = ComponentsService
