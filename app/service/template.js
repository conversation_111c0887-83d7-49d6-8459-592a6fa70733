const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class TemplatesService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Template.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params) {
    const res = await this.ctx.model.Template.create(params)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Template.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Template.update(filter, params, options)
    return res
  }
}

module.exports = TemplatesService
