const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class ScreenshotService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Screenshot.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Screenshot.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Screenshot.create(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Screenshot.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Screenshot.update(filter, params, options)
    return res
  }
}

module.exports = ScreenshotService
