const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
const path = require('path')
let fs = require('fs-extra')
const base64 = require('base-64')
const { spawnSync, exec } = require('child_process')
const compressing = require('compressing')
const _ = require('lodash')
class PackcomService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Packcom.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Packcom.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params) {
    const res = await this.ctx.model.Packcom.create(params)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Packcom.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Packcom.update(filter, params, options)
    return res
  }
  async pack(serverIp, enName) {
    const { ctx, config } = this
    // const requestBody = body
    // const serverIp = serverIp || 'http://127.0.0.1:7001'
    // const enName = enName || 'regular-bar-normal'
    const Comjson = path.resolve(config.resourcePath, './components.json')
    const packjson = fs.readFileSync(`${Comjson}`, 'utf-8')
    await ctx.service.packcom.update({ id: 1 }, { ispack: true })
    const packagelist = await ctx.service.packcom.find({})
    if (packagelist && packagelist.length < 1) {
      await ctx.service.packcom.create({
        id: 1,
        ispack: false,
        comjson: packjson
      })
    }
    const commonfiles = path.resolve(config.resourcePath, './commonfiles')
    const ccc = JSON.parse(packjson)
    const comPath = ccc[enName]
    const arr = comPath.split('&&')
    const comUrl = arr[0]
    process.chdir(comUrl)
    const execstr = 'seatom package ' + ''
    // spawnSync('seatom', ['config', 'set', 'server', serverIp], { stdio: 'inherit', timeout: 3000 });
    const res = new Promise(function (resolve, reject) {
      exec(execstr, (error, stdout, stderr) => {
        if (error) {
          console.error(`exec error: ${error}`)
          reject({
            error: `exec error: ${error}`,
            stdout,
            stderr,
            message: '打包失败'
          })
          return
        }
        resolve({
          stdout,
          stderr,
          message: '打包成功'
        })
      })
    })
    try {
      const success = await res
      const date = new Date()
      const datestr = `${date.getFullYear()}-${date.getMonth() + 1}-${
        date.getDate() + 1
      }`
      const newpackName = `${enName}_${datestr}_${base64.encode(serverIp)}`
      if (fs.existsSync(`${comUrl}/${newpackName}`)) {
        fs.removeSync(`${comUrl}/${newpackName}`)
      }
      await fs.renameSync(`${comUrl}/build`, `${comUrl}/${newpackName}`)
      // await compressing.tgz.compressDir(`${comUrl}/${newpackName}`, `${comUrl}/${newpackName}.tgz`);
      const newpackpath = `${commonfiles}/${newpackName}`
      if (fs.existsSync(newpackpath)) {
        fs.removeSync(newpackpath)
      }
      await fs.moveSync(`${comUrl}/${newpackName}`, newpackpath)

      return newpackpath
    } catch (error) {
      return error
    }
  }
}

module.exports = PackcomService
