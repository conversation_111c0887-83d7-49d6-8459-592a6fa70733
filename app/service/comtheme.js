const Service = require('egg').Service
const fs = require('fs-extra')
const path = require('path')
const { getCommonProjection, getScreenshot } = require('../extend/utils')
class ComThemesService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Comtheme.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Comtheme.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Comtheme.create(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Comtheme.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Comtheme.update(filter, params, options)
    return res
  }
  async saveIcon(params) {
    const { ctx, config } = this
    const publicPath = config.publicPath
    const { packageId, comthemeData, comthemeId } = params
    // const packageId = body.packageId
    // const comthemeData = await ctx.service.comtheme.findOne({ id: comthemeId });
    let width, height
    if (comthemeData) {
      width = +comthemeData.width
      height = +comthemeData.height
    }
    // const token = ctx.cookies.get('token')
    // const userId = ctx.cookies.get('userId')
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    const name = `${comthemeData.comType}_${comthemeId}`
    const fileType = 'jpg'
    const filefolder = path.resolve(config.resourcePath, './comthemeshotList')
    if (!fs.existsSync(filefolder)) {
      fs.mkdirSync(path.resolve(config.resourcePath, './comthemeshotList'))
    }
    const filePath = path.resolve(
      config.resourcePath,
      './comthemeshotList/' + name + '.' + fileType
    )
    if (fs.existsSync(filePath)) {
      fs.removeSync(filePath)
    }
    const screenshotData = {
      token,
      userId,
      fileType,
      name,
      width,
      height,
      url: `${config.webServerIp}${publicPath}/theme-preview?themeId=${comthemeId}&packageId=${packageId}`,
      path: path.resolve(config.resourcePath, './comthemeshotList')
    }
    const result = await getScreenshot(screenshotData)
    if (result && result.code === 0) {
      let comthemeshotUrl
      comthemeshotUrl = `/public/comthemeshotList/${name}.${fileType}`
      return comthemeshotUrl
    } else {
      return ''
    }
  }
}

module.exports = ComThemesService
