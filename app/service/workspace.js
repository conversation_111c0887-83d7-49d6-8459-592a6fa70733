const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class WorkSpaceService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Workspace.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Workspace.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params) {
    const res = await this.ctx.model.Workspace.create(params)
    // 创建工作空间，默认创建一个未分组的项目
    await this.ctx.model.Project.create({
      workspaceId: res.id,
      name: '未分组',
      type: 0
    })
    const screentpl = await this.ctx.service.screentpl.find({ level: 0 })
    if (!screentpl.length) {
      await this.ctx.service.screentpl.create({
        name: 'pc空白模版',
        type: 'pc',
        level: 0
      })
      await this.ctx.service.screentpl.create({
        name: 'mobile空白模版',
        type: 'mobile',
        level: 0
      })
    }
    return res
  }
  async createOrigin(params) {
    const res = await this.ctx.model.Workspace.create(params)
    return res
  }
  async insertMany(docs, options) {
    const res = await this.ctx.model.Workspace.insertMany(docs, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Workspace.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Workspace.update(filter, params, options)
    return res
  }
}

module.exports = WorkSpaceService
