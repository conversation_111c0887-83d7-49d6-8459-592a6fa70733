const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
const dbconns = require('../helper/datasource')
const SeatomException = require('../exception/seatomException')

const dmcDb = new dbconns.mysql({
  host: '*************',
  port: 3306,
  username: 'root',
  password: 'bdp_pcloud'
})

class UsersService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.User.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.User.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params) {
    const res = await this.ctx.model.User.create(params)
    return res
  }
  async insertMany(docs, options) {
    const res = await this.ctx.model.User.insertMany(docs, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.User.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.User.update(filter, params, options)
    return res
  }
  async updateOne(filter, params, options) {
    const res = await this.ctx.model.User.updateOne(filter, params, options)
    return res
  }
  async updateMany(filter, update) {
    const res = await this.ctx.model.User.updateMany(filter, update)
    return res
  }
  async getDmcUserInfo(data) {
    const { ctx, config } = this
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    const url =
      this.ctx.app.config.dmcAddress.address + '/api/ucenter/user/new/info'
    const res = await ctx.curl(url, {
      method: 'GET',
      data: data,
      headers: {
        Cookie: 'token=' + token + ';' + 'user_id=' + userId + ';'
      },
      dataType: 'json',
      timeout: 100000,
      rejectUnauthorized: false
    })
    return res
  }
  async genredirecturl(data) {
    const { ctx, config } = this
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    const url =
      this.ctx.app.config.dmcAddress.address + '/api/check/gen_redirect_url'
    const res = await ctx.curl(url, {
      method: 'GET',
      data: data,
      headers: {
        Cookie: 'token=' + token + ';' + 'user_id=' + userId + ';'
      },
      dataType: 'json',
      timeout: 100000,
      rejectUnauthorized: false
    })
    return res
  }
  async getDmcUserList() {
    const { ctx, config } = this
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    const url =
      this.ctx.app.config.dmcAddress.address + '/api/ucenter/user/list'
    const res = await ctx.curl(url, {
      method: 'GET',
      data: {
        page: 1,
        page_size: 100000,
        keyword: ''
      },
      headers: {
        Cookie: 'token=' + token + ';' + 'user_id=' + userId + ';'
      },
      dataType: 'json',
      timeout: 100000,
      rejectUnauthorized: false
    })
    return res
  }
  async getDmcRoleList() {
    const { ctx, config } = this
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    const url =
      this.ctx.app.config.dmcAddress.address + '/api/ucenter/role/list'
    const res = await ctx.curl(url, {
      method: 'GET',
      data: {},
      headers: {
        Cookie: 'token=' + token + ';' + 'user_id=' + userId + ';'
      },
      dataType: 'json',
      timeout: 100000,
      rejectUnauthorized: false
    })
    return res
  }
  async getDmcRoleUserList(roleId) {
    const { ctx, config } = this
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    let userList = []
    const url =
      this.ctx.app.config.dmcAddress.address + '/api/ucenter/role/user/list'
    const res = await ctx.curl(url, {
      method: 'GET',
      data: {
        role_id: roleId,
        page: 1,
        page_size: 100000
      },
      headers: {
        Cookie: 'token=' + token + ';' + 'user_id=' + userId + ';'
      },
      dataType: 'json',
      timeout: 100000,
      rejectUnauthorized: false
    })
    userList = userList.concat(res.data.result.data)
    const rolegroupurl =
      this.ctx.app.config.dmcAddress.address + '/api/ucenter/role/group/list'
    const rolegroups = await ctx.curl(rolegroupurl, {
      method: 'GET',
      data: {
        role_id: roleId,
        page: 1,
        page_size: 100000
      },
      headers: {
        Cookie: 'token=' + token + ';' + 'user_id=' + userId + ';'
      },
      dataType: 'json',
      timeout: 100000,
      rejectUnauthorized: false
    })
    if (
      rolegroups &&
      rolegroups.data &&
      rolegroups.data.result &&
      rolegroups.data.result.data
    ) {
      for (let index = 0; index < rolegroups.data.result.data.length; index++) {
        const rolegroup = rolegroups.data.result.data[index]
        const groupusers = await this.getDmcGroupUserList(rolegroup.group_id)
        userList = userList.concat(groupusers)
      }
    }

    return userList
  }
  async getDmcGroupList() {
    const { ctx, config } = this
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    const url = this.ctx.app.config.dmcAddress.address + '/api/group/list'
    const res = await ctx.curl(url, {
      method: 'GET',
      data: {},
      headers: {
        Cookie: 'token=' + token + ';' + 'user_id=' + userId + ';'
      },
      dataType: 'json',
      timeout: 100000,
      rejectUnauthorized: false
    })
    return res
  }
  async getDmcGroupCategory() {
    const { ctx, config } = this
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    const url =
      this.ctx.app.config.dmcAddress.address + '/api/ucenter/group/category'
    const res = await ctx.curl(url, {
      method: 'GET',
      data: {},
      headers: {
        Cookie: 'token=' + token + ';' + 'user_id=' + userId + ';'
      },
      dataType: 'json',
      timeout: 100000,
      rejectUnauthorized: false
    })
    return res.data
  }
  async getDmcGroupUserList(groupId) {
    const { ctx, config } = this
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    const url =
      this.ctx.app.config.dmcAddress.address + '/api/ucenter/group/user/list'
    const res = await ctx.curl(url, {
      method: 'GET',
      data: {
        group_id: groupId,
        page: 1,
        page_size: 100000
      },
      headers: {
        Cookie: 'token=' + token + ';' + 'user_id=' + userId + ';'
      },
      dataType: 'json',
      timeout: 100000,
      rejectUnauthorized: false
    })
    return res.data.result.data
  }
  async getDmcGroupSublistList(groupId) {
    const { ctx, config } = this
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    const url =
      this.ctx.app.config.dmcAddress.address + '/api/ucenter/group/sub_list'
    const res = await ctx.curl(url, {
      method: 'GET',
      data: {
        group_id: groupId
      },
      headers: {
        Cookie: 'token=' + token + ';' + 'user_id=' + userId + ';'
      },
      dataType: 'json',
      timeout: 100000,
      rejectUnauthorized: false
    })
    return res.data
  }
  /**
   * 获取Dmc用户列表
   * @returns
   */
  async getDmcUser() {
    // const dmcUserList = await dmcDb.execute(
    //   'select user_id as userId, name as userName, enterprise_id as entId, card_id as cardId from user_center.account where is_del=0'
    // )
    const dmcUserList = await this.getDmcUserList()
    let res = []
    if (
      dmcUserList &&
      dmcUserList.data &&
      dmcUserList.data.result &&
      dmcUserList.data.result.data &&
      dmcUserList.data.result.data.length
    ) {
      res =
        dmcUserList.data.result.data.map(item => {
          return {
            userId: item.user_id,
            userName: item.name,
            entId: item.enterprise_id,
            cardId: item.card_id
          }
        }) || []
    }
    return res
  }

  /**
   * 检查DMC用户，没有则创建
   */
  async checkDmcUsersAndCreate() {
    const { ctx } = this
    try {
      const dmcUserList = await this.getDmcUser()
      const dmcUserIds = dmcUserList.map(user => {
        return user.userId
      })

      const userData = await ctx.service.user.find({
        userId: {
          $in: dmcUserIds
        }
      })

      const getUserName = userDoc => {
        if(userDoc.cardId) {
          return userDoc.userName + `（${userDoc.cardId}）`
        }else {
          return userDoc.userName
        }
      }

      // 需要创建的用户
      const insertUserDocs = dmcUserList
        .filter(item => {
          const index = userData.findIndex(user => {
            return user.userId === item.userId
          })
          return index === -1
        })
        .map(userDoc => {
          return {
            userId: userDoc.userId,
            entId: userDoc.entId,
            userName: getUserName(userDoc),
            isDmcLogin: 1
          }
        })

      // 需要更新用户信息
      const updateUserDocs = dmcUserList.filter(item => {
        const index = userData.findIndex(user => {
          return user.userId === item.userId
        })
        return index !== -1
      })

      for (let index = 0; index < updateUserDocs.length; index++) {
        const userDoc = updateUserDocs[index]
        await ctx.service.user.updateOne(
          {
            userId: userDoc.userId
          },
          {
            $set: {
              userName: getUserName(userDoc)
            }
          }
        )
      }

      if (insertUserDocs.length) {
        await ctx.service.user.insertMany(insertUserDocs)
      }

      const workspaceData = await ctx.service.workspace.find({
        userId: {
          $in: dmcUserIds
        },
        type: 0
      })

      const insertWorkspaceDocs = dmcUserList
        .filter(item => {
          const index = workspaceData.findIndex(workspace => {
            return workspace.userId === item.userId
          })
          return index === -1
        })
        .map(userDoc => {
          return {
            name: userDoc.userName + '的工作空间',
            userId: userDoc.userId,
            type: 0
          }
        })

      if (!insertWorkspaceDocs.length) {
        return
      }
      // 创建工作空间
      const workspaceRes = await ctx.service.workspace.insertMany(
        insertWorkspaceDocs
      )

      const projectData = await ctx.service.project.find({
        workspaceId: {
          $in: workspaceRes.map(workspace => {
            return workspace.id
          })
        },
        type: 0
      })

      // 需要创建的分组
      const insertProjectDocs = workspaceRes
        .filter(item => {
          const index = projectData.findIndex(project => {
            return project.workspaceId === item.id
          })
          return index === -1
        })
        .map(workspace => {
          return {
            workspaceId: workspace.id,
            name: '未分组',
            type: 0
          }
        })

      // 默认创建一个未分组的项目
      await ctx.service.project.insertMany(insertProjectDocs)
    } catch (error) {
      throw new SeatomException(400, '同步DMC用户列表错误，' + error.message)
    }
  }
}

module.exports = UsersService
