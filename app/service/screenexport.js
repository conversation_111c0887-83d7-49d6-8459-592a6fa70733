const Service = require('egg').Service
const fs = require('fs-extra')
const path = require('path')
const util = require('util')
const pipeline = util.promisify(require('stream').pipeline)
const { copyResource } = require('../extend/utils')
const compressing = require('compressing')
const SeatomException = require('../exception/seatomException')
const {
  resourcePathReplace,
  batchProcess,
  handleFonts
} = require('../extend/screen-utils')

// 同步锁
const copyingCache = {}

class ScreenExportService extends Service {
  async processComponents(componentList, ctx, workspaceId, screenInfo) {
    // 用于去重的 Set
    const uniqueComIds = new Set()
    // 定义处理逻辑
    try {
      await batchProcess(
        componentList,
        100,
        async ([key, component], index) => {
          const sourceType = component.dataConfig.dataResponse.sourceType
          if (sourceType != 'static') {
            const conf = {
              componentId: key,
              workspaceId,
              params: {},
              sourceType
            }
            // 获取静态数据
            // console.log('sourceType',sourceType);
            let getDataResult = []
            try{
              getDataResult = await ctx.service.datastorage.getData(conf)
            } catch (err) {
              ctx.logger.info('getDataResult is error', getDataResult)
            }
            component.staticData = getDataResult
            component.dataConfig.dataResponse.sourceType = 'static'
          }
          const comName = `${component.comName}@${component.version}`
          // 去重：如果已经处理过，则跳过
          if (!uniqueComIds.has(comName)) {
            uniqueComIds.add(comName)
            try {
              // 异步处理资源复制
              const result = await this.handleComponentJs(component)
              if (!result) {
                // 如果返回的为0，要从图层里删掉
                let layers = screenInfo.layers
                if(layers && layers.length > 0) {
                  layers.forEach((layer, index) => {
                    // ctx.logger.info('layer is', layer)
                    if (layer && layer.children){
                      // 如果children存在，是新版图层处理方法
                      // 过滤掉 children 中符合条件的元素
                      // ctx.logger.info('layer.children is ==', layer.children)
                      layer.children = layer.children.filter(
                        child => child.id.indexOf(component.comName) === -1
                      )
                    } else {
                      layers[index] = layer.id.indexOf(component.comName) === -1 ? layer : null;
                    }
                  })
                  // 过滤掉所有为 null 的元素（即被标记为删除的元素）
                  layers = layers.filter(layer => layer !== null);
                }
                
              }
            } catch (err) {
              throw new Error(`组件资源 ${comName} 复制失败: ${err}`)
            }
          }
        }
      )
    } catch (err) {
      ctx.logger.info('processComponents报错', err.message)
      throw new SeatomException(400, err.message)
    }
  }

  async handleStaticScreen() {
    const { ctx, config } = this
    const query = ctx.request.query
    const { screenId, shareToken } = query
    let res
    try {
      res = await ctx.service.preview.getPreviewScreenOrCache({
        screenId,
        shareToken
      })
      if (res.success && res.data && !res.data.isDynamicScreen) {
        let panelScreens = await ctx.service.cacheservice.get(`${res.data.id}_preview_panel_screens`);
        // ctx.logger.info('panelScreens===', panelScreens.length);
  
        // 对 panelScreens 中的每个元素进行 processComponents 处理
        if (panelScreens && panelScreens.length > 0) {
          ctx.logger.info('panelScreens===', panelScreens.length);
          const panelScreenPromises = panelScreens.map(async (panelScreen) => {
            const componentList = Object.entries(panelScreen.components);
            const workspaceId = panelScreen.workspaceId;
            try {
              await this.processComponents(componentList, ctx, workspaceId, panelScreen);
            } catch (err) {
              throw new SeatomException(400, err.message);
            }
            return panelScreen;
          });
  
          // 使用 Promise.all 并行处理
          panelScreens = await Promise.all(panelScreenPromises);
        }
  
        res.data.panelScreens = panelScreens;
      }
    } catch (err) {
      ctx.logger.info('handleStaticScreen from Service error', err.message)
      throw new Error(err)
    }
    const componentList = Object.entries(res.data.components)
    const workspaceId = res.data.workspaceId
    const fonts = res.data.fonts
    console.log('fonts===', fonts);
    try {
      await this.processComponents(componentList, ctx, workspaceId, res.data)
      if(fonts && fonts.length > 0) {
        await handleFonts(fonts, config, ctx)
      }
    } catch (err) {
      throw new SeatomException(400, err.message)
    }
    ctx.logger.info('componentList的长度', componentList.length)
    const finalRes = resourcePathReplace(res)
    return finalRes
  }

  async handleComponentJs(comp) {
    const { ctx, config } = this
    const compKey = `${comp.comName}@${comp.version}`;
    // 如果正在复制这个组件，则等待复制完成
    if (copyingCache[compKey]) {
      ctx.logger.info(`${compKey} 正在复制中，等待结果`);
      await copyingCache[compKey];
      return path.resolve(
        config.resourcePath,
        '../seatom-ssr-next/dist/public/packages',
        compKey
      );
    }

    const compResourceJsPath = path.resolve(
      config.resourcePath,
      `./packages/${compKey}`
    )
    if (!fs.existsSync(compResourceJsPath)) {
      ctx.logger.info(`${compKey}这个组件不存在，删了`)
      // 如果本地不存在这个组件，则删掉图层里的组件不渲染，也不复制直接return
      return 0
    }
    const componentTargetPath = path.resolve(
      config.resourcePath,
      '../seatom-ssr-next/dist/public/packages'
    )
    if (!fs.existsSync(componentTargetPath)) {
      fs.mkdirSync(componentTargetPath, { recursive: true })
    }
    const targetFilePath = path.resolve(
      componentTargetPath,
      `./${comp.comName}@${comp.version}`
    )
    try {
      // ctx.logger.info('targetFilePath is ===', targetFilePath)
      if (fs.existsSync(targetFilePath)) {
        // ctx.logger.info(`文件已存在，跳过复制: ${targetFilePath}`)
        return targetFilePath
      }
      // 保存当前复制操作的 Promise 到缓存中
      copyingCache[compKey] = fs.copy(compResourceJsPath, targetFilePath, { overwrite: false });
      await copyingCache[compKey];
      // 完成后清除缓存
      delete copyingCache[compKey];
      return targetFilePath;
      // ctx.logger.info(`文件复制成功: ${targetFilePath}`)
      return targetFilePath
    } catch (err) {
      ctx.logger.info(`组件JS获取失败: ${comp.comName}@${comp.version}, ${err.message}`)
      // throw new Error(
      //   `组件JS获取失败: ${comp.comName}@${comp.version}，原因: ${err.message}`
      // )
    }
  }

  async handleResource() {
    const { ctx, config } = this
    const { screenId } = ctx.request.query
    const projection = {
      _id: 0,
      __v: 0,
      createdAt: 0,
      updatedAt: 0,
      projectId: 0,
      workspaceId: 0,
      // 协同ID不要导出
      coeditId: 0
    }
    const screenInfo = await ctx.service.screen.findOne(
      { id: screenId },
      projection
    )
    // 获取主大屏是否是模版的信息
    const screenInfoTpl = await ctx.service.screen.findOne(
      { id: screenId },
      projection
    )

    let comInfo = []
    let allScreenIds = [screenId]
    // 获取子屏信息
    const panelScreens = await ctx.service.cacheservice.get(`${screenId}_preview_panel_screens`)
    if(panelScreens && panelScreens.length > 0) {
      const panelIds = panelScreens.map(item =>{
        return item.id
      })
      allScreenIds = [...allScreenIds, ...panelIds]
    }

    // 如果是模版，导出升级后的的组件
    if (screenInfoTpl.isScreentpl) {
      comInfo = await ctx.service.component.exportUpgradeComs(screenId)
    } else {
      // todo导出的大屏组件是引用面板的话，如果该引用面板里面还有别的面板类组件，则这些组件不会被导出。引用的面板大屏也不会导出
      for (const screenId of allScreenIds) {
        const currentScreenComInfo = await ctx.service.component.find({ screenId }, projection)
        comInfo = [...currentScreenComInfo, ...comInfo]
      }
      // comInfo = await ctx.service.component.find({ screenId }, projection)
    }
    const resource = {
      screenInfo,
      comInfo,
      panelScreens
    }
    const imgTargetPath = path.resolve(
      config.resourcePath,
      '../seatom-ssr-next/dist/public/imgs'
    )
    if (!fs.existsSync(imgTargetPath)) {
      fs.mkdirSync(imgTargetPath, { recursive: true })
    }
    copyResource(
      JSON.stringify(resource),
      config.resourcePath,
      '../seatom-ssr-next/dist/public/imgs',
      '',
      screenId
    )
  }

  async handleScreenExport() {
    const { ctx, config } = this
    const { screenId, shareToken } = ctx.request.query
    const envList = [
      { name: 'local', PORT: 3000 },
      { name: 'test', PORT: 3031 },
      { name: 'prod', PORT: 3032 },
      { name: 'prev', PORT: 3032 },
    ]
    const env = envList.find(item => item.name === this.config.env);
    const port = env ? env.PORT : 3032;
    this.ctx.logger.info(`PORT is ${port}`)
    // 删除dist文件下的public文件夹
    const targetPath = path.resolve(
      config.resourcePath,
      '../seatom-ssr-next/dist/public'
    )
    if (fs.existsSync(targetPath)) {
      fs.rmdir(targetPath, { recursive: true })
    }
    let resultHtml
    try {
      resultHtml = await this.ctx.curl(
        `http://127.0.0.1:${port}/api/exportHtml`,
        {
          method: 'GET',
          data: {
            shareToken,
            screenId
          },
          dataType: 'text'
        }
      )
    } catch (err) {
      console.log('err', err)
      throw new SeatomException(400, `ssr接口通讯失败: ${err.message}`)
    }
    // console.log('resultHtml', resultHtml);
    // 把html放到ssr的dist里
    if (resultHtml) {
      console.log('config.resourcePath', config.resourcePath)
      const ssrDir = path.resolve(
        config.resourcePath,
        '../seatom-ssr-next/dist'
      )
      // ctx.logger.info('ssrDir', ssrDir)
      if (!fs.existsSync(ssrDir)) {
        throw new Error(
          '未找到ssr下的dist目录，请在docker内进入ssr后运行npm run generate'
        )
      }
      const ssrHtmlPath = path.resolve(ssrDir, `./html/screen.html`)
      fs.writeFileSync(ssrHtmlPath, resultHtml.data, 'utf-8')
    }
    // 获取图片资源+将组件和图片放到指定目录
    await this.handleResource()

    // 压缩目录返回
    const compressPath = path.resolve(
      config.resourcePath,
      '../seatom-ssr-next/dist'
    )
    const outputPath = path.resolve(
      config.resourcePath,
      './screenExportTpl/dist.tgz'
    )
    await compressing.tgz.compressDir(compressPath, outputPath)
    return outputPath
  }
}

module.exports = ScreenExportService
