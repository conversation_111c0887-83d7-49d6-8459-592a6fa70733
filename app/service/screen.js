'use strict'
const Service = require('egg').Service
const {
  getCommonProjection,
  copyResource,
  getLoggerModel
} = require('../extend/utils')
const { EXPORT_COMTYPE, DASHBOARD_CODES } = require('../extend/constant')
class ScreenService extends Service {
  async count(filter) {
    if(!filter.isHistoryScreen){
      // 如果filter里没有传isHistoryScreen，则默认为false
      filter = {
        ...filter,
        $or: [
          { isHistoryScreen: false },
          { isHistoryScreen: { $exists: false } }
        ]
      }
    }
    const res = await this.ctx.model.Screen.count(filter)
    return res
  }
  async find(filter, projection, options) {
    if(!filter.isHistoryScreen){
      // 如果filter里没有传isHistoryScreen，则默认为false
      filter = {
        ...filter,
        $or: [
          { isHistoryScreen: false },
          { isHistoryScreen: { $exists: false } }
        ]
      }
    }
    const res = await this.ctx.model.Screen.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async paginfind(filter, projection, page, pageSize, sortField) {
    const start = (page - 1) * pageSize
    if(!filter.isHistoryScreen){
      // 如果filter里没有传isHistoryScreen，则默认为false
      filter = {
        ...filter,
        $or: [
          { isHistoryScreen: false },
          { isHistoryScreen: { $exists: false } }
        ]
      }
    }
    // console.log(filter, projection, page, pageSize, start)
    const res = await this.ctx.model.Screen.find(
      filter,
      getCommonProjection(projection)
    )
      .sort(`-${sortField}`)
      .skip(start)
      .limit(pageSize)
    // console.log(res)
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Screen.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOneAndUpdate(conditions, doc, options) {
    const res = await this.ctx.model.Screen.findOneAndUpdate(
      conditions,
      doc,
      options
    )
    return res
  }
  async create(params) {
    const res = await this.ctx.model.Screen.create(params)
    return res
  }
  async delete(params) {
    await this.ctx.service.component.deleteMany({ screenId: params.id })
    await this.ctx.service.filter.deleteMany({ screenId: params.id })
    await this.ctx.service.historyscreen.deleteMany({ screenId: params.id })
    const res = await this.ctx.model.Screen.deleteMany(params)
    
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Screen.updateMany(filter, params, options)
    return res
  }
  async updateOne(filter, params, options) {
    const res = await this.ctx.model.Screen.updateMany(filter, params, options)
    return res
  }
  async updateMany(filter, params, options) {
    const res = await this.ctx.model.Screen.updateMany(filter, params, options)
    return res
  }
  async copy(params) {
    const { screenIdOrigin, name, projectId, t, isHistoryScreen } = params
    const workspaceId = params.isScreentpl ? 0 : params.workspaceId
    const projection = {
      id: 0,
      name: 0,
      workspaceId: 0,
      projectId: 0,
      updatedAt: 0,
      createdAt: 0,
      layerTree: 0,
      _id: 0,
      __v: 0,
      // 协同ID不要复制
      coeditId: 0,
      shareCollection: 0
    }
    // 大屏原数据
    let screenInfoOrigin = await this.ctx.service.screen.findOne(
      { id: screenIdOrigin },
      projection
    )
    const screenInfoNow = await this.ctx.service.screen.create({
      workspaceId,
      projectId,
      name,
      type: screenInfoOrigin.type,
      screenType: screenInfoOrigin.screenType,
      isScreentpl: !!params.isScreentpl,
      isHistoryScreen: !!params.isHistoryScreen
    })

    const comParams = {
      screenIdOrigin,
      copyScreenId: screenInfoNow.id,
      isScreentpl: !!params.isScreentpl,
      screenName: screenInfoNow.name,
      screenIdMapping: params.screenIdMapping,
      type: 'copy',
      t
    }
    // 替换模版里面文件路径
    if (params.isScreentpl) {
      const res = copyResource(
        JSON.stringify(screenInfoOrigin),
        this.config.resourcePath,
        'system',
        'tpl'
      )
      screenInfoOrigin = JSON.parse(res)
    }
    const layerTree = await this.ctx.service.component.bacthCopy(comParams)
    screenInfoOrigin.layerTree = layerTree
    screenInfoOrigin.isScreentpl = !!params.isScreentpl
    screenInfoOrigin.isHistoryScreen = isHistoryScreen
    const newScreenInfo = await this.ctx.service.screen.findOneAndUpdate(
      { id: screenInfoNow.id },
      screenInfoOrigin,
      {
        new: true
      }
    )
    // 生成模版数据
    if (params.isScreentpl && !!params.isCreateTpl) {
      const data = await this.ctx.service.screentpl.create({
        name: screenInfoNow.name,
        type: screenInfoNow.type,
        level: screenInfoNow.level,
        description: `${screenInfoNow.name}模版`,
        screenId: screenInfoNow.id,
        tag: params.tag
      })
      return data
    }
    // const data = await this.ctx.service.screen.findOne({ id: screenInfoNow.id })
    return newScreenInfo
  }

  /**
   * 通过 screenId 获取 userId
   * @param {Number} screenId
   * @return
   */
  async getUserIdByScreenId(screenId) {
    const { ctx } = this
    const key = `getUserIdByScreenId-${screenId}`

    const userId = await ctx.service.cacheservice.get(key)
    if (userId) {
      return userId
    }

    const screen = await ctx.service.screen.findOne({
      id: screenId
    })
    const workspace = await ctx.service.workspace.findOne({
      id: screen.workspaceId
    })
    ctx.service.cacheservice.set(key, workspace.userId, 86400)
    return workspace.userId
  }

  /**
   * 检查该用户的大屏删除权限
   * @param {*} screenId
   * @return
   */
  async checkScreenDeletePermissionByScreenId(screenId) {
    const { ctx } = this
    const userId = ctx.header.sysuserid
    const screenUserId = await ctx.service.screen.getUserIdByScreenId(screenId)

    if (userId !== screenUserId) {
      ctx.logger.info(
        getLoggerModel({
          message: '删除大屏权限不足',
          data: {
            screenId,
            userId
          }
        })
      )

      return {
        success: false,
        res: {
          message: '权限不足',
          code: 402
        }
      }
    }

    return {
      success: true,
      res: null
    }
  }

  /**
   * 检查该用户的大屏编辑权限
   * @param {*} screenId
   * @return
   */
  async checkScreenEditingPermissionByScreenId({ screenId }) {
    const { ctx } = this
    const userId = ctx.header.sysuserid
    const screen = await ctx.service.screen.findOne({
      id: screenId
    })
    // 如果是模版大屏则不需要校验
    if (screen.isScreentpl || screen.screenType == 'child') {
      return {
        success: true,
        res: null
      }
    }
    const screenUserId = await ctx.service.screen.getUserIdByScreenId(screenId)

    if (userId !== screenUserId) {
      // const screen = await ctx.service.screen.findOne({
      //   id: screenId
      // })
      // 如果是动态面板，则找到父级ID
      if (screen.isDynamicScreen) {
        screenId = Number(screen.parentId)
      }

      // 根据 coeditScreenId 找出协同编辑列表
      const list = await ctx.service.screencoedit.find({
        coeditScreenId: screenId
      })

      const index = list.findIndex(item => {
        return item.coeditUserId === userId || item.createUserId === userId
      })

      // 如果用户没有权限编辑，返回错误
      if (index === -1) {
        const message = '大屏不在此用户下'

        ctx.logger.info(
          getLoggerModel({
            message,
            data: {
              screenId,
              userId
            }
          })
        )

        return {
          success: false,
          res: {
            message,
            code: 402
          }
        }
      }

      // 协作者才有权限更新
      if (
        list[index].coeditUserId === userId &&
        list[index].coeditRole === 'collaborators'
      ) {
        return {
          success: true,
          res: {
            currentCoedit: list[index]
          }
        }
      }

      // 查看者没有权限编辑更新
      if (
        list[index].coeditUserId === userId &&
        list[index].coeditRole !== 'collaborators'
      ) {
        const message = '查看者没有编辑权限，请联系大屏创建者'
        ctx.logger.info(
          getLoggerModel({
            message,
            data: {
              screenId,
              userId
            }
          })
        )

        return {
          success: false,
          res: {
            message,
            code: 402
          }
        }
      }
    }

    return {
      success: true,
      res: null
    }
  }

  /**
   * 检查场景和页面是否删除
   * @param {*} param0
   * @return
   */
  async checkSceneAndPageIsDel({ screenId, screenInfo, sceneId, pageId }) {
    const { ctx } = this
    if (!screenInfo) {
      screenInfo = await ctx.service.screen.findOne({ id: screenId })
    }

    if (screenInfo.screenType === 'scene') {
      const sceneConfig =
        (screenInfo &&
          screenInfo.sceneConfig &&
          screenInfo.sceneConfig.toObject()) ||
        []

      // 检查场景是否被删除
      const currScene =
        sceneConfig.find(item => {
          return item.sceneId === sceneId
        }) || {}

      if (!currScene) {
        const message = '该屏已删除'
        ctx.logger.info(
          getLoggerModel({
            message: 'checkSceneAndPageIsDel' + message,
            data: {
              screenId: screenInfo.id,
              sceneId
            }
          })
        )

        return {
          success: false,
          errorRes: {
            message,
            code: 400
          }
        }
      }

      // 检查场景页面是否被删除
      const currPage = (currScene.pageList || []).find(page => {
        return page.pageId === pageId
      })

      if (!!pageId && !currPage) {
        const message = '该页面已删除'
        ctx.logger.info(
          getLoggerModel({
            message: 'checkSceneAndPageIsDel' + message,
            data: {
              screenId: screenInfo.id,
              sceneId,
              pageId
            }
          })
        )

        return {
          success: false,
          errorRes: {
            message,
            code: 400
          }
        }
      }
    }

    return {
      success: true,
      errorRes: null
    }
  }
  async getscreenlinkAge(screenId) {
    const { ctx } = this
    const comInfo = await ctx.service.component.findcomList(
      {
        screenId
      },
      {
        id: 1,
        interactionConfig: 1,
        comType: 1,
        config: 1
      }
    )
    const panelcominfo = comInfo.filter(item => {
      return EXPORT_COMTYPE.COPYCOMTYPE.indexOf(item.comType) !== -1
    })
    const screenIds = []
    if (panelcominfo) {
      // 获取面板大屏id
      for (let index = 0; index < panelcominfo.length; index++) {
        const element = panelcominfo[index]
        if (element.config.screens && element.config.screens.length) {
          screenIds.push(...element.config.screens.map(item => item.id))
        }
      }
    }
    const panelcomInfolist = await ctx.service.component.findcomList(
      {
        screenId: { $in: screenIds }
      },
      {
        id: 1,
        interactionConfig: 1,
        comType: 1
      }
    )
    const alllinkage = comInfo.concat(panelcomInfolist)
    const data = []
    for (let index = 0; index < alllinkage.length; index++) {
      const linkAge = alllinkage[index].interactionConfig.linkAge.filter(
        item => {
          return item.immediate
        }
      )
      data.push({ id: alllinkage[index].id, linkAge })
    }
    return data
  }
}

module.exports = ScreenService
