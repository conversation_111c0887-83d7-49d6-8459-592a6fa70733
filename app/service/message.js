/*
 * @Description: 消息Service
 * @Date: 2022-11-07 10:28:13
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */
const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class MessagesService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Message.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Message.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Message.create(params, options)
    return res
  }
  async insertMany(params, options) {
    const res = await this.ctx.model.Message.insertMany(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Message.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Message.update(filter, params, options)
    return res
  }
}

module.exports = MessagesService
