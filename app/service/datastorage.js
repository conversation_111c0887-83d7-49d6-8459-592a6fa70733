const Service = require('egg').Service
const {
  getCommonProjection,
  initDrill,
  drilldownByPath
} = require('../extend/utils')
const dbconns = require('../helper/datasource')
const SeatomException = require('../exception/seatomException')
const { ERROR_CODES } = require('../extend/constant')

class DataStorageService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Datastorage.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params) {
    const res = await this.ctx.model.Datastorage.create(params)
    return res
  }
  async insertMany(docs, options) {
    const res = await this.ctx.model.Datastorage.insertMany(docs, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Datastorage.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Datastorage.updateOne(
      filter,
      params,
      options
    )
    return res
  }

  async findOne(filter, projection, options) {
    const res = await this.ctx.model.Datastorage.findOne(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }

  async getDbList(body) {
    const type = body.type || 'static'
    const dbconn = new dbconns[type](body)
    const res = await dbconn.dblist()
    if (type == 'oracle' || type == 'dmdb') {
      return res.rows.map(v => {
        return v[0]
      })
    }
    // to_do: 是否需要把mysql的几个系统库给过滤掉
    return res.map(function (val) {
      // return val.schema_name;
      // 好像有部分mysql返回的key不是schema_name,所以用取keys的方式
      return val[Object.keys(res[0])[0]]
    })
  }

  async getData(conf) {
    let config = conf
    let component = await this.ctx.model.Component.findOne({
      id: config.componentId
    })
    if (!component) {
      throw new SeatomException(
        ERROR_CODES.COMPONENT_NOT_EXIST,
        'component not exists'
      )
    }
    let type = config.type
      ? config.type
      : component.dataConfig.dataResponse.sourceType
    let isdrillDown =
      config.params &&
      config.params._isDrill &&
      component.interactionConfig &&
      component.interactionConfig.drillDown &&
      component.interactionConfig.drillDown.length > 0 &&
      component.interactionConfig.drillDown[0].drillType &&
      component.interactionConfig.drillDown[0].drillType == 'server'
    const drillDown = component.interactionConfig.drillDown[0]
    let drillPath = []
    if (config.params) {
      drillPath = config.params._drillPath || ['']
    } else {
      drillPath = ['']
    }
    if (type === 'datacontainer') {
      const comId =
        component.dataConfig.dataResponse.source[type].data.dataContainerComId
      component = await this.ctx.model.Component.findOne({
        id: comId
      })
      // 数据容器数据源修改参数
      config.componentId = comId
      type = component.dataConfig.dataResponse.sourceType
      isdrillDown = false
    }

    if (type === 'static') {
      return component.staticData
    } else {
      const componentConfig =
        component.dataConfig.dataResponse.source[type].data
      // dmc不需要先新建数据源
      if (!componentConfig.sourceId && type != 'dmc') {
        throw new SeatomException(
          ERROR_CODES.COMPONENT_HAS_NO_SUCH_DATASOURCE,
          'component has no datasource of ' + type
        )
      }
      const baseDatastorage = await this.ctx.model.Datastorage.findOne({
        id: componentConfig.sourceId || 0
      })
      const baseConfig = baseDatastorage ? baseDatastorage.config : {}
      // 对接仪表盘数据start
      if (type === 'dashboard') {
        const dashboardData = await this.ctx.service.datadashboard.getChartData(
          Object.assign(config, baseConfig),
          componentConfig.isLimit,
          componentConfig.limitNum
        )
        return dashboardData
      }
      // 对接仪表盘数据end
      // 数据筛选和后端联动逻辑
      const dbconn = new dbconns[type](Object.assign(config, baseConfig), this)
      if (config.params && config.params.temporary && componentConfig.where) {
        if (componentConfig.where.enable) {
          // componentConfig.where.whereCondition = componentConfig.where.whereCondition.concat(config.params.whereCondition); // 后端联动和数据筛选进行merge
          componentConfig.where.whereCondition = config.params.whereCondition // 后端联动和数据筛选进行merge
        } else {
          componentConfig.where.whereCondition = config.params.whereCondition
          componentConfig.where.enable = true
        }
      }
      let res = []
      try {
        res = await dbconn.getData(baseConfig, componentConfig, config)
      } catch (error) {
        res = { message: error.message, code: 801 }
        return res
      }

      if (type === 'json') {
        return isdrillDown ? initDrill(res, drillDown, drillPath) : res
      }
      if (type === 'api') {
        return res
      }
      if (type === 'postgresql') {
        return isdrillDown
          ? initDrill(res.rows, drillDown, drillPath)
          : res.rows
      }

      // 可以截取返回值的数据源
      if (type == 'oracle' || type === 'dmdb') {
        return res.rows.map(v => {
          return res.metaData.reduce((p, key, i) => {
            p[key.name] = v[i]
            return p
          }, {})
        })
      }
      if (componentConfig.isLimit) {
        return isdrillDown
          ? initDrill(
              res.slice(0, componentConfig.limitNum),
              drillDown,
              drillPath
            )
          : res.slice(0, componentConfig.limitNum)
      }
      return isdrillDown
        ? initDrill(res.slice(0, 5000), drillDown, drillPath)
        : res.slice(0, 5000)
    }
  }

  async getFieldList(config) {
    if (config.componentId) {
      const component = await this.ctx.model.Component.findOne({
        id: config.componentId
      })
      if (!component) {
        throw new SeatomException(
          ERROR_CODES.COMPONENT_NOT_EXIST,
          'component not exists'
        )
      }
      const type = config.type
        ? config.type
        : component.dataConfig.dataResponse.sourceType
      if (type === 'static') {
        return component.staticData
      } else {
        const componentConfig =
          component.dataConfig.dataResponse.source[type].data
        if (!componentConfig.sourceId && type != 'dmc') {
          throw new SeatomException(
            ERROR_CODES.COMPONENT_HAS_NO_SUCH_DATASOURCE,
            'component has no datasource of ' + type
          )
        }
        const baseDatastorage = await this.ctx.model.Datastorage.findOne({
          id: componentConfig.sourceId || 0
        })
        const baseConfig = baseDatastorage ? baseDatastorage.config : {}
        config = Object.assign(config, baseConfig)
      }
    }
    const dbconn = new dbconns[config.type](config, this)
    const res = await dbconn.getFieldList(config)
    return res
  }

  async getTreeList(config) {
    if (config.componentId) {
      const component = await this.ctx.model.Component.findOne({
        id: config.componentId
      })
      if (!component) {
        throw new SeatomException(
          ERROR_CODES.COMPONENT_NOT_EXIST,
          'component not exists'
        )
      }
      const type = config.type
        ? config.type
        : component.dataConfig.dataResponse.sourceType
      if (type === 'static' || type == 'dmc') {
        return ''
      } else {
        const componentConfig =
          component.dataConfig.dataResponse.source[type].data
        // dmc不需要先新建数据源
        if (!componentConfig.sourceId) {
          throw new SeatomException(
            ERROR_CODES.COMPONENT_HAS_NO_SUCH_DATASOURCE,
            'component has no datasource of ' + type
          )
        }
        const baseDatastorage = await this.ctx.model.Datastorage.findOne({
          id: componentConfig.sourceId || 0
        })
        const baseConfig = baseDatastorage ? baseDatastorage.config : {}
        config = Object.assign(config, baseConfig)
      }
    }
    const dbconn = new dbconns[config.type](config, this)
    let res = dbconn.getTreeList(config)
    if (config.type == 'oracle') {
      res = await dbconn.getTreeList(config)
      return res.rows.map(v => {
        return res.metaData.reduce((p, key, i) => {
          p['tablename'] = v[i]
          return p
        }, {})
      })
    }
    return res
  }

  async getFolderTbList(config) {
    const dbconn = new dbconns[config.type](config, this)
    const res = dbconn.getFolderTbList(config)
    return res
  }

  async searchTb(config) {
    const dbconn = new dbconns[config.type](config, this)
    const res = dbconn.searchTb(config)
    return res
  }

  async preview(config) {
    let prevConfig = null // 创建数据源之前先进行预览
    if (config && config.filePath) {
      prevConfig = {
        filePath: config.filePath
      }
      const dbconn = new dbconns[config.type](config, this)
      const res = dbconn.preview(prevConfig || baseDatastorage.config)
      return res
    }
    const baseDatastorage = await this.ctx.model.Datastorage.findOne({
      id: config.storageId || 0,
      type: config.type
    })
    if (!baseDatastorage) {
      throw new SeatomException(
        ERROR_CODES.DATASTORAGE_NOT_EXIST,
        'datastorage not exists'
      )
    }
    const dbconn = new dbconns[config.type](config, this)
    const res = dbconn.preview(baseDatastorage.config)
    return res
  }

  async getFunctionList(config) {
    const dbconn = new dbconns[config.type](config, this)
    const res = await dbconn.getFunctionList()
    return res
  }
}

module.exports = DataStorageService
