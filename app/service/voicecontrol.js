const Service = require('egg').Service
const _ = require('lodash')
const { getCommonProjection, deletefilter } = require('../extend/utils')
const { EXPORT_COMTYPE } = require('../extend/constant')
const parseConfig = require('../extend/parse-config')
class VoicecontrolService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Voicecontrol.find(
      deletefilter(filter),
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Voicecontrol.findOne(
      deletefilter(conditions),
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Voicecontrol.create(params, options)
    return res
  }
  async insertMany(docs, options) {
    const res = await this.ctx.model.Voicecontrol.insertMany(docs, options)
    return res
  }
  async deleteOne(params, options) {
    const res = await this.ctx.model.Voicecontrol.deleteOne(params, options)
    return res
  }
  async deleteMany(params, options, deleteIds) {
    const res = await this.ctx.model.Voicecontrol.deleteMany(params, options)
    return res
  }
  async updateMany(params, options) {
    const res = await this.ctx.model.Voicecontrol.updateMany(params, options)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Voicecontrol.update(
      filter,
      params,
      options
    )
    return res
  }
}

module.exports = VoicecontrolService
