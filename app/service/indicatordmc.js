const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class IndicatorDmcService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Indicatordmc.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Indicatordmc.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params) {
    const res = await this.ctx.model.Indicatordmc.create(params)
    return res
  }
  async delete(params, options) {
    const res = await this.ctx.model.Indicatordmc.deleteMany(params, options)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Indicatordmc.update(
      filter,
      params,
      options
    )
    return res
  }
}

module.exports = IndicatorDmcService
