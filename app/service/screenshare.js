/*
 * @Description: ScreenShareService
 * @Date: 2022-09-13 16:22:52
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */
const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')

class ScreenShareService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Screenshare.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Screenshare.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Screenshare.create(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Screenshare.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Screenshare.update(filter, params, options)
    return res
  }
  /**
   * 通过 ShareToken 获取大屏发布页
   * @param {number} screenId 大屏ID
   * @param {string} shareToken shareToken
   * @returns
   */
  async getScreenShareByShareToken({ screenId, shareToken }) {
    const { ctx } = this
    const projection = {
      shareToken: 0,
      screenId: 0
    }
    const filter = {}
    if (screenId) {
      filter.screenId = screenId
    }
    if (shareToken) {
      filter.shareToken = shareToken
      delete projection.screenId
    }

    let data = await ctx.service.screenshare.findOne(filter)
    if (!data) {
      return null
    }

    const id = screenId || data.screenId
    const screenInfo = await ctx.service.screen.findOne({ id })
    const workspaceInfo = await ctx.service.workspace.findOne({
      id: screenInfo.workspaceId
    })
    let res = {
      isPublic: data.isPublic,
      needPassword: data.needPassword,
      needLoading: data.needLoading,
      sharePassword: data.sharePassword,
      shareUrl: data.shareUrl,
      screenId: data.screenId,
      workspaceId: screenInfo.workspaceId || '',
      userId: workspaceInfo.userId || '',
      enableCache: data.enableCache || false,
      needAuth: data.needAuth || false,
      renderType: data.renderType || 'static'
    }
    return res
  }
}

module.exports = ScreenShareService
