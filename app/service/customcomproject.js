const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class CustomComProjectsService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Customcomproject.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Customcomproject.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Customcomproject.create(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Customcomproject.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Customcomproject.update(
      filter,
      params,
      options
    )
    return res
  }
}

module.exports = CustomComProjectsService
