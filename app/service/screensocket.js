/*
 * @Description: ScreenSocketService
 * @Date: 2022-09-13 16:22:52
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */
const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')

class ScreenSocketService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Screensocket.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Screensocket.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Screensocket.create(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Screensocket.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Screensocket.update(
      filter,
      params,
      options
    )
    return res
  }

  /**
   * 通过 screenId 获取 Screensocket
   * @param {number} param0 screenId
   * @returns
   */
  async getScreensocketByScreenId({ screenId }) {
    const { ctx } = this
    const screensocketData = await ctx.service.screensocket.findOne({
      screenId
    })
    if (!screensocketData) {
      return {
        isConnect: false,
        screenId,
        password: '',
        isConnect: false,
        socketidList: []
      }
    }

    return screensocketData
  }
}

module.exports = ScreenSocketService
