/*
 * @Description: db0
 * @Date: 2022-11-11 16:56:29
 * @Author: chen<PERSON><PERSON>
 * @LastEditors: chenxingyu
 */
const Service = require('egg').Service
const crypto = require('crypto')
class CacheService extends Service {
  // 处理redis存储key的逻辑
  async keyProcess(sql, id) {
    const key = 'sql' + id + crypto.createHash('md5').update(sql).digest('hex')
    return key
  }

  // 用LRU进行set，存储获取的数据到redis，并且添加时间戳到有序集合
  async lruSet(key, value) {
    const { redis } = this.app
    value = JSON.stringify(value)
    await redis.get('db0').set(key, value) //存储到db0里的key
    await redis.get('db0').zadd('timecache', Date.now(), key) // 在有序集合里添加时间戳
  }

  // 用LRU进行get，需要更新有序集合的时间戳
  async lruGet(key) {
    const { redis } = this.app
    const result = await redis.get('db0').get(key) //读取数据
    await redis.get('db0').zadd('timecache', Date.now(), key) // 在有序集合里更新时间戳
    return JSON.parse(result)
  }

  // 删除redis缓存的同时清除存储的有序集合里的数据
  async collectDel(key) {
    const { redis } = this.app
    const result = await redis.get('db0').zrem('timecache', key)
    return result
  }

  // 删除指定key的内容
  async del(key) {
    const { redis } = this.app
    let data = await redis.get('db0').del(key)
    if (!data) return
    return data
  }

  async zscore(key) {
    const { redis } = this.app
    let result = await redis.get('db0').zscore('timecache', key)
    return result
  }

  // 获取timecache的keys
  async getKeys() {
    const { redis } = this.app
    const result = await redis.get('db0').zrange('timecache', 0, -1)
    return result
  }

  async getTTL(key){
    const { redis } = this.app
    const result = await redis.get('db0').ttl(key)
    return result
  }

  async set(key, value, seconds) {
    const { redis } = this.app
    value = JSON.stringify(value)
    if (!seconds) {
      await redis.get('db0').set(key, value)
    } else {
      await redis.get('db0').set(key, value, 'EX', seconds)
    }
  }

  async get(key) {
    const { redis } = this.app
    let data = await redis.get('db0').get(key)
    if (!data) return
    data = JSON.parse(data)
    return data
  }

  async hset(objName, key, value, seconds) {
    const { redis } = this.app
    value = JSON.stringify(value)
    if (!seconds) {
      await redis.get('db0').hset(objName, key, value)
    } else {
      await redis.get('db0').hset(objName, key, value, 'EX', seconds)
    }
  }

  async hget(objName, key) {
    const { redis } = this.app
    let data = await redis.get('db0').hget(objName, key)
    if (!data) return
    data = JSON.parse(data)
    return data
  }

  async hgetall(objName) {
    const { redis } = this.app
    let data = await redis.get('db0').hgetall(objName)
    if (!data) return
    return data
  }

  async hdel(objName, key) {
    const { redis } = this.app
    let data = await redis.get('db0').hdel(objName, key)
    if (!data) return
    return data
  }

}

module.exports = CacheService
