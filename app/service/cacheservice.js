/*
 * @Description: db0
 * @Date: 2022-11-11 16:56:29
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */
const Service = require('egg').Service
class CacheService extends Service {
  async set(key, value, seconds) {
    const { redis } = this.app
    value = JSON.stringify(value)
    if (!seconds) {
      await redis.get('db0').set(key, value)
    } else {
      await redis.get('db0').set(key, value, 'EX', seconds)
    }
  }

  async get(key) {
    const { redis } = this.app
    let data = await redis.get('db0').get(key)
    if (!data) return
    data = JSON.parse(data)
    return data
  }

  async hset(objName, key, value, seconds) {
    const { redis } = this.app
    value = JSON.stringify(value)
    if (!seconds) {
      await redis.get('db0').hset(objName, key, value)
    } else {
      await redis.get('db0').hset(objName, key, value, 'EX', seconds)
    }
  }

  async hget(objName, key) {
    const { redis } = this.app
    let data = await redis.get('db0').hget(objName, key)
    if (!data) return
    data = JSON.parse(data)
    return data
  }

  async hgetall(objName) {
    const { redis } = this.app
    let data = await redis.get('db0').hgetall(objName)
    if (!data) return
    return data
  }

  async hdel(objName, key) {
    const { redis } = this.app
    let data = await redis.get('db0').hdel(objName, key)
    if (!data) return
    return data
  }
}

module.exports = CacheService
