const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class CoverService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Cover.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Cover.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Cover.create(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Cover.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Cover.update(filter, params, options)
    return res
  }
}

module.exports = CoverService
