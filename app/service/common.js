const Service = require('egg').Service
const _ = require('lodash')
const fs = require('fs-extra')
const path = require('path')
const crypto = require('crypto')
const jschardet = require('jschardet')
const iconv = require('iconv-lite')
const compressing = require('compressing')
const SVGSpriter = require('svg-sprite')
const { uuid } = require('../extend/utils')
const { optimize } = require('svgo') // Path of the svg file
const getSvgOptionsByIconFontContent = function (string = '') {
  const options = []
  const rxp = /glyph-name=".*?"/g
  const namesArr = string.match(rxp)
  let itemLen, fileName
  namesArr.forEach(item => {
    itemLen = item.length
    fileName = item.slice(12, itemLen - 1)
    options.push({
      value: fileName,
      label: fileName
    })
  })
  return options
}
function formatIconFontSvg(string = '') {
  const svgNameOptions = getSvgOptionsByIconFontContent(string)
  const rxp = /d=".*?"/g
  let svgArr = string.match(rxp)
  let itemName, itemSvg
  if (!svgArr) {
    svgArr = []
  }
  // 匹配到的第一项为 id 删除
  svgArr.shift()
  let svgList = []
  for (const i in svgArr) {
    itemName = svgNameOptions[i].value // 当前图片的名字
    let content = `<svg viewBox="0 0 1024 1024" width="32" height="32" transform="scale(1, -1)" xmlns="http://www.w3.org/2000/svg">`
    itemSvg = `<path ${svgArr[i]} />`
    content += itemSvg
    content += '</svg>'
    svgList.push({
      content,
      itemName
    })
  }
  return svgList
}
class CommonsService extends Service {
  async upload(fileData) {
    const { config } = this
    const filefolder = fileData.filefolder
    const file = fileData.file
    const isModelFile = fileData.isModelFile
    const md5data = fs.readFileSync(file.filepath)
    if (file.mimeType === 'text/csv') {
      const encodeType = jschardet.detect(md5data)
      if (encodeType && encodeType.encoding != 'UTF-8') {
        const fileInfo = iconv.decode(Buffer.from(md5data), encodeType.encoding)
        fs.writeFileSync(file.filepath, fileInfo, { encoding: 'utf-8' })
      }
    }
    let fileName = await crypto
      .createHash('md5')
      .update(md5data, 'utf8')
      .digest('base64')
    fileName = fileName.replace(/\//g, '_')
    fileName = fileName.replace(/\+/g, '_')
    const fileNameArr = file.filename.split('.')
    const fileType = fileNameArr[fileNameArr.length - 1] // 获取文件格式
    let filePath, newUrl
    filePath = path.resolve(
      config.resourcePath,
      `./${filefolder}/${fileName}.${fileType}`
    )
    newUrl = `/public/${filefolder}/${fileName}.${fileType}`
    if (!fs.existsSync(filePath)) {
      await fs.moveSync(file.filepath, filePath)
    }
    if (isModelFile) {
      const uncompressName = file.filename.split('.')[0]
      const uncompressPath = path.resolve(config.resourcePath, `./packages`)
      await compressing.tgz.uncompress(filePath, uncompressPath)
      newUrl = `/public/packages/${uncompressName}`
    }
    const res = {
      url: newUrl, //返回文件的路径
      fileName: file.filename,
      filePath: filePath
    }
    return res
  }
  // 上传合图后切割
  async uploadMergeSvg(fileData) {
    const { config } = this
    const filefolder = fileData.filefolder
    const file = fileData.file
    const svgdata = fs.readFileSync(file.filepath, 'utf-8')
    const svgList = formatIconFontSvg(svgdata)
    let resList = []
    for (let index = 0; index < svgList.length; index++) {
      const md5data = svgList[index].content
      let fileName = await crypto
        .createHash('md5')
        .update(md5data, 'utf8')
        .digest('base64')

      fileName = fileName.replace(/\//g, '_')
      fileName = fileName.replace(/\+/g, '_')
      const fileNameArr = file.filename.split('.')
      const fileType = fileNameArr[fileNameArr.length - 1] // 获取文件格式
      let filePath, newUrl
      filePath = path.resolve(
        config.resourcePath,
        `./${filefolder}/${fileName}.${fileType}`
      )
      newUrl = `/public/${filefolder}/${fileName}.${fileType}`
      if (!fs.existsSync(filePath)) {
        fs.writeFileSync(filePath, md5data)
        // await fs.moveSync(file.filepath, filePath)
      }
      const res = {
        resourceId: uuid(),
        resourceUrl: newUrl,
        resourceName: svgList[index].itemName,
        description: 'merge'
      }
      resList.push(res)
    }
    return resList
  }
  // 合并svg图片，并删除fill属性
  async mergeSvg(resSVG) {
    const { config } = this
    const filefolder = path.resolve(config.resourcePath, `./system/screenIcons`)
    const svgFiles = resSVG.iconList
    const spriteName = `screen_${resSVG.screenId}_icon`
    const changeColor = resSVG.changeColor || false
    const resourceUrlList = resSVG.resourceUrlList
    let resourceUrl
    if (resourceUrlList.length === 1) {
      resourceUrl = resourceUrlList[0]
    }
    const svgconfig = {
      dest: filefolder,
      mode: {
        symbol: {
          sprite: spriteName,
          inline: false // Prepare for inline embedding
        }, // Create a «symbol» sprite
        view: true
      }
    }
    try {
      const spriter = new SVGSpriter(svgconfig)
      // Add SVG source files — the manual way ...
      for (let index = 0; index < svgFiles.length; index++) {
        const svgItem = svgFiles[index]
        const svgPath = svgItem.split('/public/')[1]
        const svgNameArr = svgItem.split('/')

        const filePath = path.resolve(config.resourcePath, `./${svgPath}`)
        const svgoData = fs.readFileSync(filePath, 'utf-8')
        let svgData
        if (changeColor && svgItem === resourceUrl) {
          const res = optimize(svgoData, {
            path: filePath,
            plugins: [
              {
                name: 'removeAttrs',
                params: {
                  attrs: '(fill|stroke|opacity)'
                }
              }
            ]
          })
          svgData = res.data
          spriter.add(
            `changeColor${svgNameArr[svgNameArr.length - 1]}`,
            null,
            svgData
          )
        } else {
          svgData = fs.readFileSync(filePath, 'utf-8')
        }
        spriter.add(`${svgNameArr[svgNameArr.length - 1]}`, null, svgData)
      }
      /* ... */
      // Compile the sprite
      spriter.compile((error, result) => {
        for (const mode in result) {
          for (const resource in result[mode]) {
            fs.mkdirSync(path.dirname(result[mode][resource].path), {
              recursive: true
            })
            fs.writeFileSync(
              result[mode][resource].path,
              result[mode][resource].contents
            )
          }
        }
      })
      const svgoPath = `${filefolder}/symbol/${spriteName}.svg`
      // const svgoData = fs.readFileSync(svgoPath, 'utf-8')
      // const res = optimize(svgoData, {
      //   path: svgoPath,
      //   plugins: [
      //     {
      //       name: 'removeAttrs',
      //       params: {
      //         attrs: '(fill|stroke|opacity)'
      //       }
      //     },
      //     {
      //       name: 'removeDoctype'
      //     },
      //     {
      //       name: 'cleanupAttrs'
      //     },
      //     {
      //       name: 'mergePaths'
      //     }
      //   ]
      // })
      // fs.writeFileSync(svgoPath, res.data)
      return svgoPath
    } catch (error) {
      console.log(error, 'ssss')
      return error
    }
  }
}

module.exports = CommonsService
