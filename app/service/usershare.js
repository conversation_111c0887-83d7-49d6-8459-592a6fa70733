/*
 * @Description: 这是***页面
 * @Date: 2022-11-07 10:28:13
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */
const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class UserSharesService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Usershare.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Usershare.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Usershare.create(params, options)
    return res
  }
  async insertMany(params, options) {
    const res = await this.ctx.model.Usershare.insertMany(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Usershare.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Usershare.update(filter, params, options)
    return res
  }
}

module.exports = UserSharesService
