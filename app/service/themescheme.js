const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class ThemeSchemesService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Themescheme.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Themescheme.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Themescheme.create(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Themescheme.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Themescheme.update(filter, params, options)
    return res
  }
}

module.exports = ThemeSchemesService
