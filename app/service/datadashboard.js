const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
const dbconns = require('../helper/datasource')
const SeatomException = require('../exception/seatomException')
const { ERROR_CODES, DASHBOARD_CODES } = require('../extend/constant')

class DataDashboardService extends Service {
  // 对接dmc仪表盘的接口
  async getProjectTree(config) {
    const dbconn = new dbconns['dmc'](config, this)
    const res = await dbconn.getProjectTree()
    return res
  }
  async getDashboardInfo(config) {
    const dbconn = new dbconns['dmc'](config, this)
    const data = {
      dsh_id: config.dsh_id
    }
    const res = await dbconn.getDashboardInfo(data)
    // console.log(res,'数据是什么呢')
    const array = res.meta.charts
    for (let i = 0; i < array.length; i++) {
      const element = array[i]
      for (let j = 0; j < element.children.length; j++) {
        const childitem = element.children[j]
        res.meta.charts[i].children[j].meta['comType'] = DASHBOARD_CODES[
          childitem.meta.type
        ]
          ? DASHBOARD_CODES[childitem.meta.type]
          : ''
      }
    }
    return res
  }
  async checkChartData(config) {
    const dbconn = new dbconns['dmc'](config, this)
    const res = await dbconn.getChartData(config)
    return res
  }
  async getChartData(config, isLimit, limitNum) {
    const dbconn = new dbconns['dmc'](config, this)
    const res = await dbconn.getChartData(config)
    // if (res.data.x.length > 1 || res.data.y.length === 0) {
    //     return false
    // }
    if (isLimit) {
      if (res && res.data && res.data.x.length > 0 && res.data.y.length > 0) {
        for (let x = 0; x < res.data.x.length; x++) {
          res.data.x[x].data = res.data.x[x].data.slice(0, limitNum)
        }
        for (let y = 0; y < res.data.y.length; y++) {
          res.data.y[y].data = res.data.y[y].data.slice(0, limitNum)
        }
      }
      if (res && res.data && res.data.x.length > 0 && res.data.y.length === 0) {
        for (let x = 0; x < res.data.x.length; x++) {
          res.data.x[x].data = res.data.x[x].data.slice(0, limitNum)
        }
      }
      if (res && res.data && res.data.x.length === 0 && res.data.y.length > 0) {
        for (let y = 0; y < res.data.y.length; y++) {
          res.data.y[y].data = res.data.y[y].data.slice(0, limitNum)
        }
      }
    }

    let dmcData = []
    // 多个个维度
    if (res && res.data && res.data.x.length >= 1 && res.data.y.length === 0) {
      for (let index = 0; index < res.data.x[0].data.length; index++) {
        let item = {}
        for (let x = 0; x < res.data.x.length; x++) {
          const xattr = res.data.x[x].name
          item[xattr] = res.data.x[x].data[index]
        }
        dmcData.push(item)
      }
    }

    // 一个数值
    if (res && res.data && res.data.x.length === 0 && res.data.y.length === 1) {
      const yname = res.data.y[0].name
      const ydata = new Number(res.data.y[0].data[0])
      dmcData.push({
        name: yname,
        value: ydata
      })
    }
    // 多个数值
    if (res && res.data && res.data.x.length === 0 && res.data.y.length > 1) {
      for (let index = 0; index < res.data.y[0].data.length; index++) {
        let item = {}
        for (let y = 0; y < res.data.y.length; y++) {
          const yattr = res.data.y[y].alias_name
          item[yattr] = res.data.y[y].data[index]
        }
        dmcData.push(item)
      }
    }
    // 一个维度一个数值
    if (res && res.data && res.data.x.length === 1 && res.data.y.length === 1) {
      for (let index = 0; index < res.data.x[0].data.length; index++) {
        const xdata = res.data.x[0].data[index]
        const ydata = new Number(res.data.y[0].data[index])
        const xattr = res.data.x[0].name
        const yattr = res.data.y[0].alias_name
        dmcData.push({
          [xattr]: xdata,
          [yattr]: ydata
        })
      }
    }
    // 一个维度多个数值
    if (res && res.data && res.data.x.length === 1 && res.data.y.length >= 2) {
      //for (let index = 0; index < res.data.x[0].data.length; index++) {
      for (let j = 0; j < res.data.y.length; j++) {
        const ydatalist = res.data.y[j]
        const yattr = res.info.yaxis_name || 'y'
        const xattr = res.data.x[0].name || 'x'
        for (let i = 0; i < ydatalist.data.length; i++) {
          const ydata = new Number(ydatalist.data[i])
          dmcData.push({
            系列: ydatalist.nick_name,
            [xattr]: res.data.x[0].data[i],
            [yattr]: ydata
          })
        }
      }
      // }
    }
    // 多个维度多个数值
    if (res && res.data && res.data.x.length >= 2 && res.data.y.length >= 1) {
      // 全部平铺的数据格式
      for (let index = 0; index < res.data.y[0].data.length; index++) {
        let item = {}
        const ydata = res.data.y[0].data[index]
        for (let y = 0; y < res.data.y.length; y++) {
          const yattr = res.data.y[y].alias_name
          item[yattr] = res.data.y[y].data[index]
        }
        for (let x = 0; x < res.data.x.length; x++) {
          const xattr = res.data.x[x].name
          item[xattr] = res.data.x[x].data[index]
        }
        dmcData.push(item)
      }
      // 多个系列的数据格式
      // for (let j = 0; j < res.data.y.length; j++) {
      //     const ydatalist = res.data.y[j];
      //     let item = {}
      //     for (let i = 0; i < ydatalist.data.length; i++) {
      //         const ydata = new Number(ydatalist.data[i])
      //         item[ydatalist.alias_name] = ydata
      //         item.s = ydatalist.name
      //         for (let k = 0; k < res.data.x.length; k++) {
      //             const xdatalist = res.data.x[k];
      //             item[xdatalist.name] = xdatalist.data[i]
      //         }
      //     }
      //     dmcData.push(item)
      // }
    }
    return dmcData
  }
}

module.exports = DataDashboardService
