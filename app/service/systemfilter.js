const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class SystemFiltersService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Systemfilter.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Systemfilter.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Systemfilter.create(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Systemfilter.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Systemfilter.update(
      filter,
      params,
      options
    )
    return res
  }
}

module.exports = SystemFiltersService
