const Service = require('egg').Service
const { getCommonProjection } = require('../extend/utils')
class LayergroupsService extends Service {
  async find(filter, projection, options) {
    const res = await this.ctx.model.Layergroup.find(
      filter,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async findOne(conditions, projection, options) {
    const res = await this.ctx.model.Layergroup.findOne(
      conditions,
      getCommonProjection(projection),
      options
    )
    return res
  }
  async create(params, options) {
    const res = await this.ctx.model.Layergroup.create(params, options)
    return res
  }
  async delete(params) {
    const res = await this.ctx.model.Layergroup.deleteOne(params)
    return res
  }
  async update(filter, params, options) {
    const res = await this.ctx.model.Layergroup.update(filter, params, options)
    return res
  }
}

module.exports = LayergroupsService
