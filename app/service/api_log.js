'use strict';

const Service = require('egg').Service;

class ApiLogService extends Service {
  /**
   * 创建API日志记录
   * @param {Object} params - API日志参数
   * @return {Object} 创建结果
   */
  async create(params) {
    return await this.ctx.model.ApiLog.create(params);
  }

  /**
   * 查询API日志
   * @param {Object} filter - 查询条件
   * @param {Object} projection - 投影选项
   * @param {Object} options - 查询选项
   * @return {Array} 查询结果
   */
  async find(filter = {}, projection = {}, options = {}) {
    return await this.ctx.model.ApiLog.find(filter, projection, options).sort({ requestTime: -1 });
  }

  /**
   * 获取单条API日志
   * @param {Object} filter - 查询条件
   * @param {Object} projection - 投影选项
   * @param {Object} options - 查询选项
   * @return {Object} 查询结果
   */
  async findOne(filter, projection = {}, options = {}) {
    return await this.ctx.model.ApiLog.findOne(filter, projection, options);
  }

  /**
   * 统计API调用次数
   * @param {Object} filter - 查询条件
   * @return {Number} 调用次数
   */
  async count(filter = {}) {
    return await this.ctx.model.ApiLog.count(filter);
  }

  /**
   * 按时间段统计API调用
   * @param {Date} startTime - 开始时间
   * @param {Date} endTime - 结束时间
   * @return {Object} 统计结果
   */
  async countByTimeRange(startTime, endTime) {
    const filter = {
      requestTime: {
        $gte: startTime,
        $lte: endTime
      }
    };
    return await this.count(filter);
  }

  /**
   * 按用户统计API调用
   * @param {String} userId - 用户ID
   * @return {Object} 统计结果
   */
  async countByUser(userId) {
    return await this.count({ userId });
  }

  /**
   * 获取最近的API调用记录
   * @param {Number} limit - 限制数量
   * @return {Array} 最近的调用记录
   */
  async getRecent(limit = 10) {
    return await this.ctx.model.ApiLog.find()
      .sort({ requestTime: -1 })
      .limit(limit);
  }
}

module.exports = ApiLogService; 