const { getResponseBody, getResponseError } = require('../extend/utils')
module.exports = (options, app) => {
  return async function opLog(ctx, next) {
    const { 
      url, 
      body, 
      query
    } = ctx.request

    let delName = ''

    if (url === '/api/screen/delete') {
      const screenInfo = await ctx.service.screen.findOne({ id: body.screens[0] })
      delName = screenInfo.name
    }

    await next()

    const userId = ctx.request.headers.sysuserid || // 优先获取请求头内的 userid
    ctx.response.body.data.userInfo.data.result.user_info.user_id || // 请求/api/user/dmcLogin接口返回的 userid
    ctx.response.body.data.workspaceData.userId // 请求/api/user/getUserId 接口返回的 userid

    try {
      const userData = await ctx.service.user.findOne({ userId })
      const userName = userData.userName
      if (userData.isDmcLogin && userData.isDmcLogin == 1) {

        const userInfoUrl = app.config.dmcAddress.address + '/api/ucenter/open/batch_user_info'

        const ent_id = userData.entId

        const userInfo = await ctx.curl(userInfoUrl, {
          method: 'POST',
          data: {
            ent_id,
            user_ids: JSON.stringify([userId])
          },
          dataType: 'json',
          followRedirect: true, // 允许重定向
          maxRedirects: 3 // 设置最大自动跳转次数，避免循环跳转无法终止
        })

        if(userInfo.status === 200 && userInfo.data.result) {
  
          const { group_id, org_code, group_name } = userInfo.data.result[0].group_info[0][0]
    
          const userBehaviorData = {
            c_time: new Date(),
            user_name: userName,
            operateType: '',
            status: checkStatus(ctx.response.status),
            details: '',
            user_id: userId,
            organization: group_name,
            organizationID: org_code,
            group_id
          }
  
          switch (url) {
            case '/api/user/getUserId':
            case '/api/user/dmcLogin':
              userBehaviorData.operateType = '登录'
              userBehaviorData.details = '登录了系统'
              break;
            case '/api/screen/create':
              userBehaviorData.operateType = '新建'
              userBehaviorData.details = `新建了大屏“${body.name}”`
              break;
            case '/api/screen/delete':
              userBehaviorData.operateType = '删除'
              userBehaviorData.details = `删除了大屏“${delName}”等`
              break;
            case `/api/screen/getscreenlinkAge?screenId=${query.screenId}`: {
              userBehaviorData.operateType = '查询'
              const screenInfo = await ctx.service.screen.findOne({ id: query.screenId })
              userBehaviorData.details = `查询了大屏“${screenInfo.name}”`
              }
              break;
            case `/api/component/update?id=${query.id}&sceneId=&pageId=`: {
              if (setScreenId() === query.id) return
              setScreenId(query.id)
              userBehaviorData.operateType = '修改'
              const screenInfo = await ctx.service.screen.findOne({ id: query.id })
              userBehaviorData.details = `修改了大屏“${screenInfo.name}”`
              }
              break;
          }

          await ctx.service.userBehavior.create(userBehaviorData)

        } else {
          ctx.body = getResponseBody(userInfo)
        }
      }
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })
    }

  }
}

/**
 * 判断调用函数是否成功 1为失败 2为成功
 * @param {*} num 
 * @returns 
 */
function checkStatus(num) {
  let strNum = String(num);
  if (strNum.startsWith('4') && strNum.startsWith('5')) {
    return 1
  }else {
    return 2
  }
}
/**
 * 同编辑页面的update接口只记录一次
 */
const setScreenId = (() => {
  let updateScreenId = ''
  return (id) => {
    if (id) {
      updateScreenId = id
    }
    return updateScreenId
  }
})()