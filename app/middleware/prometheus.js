const promClient = require('prom-client');

// 创建接口耗时的直方图指标
const apiDurationHistogram = new promClient.Gauge({
  name: 'fuxi_http_request_duration_seconds',
  help: 'Duration of API requests in seconds',
  labelNames: ['path', 'userId', 'sessionId']
});

module.exports = (options, app) => {
  return async function prometheus(ctx, next) {
    // 记录接口处理开始时间
    const start = process.hrtime();
    const {
      query
    } = ctx.request

    if (ctx.path === '/api/preview/screen') {
      setShareToken(query.shareToken)
    }

    const userId = ctx.request.headers.sysuserid

    try {
      await next()
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })
    } finally {
      // 计算接口耗时并记录到直方图指标中
      const end = process.hrtime(start);
      const elapsedTime = end[0] * 1e3 + end[1] / 1e6;
      apiDurationHistogram.labels({ path: ctx.path, userId, sessionId: setShareToken() }).set(elapsedTime)
    }
  }
}
const setShareToken = (() => {
  let updateScreenId = ''
  return (id) => {
    if (id) {
      updateScreenId = id
    }
    return updateScreenId
  }
})()
