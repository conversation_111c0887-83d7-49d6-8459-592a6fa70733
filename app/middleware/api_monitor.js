/**
 * API 监控中间件
 * 主要用于监控 getData API 的调用情况，包括请求发起人、请求时间、请求的API链接等
 */
module.exports = (options, app) => {
  return async function apiMonitor(ctx, next) {
    // 如果中间件被禁用，直接继续处理请求
    if (options.enable === false) {
      await next();
      return;
    }
    // 获取当前请求的用户
    const userId = ctx.request.headers.sysuserid;
    let userName = '';
    
    // 尝试获取用户名称
    if (userId) {
      const userData = await ctx.service.user.findOne({ userId })
      userName = userData.userName
    }
    
    // 对ctx.request.body进行遍历，只对item.sourceType为api的进行监控，其他的不监控
    const apiList = ctx.request.body.filter(item => item.sourceType === 'api')
    if (apiList.length === 0) {
      await next();
      return;
    }
    // 获取apiList的组件的id
    const componentIds = apiList.map(item => item.componentId)
    // 获取组件的配置
    const components = await ctx.model.Component.find({ id: { $in: componentIds } })
    // 记录请求开始时间
    const startTime = Date.now();
    // 存储API请求日志
    const apiLogs = [];
    await Promise.all(components.map(async (component) => {
      const baseUrl = component.dataConfig.dataResponse.source.api.data.baseUrl
      const path = component.dataConfig.dataResponse.source.api.data.path
      const url = baseUrl + path
      // 根据组件的ID获取大屏的id
      const screenId = component.screenId
      // 获取大屏的配置
      const screen = await ctx.model.Screen.findOne({ id: screenId })
      apiLogs.push({
        requestUrl: url,
        userName: userName,
        requestTime: new Date(),
        screenName: screen.name,
      });
    }))
    try {
      await next();
      // 请求完成后更新日志状态
      const responseTime = Date.now() - startTime;
      const responseStatus = ctx.response.status;
      // 更新并保存日志
      apiLogs.forEach(async (apiLog) => {
        apiLog.responseStatus = responseStatus;
        apiLog.responseTime = responseTime;
        try {
          console.log(apiLog, 'apiLog')
          await ctx.service.apiLog.create(apiLog);
        } catch (logError) {
          ctx.logger.error('保存API日志失败', logError);
        }
      });
      
    } catch (error) {
      // 出错时记录错误信息
      const responseTime = Date.now() - startTime;
      const responseStatus = error.status || 500;
      
      apiLogs.forEach(async (apiLog) => {
        apiLog.responseStatus = responseStatus;
        apiLog.responseTime = responseTime;
        apiLog.error = error.message;
        try {
          await ctx.service.apiLog.create(apiLog);
        } catch (logError) {
          ctx.logger.error('保存API错误日志失败', logError);
        }
      });
      
      throw error;
    }
  };
}; 