const { getResponseBody } = require('../extend/utils')
module.exports = (options, app) => {
  //返回一个异步的方法
  return async function loginMiddleware(ctx, next) {
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    const product = ctx.request.header.product
    if (product) {
      await next()
      return
    }
    if (!token || !userId) {
      ctx.body = getResponseBody(null, false, '无用户权限，请登录后访问', 401)
      return
    }
    const userData = await ctx.service.user.findOne({ userId })
    if (!userData && !product) {
      ctx.body = getResponseBody(
        null,
        false,
        '登录失效，请重新登录no userId',
        401
      )
      return
    }
    if (userData.isDmcLogin && userData.isDmcLogin == 1 && options.ucenter) {
      const checkTokenUrl =
        options.dmcAddress.address + '/api/ucenter/check/valid_token'
      const checkResult = await ctx.curl(checkTokenUrl, {
        method: 'GET',
        data: {
          valid_token: token
        },
        dataType: 'json',
        timeout: 100000,
        rejectUnauthorized: false
      })
      if (
        checkResult &&
        checkResult.status == 200 &&
        checkResult.data.status != '0'
      ) {
        ctx.body = getResponseBody(
          checkResult.data,
          false,
          '登录已过期，请重新登录',
          401
        )
        return
      }
    }
    if (userData.isDmcLogin && userData.isDmcLogin == 2) {
      try {
        const decode = ctx.app.jwt.verify(token, ctx.app.config.jwt.secret)
      } catch (e) {
        ctx.body = getResponseBody(null, false, 'token失效，请重新登录', 401)
        return
      }
    }
    await next()
  }
}
