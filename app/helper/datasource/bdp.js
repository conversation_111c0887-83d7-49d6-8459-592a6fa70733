'use strict'

const { findIndex } = require('lodash')
const crypto = require('crypto')
const utils = require('util')
const dateformat = require('../../utils/dateformat')
const SeatomException = require('../../exception/seatomException')
const {
  buildSelectSql,
  wrapAggregate,
  calulateDate,
  formattedDateBdp,
  compare,
  generateSign
} = require('../../extend/sql-utils')
const {
  ERROR_CODES,
} = require('../../extend/constant')
const moment = require('moment')
const DbConn = require('../base-datasource')

function Bdp(config, service) {
  this.ctx = service.ctx
  this.base = service.config.bdpAddress.address
  this.privateKey = service.config.bdpAddress.privateKey
}

// 通用请求函数
Bdp.prototype._request = async function (
  data,
  shortUrl = '/fuxi/get_tb_list_with_folder'
) {
  const verifyCode = this.privateKey
  const currentDate = dateformat.format(new Date(), 'yyyy-MM-dd')
  const dataTohash = currentDate + verifyCode
  const hashed = crypto.createHash('md5').update(dataTohash).digest('hex')
  const headers = {
    bdptoken: hashed
  }
  console.log('data入参######', data)
  let result
  try {
    result = await this.ctx.curl(this.base + shortUrl, {
      headers,
      dataType: 'json',
      data: JSON.stringify(data),
      method: 'POST',
      timeout: 5*60*1000
    })
  } catch (error) {
    this.ctx.logger.info('bdp接口报错', data, error, result)
    throw new SeatomException(400, error)
  }
  // const tokenend = new Date().getTime()
  // this.ctx.logger.info(
  //   '请求bdp接口时间',
  //   shortUrl,
  //   `${tokenend - tokenstart}hzms`
  // )
  // this.ctx.logger.info('_request方法的bdp接口请求的参数', data)
  this.ctx.logger.info('_request返回值', result)
  if (result.data.code == '200' && result.data.success) {
    console.log('result.data.data####', result.data.data)
    // this.ctx.logger.info('_requst', '')
    return result.data.data
  } else {
    throw new SeatomException(+result.code, result.message)
  }
}

// 初始选择打开工作表之后请求的接口
Bdp.prototype.getTreeList = async function () {
  const data = {
    folder_id: 'root'
  }
  const result = await this._request(data)
  return result
}

// 点击文件夹之后传folderId后请求的接口
Bdp.prototype.getFolderTbList = async function (config) {
  const data = {
    folder_id: config.folderId
  }
  const result = await this._request(data)
  return result
}

// 搜索栏回车调用的接口
Bdp.prototype.searchTb = async function (config) {
  const data = {
    filter_str: config.filterStr,
    folder_id: 'root'
  }
  const result = await this._request(data)
  return result
}

// 获取字段列表的接口
Bdp.prototype.getFieldList = async function (config) {
  const url = '/fuxi/get_tb_info'
  const data = {
    tb_id: config.tbid
  }
  let result = await this._request(data, url)

  const dateFields = [],
    stringFields = [],
    numberFields = []
  result.forEach(element => {
    if (element.f_name) {
      element.name = element.f_name
    }
    switch (element.data_type) {
      case 'date':
        dateFields.push(element)
        break
      case 'string':
        stringFields.push(element)
        break
      case 'number':
        numberFields.push(element)
        break
    }
  })
  const fieldList = []
  fieldList.push({
    type: 'date',
    fields: dateFields
  })
  fieldList.push({
    type: 'string',
    fields: stringFields
  })
  fieldList.push({
    type: 'number',
    fields: numberFields
  })
  return fieldList
}

// 获取数据接口
Bdp.prototype.getData = async function (
  baseConfig,
  componentConfig,
  queryParams
) {
  const fieldList = []
  let selectList = [],
    whereList = '',
    groupByList = [],
    orderByList = [],
    convertOrderByList = []
  const shortUrl = '/fuxi/get_tb_data'
  const tbid = componentConfig.tbId
  if (!tbid) {
    throw new SeatomException(ERROR_CODES.PARAMETER_IS_REQUIRED, 'tbId不存在')
  }
  // 维度部分
  let datefile = []
  let dmCalculation,valCalculation;
  const dimension = componentConfig.fields.dimension
  dimension.forEach((element, index) => {
    if (element.fid.indexOf('fk') == 0) {
      if (element.calculation) {
        // 如果纬度中有计算字段
        dmCalculation = element.calculation
        const field = calulateDate(element.calculation, element.fid)
        if (field) {
          selectList.push(field)
          groupByList.push(field)
          fieldList.push(element.name)
          // 同环比维度
          // 不要求做
          datefile.push({
            name: element.name,
            dateFormatter: element.dateFormatter
          })
        }
      } else {
        selectList.push(element.fid)
        groupByList.push(element.fid)
        orderByList.push(element.fid)
        // 有别名加别名，没有别名字段先加名称
        fieldList.push(element.name)
      }
    } else {
      // 是计算字段
      // 需求不要求加
      // todo
    }
  })

  // 数值部分
  const numerical = componentConfig.fields.numericalValue
  let numFile = []
  numerical &&
    numerical.forEach((element, index) => {
      if (element.fid.indexOf('fk') == 0) {
        const fieldStr = wrapAggregate(
          element.calculation,
          element.fid,
          'bdp',
          element.data_type
        )
        selectList.push(fieldStr)
        if (element.yoyQoqType) {
          fieldList.push(
            element.name + '(' + element.calculation + element.yoyQoqType + ')'
          )
        } else {
          fieldList.push(element.name + '(' + element.calculation + ')')
        }
        // 同环比数值
        // 这个版本还不支持
        // y_fields.push({
        //   fid: fieldStr,
        //   YoY: element.YoY,
        //   yoyQoqType: element.yoyQoqType,
        //   alias: element.fid + element.yoyQoqType
        // })
        numFile.push({
          name:
            element.name + '(' + element.calculation + element.yoyQoqType + ')',
          formatter: element.formatter
        })
      } else {
        // 计算字段处理
        // 这个版本还不支持
        // const fieldStr = wrapAggregate(
        //   element.calculation,
        //   calculateFieldMap[element.fid],
        //   'dmc',
        //   element.data_type
        // )
        // selectList.push(fieldStr)
        // fieldList.push(element.name + '(' + element.calculation + ')')
      }
    })
  if (selectList.length == 0) {
    throw new SeatomException(ERROR_CODES.SQL_ERROR, '字段列表为空')
  }

  // where部分 相当于前端页面上的筛选
  const where = componentConfig.where
  let whereSql = ''
  const relative = [
    'today',
    'lastday',
    'last7Days',
    'last30Days',
    'last90Days',
    'lastYear'
  ]
  if (where.enable) {
    where.whereCondition &&
      where.whereCondition.forEach((item, index) => {
        // 处理筛选条件，包括今天、昨天、最近7天、最近30天、最近90天、最近一年、全部
        if (relative.includes(item.compare)) {
          item.compareValue = this.relativeDate(item)
        }
        if (item.compare == 'unequal' && item.type == 'date') {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                "IFNULL('" +
                item.fid +
                "',' ')" +
                ' '
              : "IFNULL('" + item.fid + "',' ')" + ' '
        } else if (item.compare == 'contained') {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                "'" +
                item.compareValue[0] +
                "'"
              : "'" + item.compareValue[0] + "'"
        } else {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                item.fid +
                ' '
              : item.fid + ' '
        }
        switch (item.compareValue.length) {
          case 0:
            whereSql +=
              compare[item.type][item.compare] +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
            break
          case 1:
            if (item.type == 'string') {
              if (
                [
                  'matchOnEnd',
                  'matchOnStart',
                  'notContain',
                  'contain'
                ].includes(item.compare)
              ) {
                whereSql +=
                  compare[item.type][item.compare].replace(
                    '{0}',
                    item.compareValue[0]
                  ) +
                  (where.whereCondition.length == index + 1
                    ? ' '
                    : item.composeType == 'or'
                    ? ' ) OR '
                    : ' ) AND ')
              } else if (item.compare == 'contained') {
                whereSql += compare[item.type][item.compare].replace(
                  '{0}',
                  item.fid
                )
                +(where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
              } else if (item.compare == 'range') {
                const inItem = JSON.stringify(item.compareValue).replace(
                  /\[|]/g,
                  ''
                )
                whereSql += compare[item.type][item.compare].replace(
                  '{0}',
                  inItem
                )
                +(where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
              } else {
                whereSql +=
                  compare[item.type][item.compare].replace(
                    '{0}',
                    JSON.stringify(item.compareValue[0])
                  ) +
                  (where.whereCondition.length == index + 1
                    ? ' '
                    : item.composeType == 'or'
                    ? ' ) OR '
                    : ' ) AND ')
              }
            }
            if (item.type == 'date') {
              const compareValue = moment(item.compareValue[0]).format(
                'YYYY-MM-DD HH:mm:ss'
              )
              whereSql +=
                compare[item.type][item.compare].replace(
                  '{0}',
                  JSON.stringify(compareValue)
                ) +
                (where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
            }
            if (item.type == 'number') {
              whereSql +=
                compare[item.type][item.compare].replace(
                  '{0}',
                  item.compareValue[0]
                ) +
                (where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
            }
            break
          case 2:
            let compareValue1, compareValue2
            if (item.type == 'date') {
              compareValue1 = JSON.stringify(
                moment(item.compareValue[0]).format('YYYY-MM-DD HH:mm:ss')
              )
              compareValue2 = JSON.stringify(
                moment(item.compareValue[1]).format('YYYY-MM-DD HH:mm:ss')
              )
            } else {
              compareValue1 = item.compareValue[0]
              compareValue2 = item.compareValue[1]
            }
            if (item.type == 'string' && item.compare == 'range') {
              // 范围筛选
              const inItem = JSON.stringify(item.compareValue).replace(
                /\[|]/g,
                ''
              )
              whereSql += compare[item.type][item.compare].replace(
                '{0}',
                inItem
              )
              +(where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
            } else if (relative.includes(item.compare)) {
              // 日期范围筛选
              whereSql +=
                compare[item.type][item.compare]
                  .replace('{0}', compareValue1)
                  .replace('{1}', item.fid)
                  .replace('{2}', compareValue2) +
                (where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
            } else if (item.type == 'string' && item.compare == 'in') {
              // in筛选
              const inItem = JSON.stringify(item.compareValue).replace(
                /\[|]/g,
                ''
              )
              whereSql += compare[item.type][item.compare].replace(
                '{0}',
                inItem
              )
              +(where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
            } else {
              // contain筛选
              whereSql +=
                compare[item.type][item.compare]
                  .replace('{0}', compareValue1)
                  .replace('{1}', compareValue2) +
                (where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
            }
            break
        }
        if (item.compareValue.length > 2) {
          const inItem = JSON.stringify(item.compareValue).replace(/\[|]/g, '')
          whereSql += compare[item.type][item.compare].replace('{0}', inItem)
          +(where.whereCondition.length == index + 1
            ? ' '
            : item.composeType == 'or'
            ? ' ) OR '
            : ' ) AND ')
        }
      })
    whereList = whereSql
    // 兼容数据处理功能对orderby
    where.orderCondition &&
      where.orderCondition.forEach((item, index) => {
        if (item.calculation) {
          // 按年、月、日、时、分、秒
          const field = calulateDate(item.calculation, item.fid)
          orderByList.unshift(field + ' ' + item.orderBy)
        } else {
          const orderByListIndex = findIndex(orderByList, o => {
            return o === item.fid
          })
          if (orderByListIndex > -1) {
            orderByList.splice(orderByListIndex, 1)
            orderByList.unshift(item.fid + ' ' + item.orderBy)
          } else {
            orderByList.unshift(item.fid + ' ' + item.orderBy)
          }
        }
        convertOrderByList.push('a.' + item.fid + ' ' + item.orderBy)
      })
    orderByList.forEach((item, index) => {
      if (item.search('desc') == -1 && item.search('asc') == -1) {
        orderByList[index] = orderByList[index] + ' ' + ' asc '
      }
    })
  } else {
    whereList = ''
  }

  // 拼接sql
  let sql = buildSelectSql(
    tbid,
    selectList,
    whereList,
    groupByList,
    orderByList,
    5 * 60 * 1000
  )
  const dataPrams = {
    tb_id: tbid,
    query_sql: sql
  }
  this.ctx.logger.info('查询的sql', sql)
  let bdpResult;
  try {
    bdpResult = await this._request(dataPrams, shortUrl)
  } catch (error) {
    throw new SeatomException(500, error)
  }
  const finalResult = []
  bdpResult.forEach(dataElement => {
    const rowElement = {}
    Object.keys(dataElement).forEach((element, index) => {
      const dateformatter = datefile.filter(item => {
        return item.name == fieldList[index] && item.dateFormatter
      })
      const formatter = numFile.filter(item => {
        return item.name == fieldList[index] && item.formatter
      })
      if (dateformatter && dateformatter.length) {
        rowElement[fieldList[index]] = formattedDateBdp(
          dataElement[element],
          dateformatter[0].dateFormatter
        )
      } else if (
        formatter &&
        formatter.length &&
        formatter[0].formatter.num.unit &&
        formatter[0].formatter.num.digit
      ) {
        rowElement[fieldList[index]] = dataElement[element]
          ? (dataElement[element] / formatter[0].formatter.num.unit).toFixed(
              formatter[0].formatter.num.digit
            )
          : dataElement[element]
      } else {
        rowElement[fieldList[index]] = dataElement[element]
      }
    })
    finalResult.push(rowElement)
  })
  return finalResult
}
utils.inherits(Bdp, DbConn)

exports = module.exports = Bdp
