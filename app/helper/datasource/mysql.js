const utils = require('util')
const DbConn = require('../base-datasource')
const mysql = require('mysql')
const moment = require('moment')
const { findIndex } = require('lodash')
const SeatomException = require('../../exception/seatomException')
const { ERROR_CODES } = require('../../extend/constant')
const {
  buildSelectSql,
  replaceNameToFid,
  generateSign,
  wrapAggregate,
  compare
} = require('../../extend/sql-utils')
const { Parser } = require('node-sql-parser')
const parser = new Parser()

const { MYSQL_TYPES } = require('../../extend/constant')

function Mysql(config, service) {
  this.config = {
    host: String(config.host) || 'localhost',
    user: String(config.username) || 'root',
    password: String(config.password) || '',
    // password: '',
    port: config.port || 3306,
    database: config.database,
    timezone: '08:00',
    multipleStatements: false // 一次执行一条sql，防止sql注入
  }
  this.config = Object.assign(config, this.config)
}

utils.inherits(Mysql, DbConn)

Mysql.prototype.getFieldList = async function (config) {
  const sql = `select column_name as name, data_type as data_type from information_schema.columns where table_schema ='${config.database}' and table_name = '${config.tbName}' ;`
  // 处理返回的数据类型
  const res = await this.execute(sql)
  console.log(res, 'res')
  const dateFields = [],
    stringFields = [],
    numberFields = []
  res.map((item, value) => {
    console.log(item.data_type.toUpperCase(), 'res')

    if (MYSQL_TYPES.STRING_TYPE.includes(item.data_type.toUpperCase())) {
      stringFields.push(item)
    }
    if (MYSQL_TYPES.NUMBER_TYPE.includes(item.data_type.toUpperCase())) {
      numberFields.push(item)
    }
    if (MYSQL_TYPES.DATE_TYPE.includes(item.data_type.toUpperCase())) {
      dateFields.push(item)
    }
  })
  const fieldList = []
  fieldList.push({
    type: 'date',
    fields: dateFields
  })
  fieldList.push({
    type: 'string',
    fields: stringFields
  })
  fieldList.push({
    type: 'number',
    fields: numberFields
  })
  return fieldList
}

Mysql.prototype.getTreeList = async function (config) {
  const sql = `select table_name as \`TABLE_NAME\` from information_schema.tables where table_schema='${this.config.database}'`
  const res = await this.execute(sql)
  return res
}

Mysql.prototype.dblist = async function () {
  const sql = 'select schema_name from information_schema.schemata;'
  const res = await this.execute(sql)
  return res
}

Mysql.prototype.hanleData = async function (
  baseConfig,
  componentConfig,
  queryParams,
  dateBaseType
) {
  // 记录四个模块的内容
  let selectList = [],
    whereList = '',
    groupByList = [],
    orderByList = []
  if (!queryParams.workspaceId && queryParams.workspaceId !== 0) {
    throw new SeatomException(
      ERROR_CODES.PARAMETER_IS_REQUIRED,
      '缺失workspaceId参数'
    )
  }
  // 处理维度字段列表
  const fieldList = []
  const dimension = componentConfig.fields.dimension
  dimension.forEach((element, index) => {
    if (element.name) {
      selectList.push('`' + element.name + '`')
      groupByList.push('`' + element.name + '`')
      orderByList.push('`' + element.name + '`')
      // 有别名加别名，没有别名字段先加名称
      fieldList.push('`' + element.name + '`')
    }
  })

  // 处理数值字段列表
  const numerical = componentConfig.fields.numericalValue
  numerical.forEach((element, index) => {
    if (element.name && element.calculation) {
      const fieldStr = wrapAggregate(
        element.calculation,
        '`' + element.name + '`',
        dateBaseType,
        element.data_type
      )
      selectList.push(fieldStr)
    }
  })
  if (selectList.length == 0) {
    throw new SeatomException(ERROR_CODES.SQL_ERROR, '字段列表为空')
  }
  const where = componentConfig.where
  let whereSql = ''
  const relative = [
    'today',
    'lastday',
    'last7Days',
    'last30Days',
    'last90Days',
    'lastYear'
  ]
  if (where.enable) {
    where.whereCondition.forEach((item, index) => {
      // 处理筛选条件，包括今天、昨天、最近7天、最近30天、最近90天、最近一年、全部
      if (relative.includes(item.compare)) {
        item.compareValue = this.relativeDate(item)
      }
      {
        if (
          item.compare == 'unequal' &&
          dateBaseType == 'mysql' &&
          item.type == 'date'
        ) {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                "IFNULL('" +
                item.field +
                "',' ')" +
                ' '
              : "IFNULL('" + item.field + "',' ')" + ' '
        } else if (item.compare == 'contained') {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                "'" +
                item.compareValue[0] +
                "'"
              : "'" + item.compareValue[0] + "'"
        } else {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                '`' +
                item.field +
                '`' +
                ' '
              : '`' + item.field + '`' + ' '
        }
      }
      switch (item.compareValue.length) {
        case 0:
          whereSql +=
            compare[item.type][item.compare] +
            (where.whereCondition.length == index + 1
              ? ' '
              : item.composeType == 'or'
              ? ' ) OR '
              : ' ) AND ')
          break
        case 1:
          if (item.type == 'string') {
            if (
              ['matchOnEnd', 'matchOnStart', 'notContain', 'contain'].includes(
                item.compare
              )
            ) {
              if (dateBaseType == 'mysql') {
                whereSql +=
                  compare[item.type][item.compare].replace(
                    '{0}',
                    `${item.compareValue[0]}`
                  ) +
                  (where.whereCondition.length == index + 1
                    ? ' '
                    : item.composeType == 'or'
                    ? ' ) OR '
                    : ' ) AND ')
              } else {
                whereSql +=
                  compare[item.type][item.compare].replace(
                    '{0}',
                    `'${item.compareValue[0]}'`
                  ) +
                  (where.whereCondition.length == index + 1
                    ? ' '
                    : item.composeType == 'or'
                    ? ' ) OR '
                    : ' ) AND ')
              }
            } else if (item.compare == 'contained') {
              whereSql += compare[item.type][item.compare].replace(
                '{0}',
                `${item.fid}`
              )
              ;+(where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
            } else {
              whereSql +=
                compare[item.type][item.compare].replace(
                  '{0}',
                  `'${item.compareValue[0]}'`
                ) +
                (where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
            }
          }
          if (item.type == 'date') {
            const compareValue = moment(item.compareValue[0]).format(
              'YYYY-MM-DD HH:mm:ss'
            )
            whereSql +=
              compare[item.type][item.compare].replace(
                '{0}',
                `'${compareValue}'`
              ) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          }
          if (item.type == 'number') {
            whereSql +=
              compare[item.type][item.compare].replace(
                '{0}',
                item.compareValue[0]
              ) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          }
          break
        case 2:
          let compareValue1, compareValue2
          if (item.type == 'date') {
            compareValue1 = JSON.stringify(
              moment(item.compareValue[0]).format('YYYY-MM-DD HH:mm:ss')
            )
            compareValue2 = JSON.stringify(
              moment(item.compareValue[1]).format('YYYY-MM-DD HH:mm:ss')
            )
          } else {
            compareValue1 = item.compareValue[0]
            compareValue2 = item.compareValue[1]
          }
          if (relative.includes(item.compare)) {
            whereSql +=
              compare[item.type][item.compare]
                .replace('{0}', compareValue1)
                .replace('{1}', item.field)
                .replace('{2}', compareValue2) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          } else {
            whereSql +=
              compare[item.type][item.compare]
                .replace('{0}', compareValue1)
                .replace('{1}', compareValue2) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          }
          break
      }
    })

    whereList = whereSql
    // 兼容数据处理功能对orderby
    console.log(where.orderCondition, 'dadsa=================dsda')
    if (where.orderCondition && where.orderCondition.length) {
      where.orderCondition.forEach((item, index) => {
        if (item.calculation) {
          // 按年、月、日、时、分、秒
          const field = calulateDate(item.calculation, item.fid)
          orderByList.unshift(field + ' ' + item.orderBy)
        } else {
          const orderByListIndex = findIndex(orderByList, item.field)
          if (orderByListIndex > -1) {
            delete orderByList[orderByListIndex]
            orderByList.unshift(item.field + ' ' + item.orderBy)
          } else {
            orderByList.unshift(item.field + ' ' + item.orderBy)
          }
        }
      })
    } else {
      orderByList = []
    }

    orderByList.forEach((item, index) => {
      if (item.search('desc') == -1 && item.search('asc') == -1) {
        orderByList[index] = orderByList[index] + ' ' + ' asc '
      }
    })
  } else {
    whereList = ''
    orderByList = []
  }

  const sql = buildSelectSql(
    componentConfig.tbName,
    selectList,
    whereList,
    groupByList,
    orderByList,
    100000,
    queryParams.type || dateBaseType
  )
  // console.log('>>>>>>>><<<<<<<<<<>>>>>', typeof sql, sql)
  return sql
}

Mysql.prototype.getData = async function (
  baseConfig,
  componentConfig,
  queryParams
) {
  let sql
  if (componentConfig.advanced) {
    sql = queryParams.params.targetSql || componentConfig.sql
    try {
      const ast = parser.astify(sql.replace(/[\u4e00-\u9fa5]/g, 't'))
      if (Array.isArray(ast)) {
        for (let i = 0; i < ast.length; i++) {
          if (ast[i].type !== 'select') {
            throw new Error()
          }
        }
      } else if (ast.type !== 'select') {
        throw new Error()
      }
    } catch (error) {
      throw new SeatomException(
        ERROR_CODES.PARAMETER_IS_REQUIRED,
        'sql格式错误'
      )
    }
    // 拼上筛选条件
    let screenSQl = hanleScreen(
      baseConfig,
      componentConfig,
      queryParams,
      'mysql'
    )
    sql = joinWhere(sql, screenSQl.whereList)
  } else {
    sql = await this.hanleData(
      baseConfig,
      componentConfig,
      queryParams,
      'mysql'
    )
  }
  const mysqlResult = await this.query(sql)
  return mysqlResult
}

Mysql.prototype.query = async function (sql) {
  // 校验sql变量格式
  if (!sql || typeof sql !== 'string') {
    console.warn('ql statement format is illegal', sql)
    throw new Error('sql statement format is illegal')
  }

  // 去掉sql两端空格
  sql = sql.replace(/^\s+|\s+$/g, '')

  // 做一个简单的sql校验，防止用户写了个drop之类的东西搞事情
  if (sql.substring(0, 6).toLowerCase() != 'select') {
    throw new Error("query sql must start with 'select'")
  }
  console.log(sql, 'sql')
  const res = await this.execute(sql)
  return res
}

Mysql.prototype.execute = function (sql) {
  // to_do: 目前为了防止连接数过多，采取查完即关闭连接，需要优化
  console.log(this.config, 'this.config', sql, 'sql')
  const connection = mysql.createConnection(this.config)
  return new Promise(function (resolve, reject) {
    connection.connect(function (err) {
      if (err) {
        reject(err)
        return
      }
    })

    connection.query(sql, function (error, res) {
      if (error) {
        reject(error)
        return
      }
      resolve(res)
    })
    connection.end()
  })
}

exports = module.exports = Mysql

function hanleScreen(baseConfig, componentConfig, queryParams, dateBaseType) {
  // 记录四个模块的内容
  let selectList = [],
    whereList = '',
    groupByList = [],
    orderByList = []
  if (!queryParams.workspaceId && queryParams.workspaceId !== 0) {
    throw new SeatomException(
      ERROR_CODES.PARAMETER_IS_REQUIRED,
      '缺失workspaceId参数'
    )
  }
  // 处理维度字段列表
  const fieldList = []
  const dimension = componentConfig.fields.dimension
  dimension.forEach((element, index) => {
    if (element.name) {
      selectList.push('`' + element.name + '`')
      groupByList.push('`' + element.name + '`')
      orderByList.push('`' + element.name + '`')
      // 有别名加别名，没有别名字段先加名称
      fieldList.push('`' + element.name + '`')
    }
  })

  // 处理数值字段列表
  const numerical = componentConfig.fields.numericalValue
  numerical.forEach((element, index) => {
    if (element.name && element.calculation) {
      const fieldStr = wrapAggregate(
        element.calculation,
        '`' + element.name + '`',
        dateBaseType,
        element.data_type
      )
      selectList.push(fieldStr)
    }
  })
  // if (selectList.length == 0) {
  //   throw new SeatomException(ERROR_CODES.SQL_ERROR, '字段列表为空')
  // }
  const where = componentConfig.where
  let whereSql = ''
  const relative = [
    'today',
    'lastday',
    'last7Days',
    'last30Days',
    'last90Days',
    'lastYear'
  ]
  if (where.enable) {
    where.whereCondition.forEach((item, index) => {
      // 处理筛选条件，包括今天、昨天、最近7天、最近30天、最近90天、最近一年、全部
      if (relative.includes(item.compare)) {
        item.compareValue = this.relativeDate(item)
      }
      {
        if (
          item.compare == 'unequal' &&
          dateBaseType == 'mysql' &&
          item.type == 'date'
        ) {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                "IFNULL('" +
                item.field +
                "',' ')" +
                ' '
              : "IFNULL('" + item.field + "',' ')" + ' '
        } else if (item.compare == 'contained') {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                "'" +
                item.compareValue[0] +
                "'"
              : "'" + item.compareValue[0] + "'"
        } else {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                '`' +
                item.field +
                '`' +
                ' '
              : '`' + item.field + '`' + ' '
        }
      }
      switch (item.compareValue.length) {
        case 0:
          whereSql +=
            compare[item.type][item.compare] +
            (where.whereCondition.length == index + 1
              ? ' '
              : item.composeType == 'or'
              ? ' ) OR '
              : ' ) AND ')
          break
        case 1:
          if (item.type == 'string') {
            if (
              ['matchOnEnd', 'matchOnStart', 'notContain', 'contain'].includes(
                item.compare
              )
            ) {
              if (dateBaseType == 'mysql') {
                whereSql +=
                  compare[item.type][item.compare].replace(
                    '{0}',
                    `${item.compareValue[0]}`
                  ) +
                  (where.whereCondition.length == index + 1
                    ? ' '
                    : item.composeType == 'or'
                    ? ' ) OR '
                    : ' ) AND ')
              } else {
                whereSql +=
                  compare[item.type][item.compare].replace(
                    '{0}',
                    `'${item.compareValue[0]}'`
                  ) +
                  (where.whereCondition.length == index + 1
                    ? ' '
                    : item.composeType == 'or'
                    ? ' ) OR '
                    : ' ) AND ')
              }
            } else if (item.compare == 'contained') {
              whereSql += compare[item.type][item.compare].replace(
                '{0}',
                `${item.fid}`
              )
              ;+(where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
            } else {
              whereSql +=
                compare[item.type][item.compare].replace(
                  '{0}',
                  `'${item.compareValue[0]}'`
                ) +
                (where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
            }
          }
          if (item.type == 'date') {
            const compareValue = moment(item.compareValue[0]).format(
              'YYYY-MM-DD HH:mm:ss'
            )
            whereSql +=
              compare[item.type][item.compare].replace(
                '{0}',
                `'${compareValue}'`
              ) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          }
          if (item.type == 'number') {
            whereSql +=
              compare[item.type][item.compare].replace(
                '{0}',
                item.compareValue[0]
              ) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          }
          break
        case 2:
          let compareValue1, compareValue2
          if (item.type == 'date') {
            compareValue1 = JSON.stringify(
              moment(item.compareValue[0]).format('YYYY-MM-DD HH:mm:ss')
            )
            compareValue2 = JSON.stringify(
              moment(item.compareValue[1]).format('YYYY-MM-DD HH:mm:ss')
            )
          } else {
            compareValue1 = item.compareValue[0]
            compareValue2 = item.compareValue[1]
          }
          if (relative.includes(item.compare)) {
            whereSql +=
              compare[item.type][item.compare]
                .replace('{0}', compareValue1)
                .replace('{1}', item.field)
                .replace('{2}', compareValue2) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          } else {
            whereSql +=
              compare[item.type][item.compare]
                .replace('{0}', compareValue1)
                .replace('{1}', compareValue2) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          }
          break
      }
    })

    whereList = whereSql
    // 兼容数据处理功能对orderby
    console.log(where.orderCondition, 'dadsa=================dsda')
    if (where.orderCondition && where.orderCondition.length) {
      where.orderCondition.forEach((item, index) => {
        if (item.calculation) {
          // 按年、月、日、时、分、秒
          const field = calulateDate(item.calculation, item.fid)
          orderByList.unshift(field + ' ' + item.orderBy)
        } else {
          const orderByListIndex = findIndex(orderByList, item.field)
          if (orderByListIndex > -1) {
            delete orderByList[orderByListIndex]
            orderByList.unshift(item.field + ' ' + item.orderBy)
          } else {
            orderByList.unshift(item.field + ' ' + item.orderBy)
          }
        }
      })
    } else {
      orderByList = []
    }

    orderByList.forEach((item, index) => {
      if (item.search('desc') == -1 && item.search('asc') == -1) {
        orderByList[index] = orderByList[index] + ' ' + ' asc '
      }
    })
  } else {
    whereList = ''
    orderByList = []
  }
  console.log(
    'whereList-------------',
    whereList,
    'whereList-------------',
    orderByList
  )
  // const sql = buildSelectSql(
  //   componentConfig.tbName,
  //   selectList,
  //   whereList,
  //   groupByList,
  //   orderByList,
  //   100000,
  //   queryParams.type || dateBaseType
  // )
  // console.log('>>>>>>>><<<<<<<<<<>>>>>', typeof sql, sql)
  return {
    whereList
  }
}
// 拼接wherelist条件
function joinWhere(query, filter) {
  // 如果query或filter为空，直接返回query
  if (!query || !filter) {
    return query
  }
  // 去掉query末尾的分号（如果有的话）
  query = query.trim()
  if (query.endsWith(';')) {
    query = query.slice(0, -1)
  }
  // 根据sql语法，直接把要写在where后面的语法直接全部截取出来
  // 匹配where后面的所有子句（order by, limit, group by, having等）
  let afterWhereRegex = /(\s+(order|limit|group|having)\s+.*)/i
  let afterWhereMatch = query.match(afterWhereRegex)
  let afterWherePart = afterWhereMatch ? afterWhereMatch[0] : '' // where后面的所有子句的字符串（如果有的话）
  let beforeWherePart = query // where前面的所有子句的字符串（默认为整个查询语句）
  // 如果有where后面的子句，则从query中截取掉
  if (afterWherePart) {
    beforeWherePart = query.slice(0, -afterWherePart.length)
  }

  // 然后把where按条件插入进去，再拼接之前截取出的
  if (beforeWherePart.toLowerCase().includes('where')) {
    // 如果有where部分，说明已经有过滤条件
    // 则在where部分后面用and连接新的过滤条件
    return beforeWherePart + ' and ' + filter + afterWherePart + ';'
  } else {
    // 如果没有where部分，说明没有过滤条件
    // 则在beforeWherePart的末尾添加where子句和过滤条件
    return beforeWherePart + ' where ' + filter + afterWherePart + ';'
  }
}
