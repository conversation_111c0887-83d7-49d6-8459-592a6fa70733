const utils = require('util')
const DbConn = require('../base-datasource')
const pg = require('pg')
const { HIGHGODB_TYPES } = require('../../extend/constant')
const SeatomException = require('../../exception/seatomException')
const { ERROR_CODES } = require('../../extend/constant')
const { Parser } = require('node-sql-parser')
const { formattedDate,
        wrapAggregate,
        buildSelectSql } = require('../../extend/sql-utils')
const parser = new Parser()

/**
 * 瀚高数据库数据源实现
 * 基于PostgreSQL兼容性，使用pg驱动进行连接
 */
function HighgoDB(config, service) {
  this.config = {
    user: String(config.username) || 'highgo',
    database: config.database,
    host: String(config.host) || 'localhost',
    password: String(config.password) || '',
    port: config.port || 5866, // 瀚高数据库默认端口
    // 连接池配置
    max: 20, // 连接池最大连接数
    idleTimeoutMillis: 30000, // 连接最大空闲时间 30s
    connectionTimeoutMillis: 10000, // 连接超时时间 10s
    // 瀚高数据库特有配置
    ssl: config.ssl || false,
    application_name: config.application_name || 'seatom-server'
  }
  // 合并配置，用户配置优先
  this.config = Object.assign(this.config, config)
  this.service = service
  this.pool = null // 连接池实例
}

utils.inherits(HighgoDB, DbConn)



/**
 * 获取数据库列表
 */
HighgoDB.prototype.dblist = async function () {
  const sql = 'SELECT datname as schema_name FROM pg_database WHERE datistemplate = false;'
  const res = await this.execute(sql)
  return res
}

/**
 * 获取表列表（树形结构）
 */
HighgoDB.prototype.getTreeList = async function () {
  const schema = this.config.schema || 'public'
  const sql = `
    SELECT table_name as TABLE_NAME
    FROM information_schema.tables
    WHERE table_schema = '${schema}'
    AND table_type = 'BASE TABLE'
    ORDER BY table_name
  `
  const res = await this.execute(sql)
  const result = res.rows.map((item)=>({TABLE_NAME: item.table_name}))
  return result || []
}

/**
 * 获取字段列表
 */
HighgoDB.prototype.getFieldList = async function (config) {
  const { ctx } = this.service
  let dateFields = []
  let stringFields = []
  let numberFields = []

  const schema = this.config.schema || 'public'
  const sql = `
    SELECT column_name as name, data_type as data_type
    FROM information_schema.columns
    WHERE table_schema = '${schema}'
    AND table_name = '${config.tbName}'
    ORDER BY ordinal_position
  `

  const res = await this.execute(sql)
  res.rows.forEach((item) => {
    const dataType = item.data_type.toUpperCase()
    if (HIGHGODB_TYPES.STRING_TYPE.includes(dataType)) {
      stringFields.push(item)
    } else if (HIGHGODB_TYPES.NUMBER_TYPE.includes(dataType)) {
      numberFields.push(item)
    } else if (HIGHGODB_TYPES.DATE_TYPE.includes(dataType)) {
      dateFields.push(item)
    } else {
      // 未知类型默认按字符串处理
      stringFields.push(item)
    }
  })

  const fieldList = [
    {
      type: 'date',
      fields: dateFields
    },
    {
      type: 'string',
      fields: stringFields
    },
    {
      type: 'number',
      fields: numberFields
    }
  ]

  return fieldList
}

/**
 * 获取数据
 */
HighgoDB.prototype.getData = async function (
  baseConfig,
  componentConfig,
  queryParams
) {

  let sql, numFile, datefile

  if (componentConfig.advanced) {
    sql = queryParams.params.targetSql || componentConfig.sql
    this.service.ctx.logger.info('高级模式 SQL:', sql)
    try {
      const ast = parser.astify(sql.replace(/[\u4e00-\u9fa5]/g, 't'))
      if (Array.isArray(ast)) {
        for (let i = 0; i < ast.length; i++) {
          if (ast[i].type !== 'select') {
            throw new Error()
          }
        }
      } else if (ast.type !== 'select') {
        throw new Error()
      }
    } catch (error) {
      throw new SeatomException(
        ERROR_CODES.PARAMETER_IS_REQUIRED,
        'SQL格式错误，仅支持SELECT语句'
      )
    }
  } else {
    try {
      const obj = await this.hanleData(
        baseConfig,
        componentConfig,
        queryParams,
        'highgodb'
      )
      sql = obj.sql
      numFile = obj.numFile
      datefile = obj.datefile
    } catch (error) {
      this.service.ctx.logger.error('hanleData 执行失败:', error)
      throw error
    }
  }

  this.service.ctx.logger.info('准备执行 SQL:', sql)
  const result = (await this.query(sql)).rows || []

  // 格式化数据（仅在非高级模式下进行）
  if (!componentConfig.advanced && numFile && datefile) {
    for (let index = 0; index < result.length; index++) {
      const item = result[index]

      // 格式化日期字段
      for (let d = 0; d < datefile.length; d++) {
        const dateitem = datefile[d]
        if (item.hasOwnProperty(dateitem.name) && dateitem.dateFormatter) {
          result[index][dateitem.name] = formattedDate(
            item[dateitem.name],
            dateitem.dateFormatter
          )
        }
      }

      // 格式化数值字段
      for (let d = 0; d < numFile.length; d++) {
        const numFiletem = numFile[d]
        if (
          item.hasOwnProperty(numFiletem.name) &&
          numFiletem.formatter &&
          numFiletem.formatter.num.digit &&
          numFiletem.formatter.num.unit
        ) {
          result[index][numFiletem.name] = (
            item[numFiletem.name] / numFiletem.formatter.num.unit
          ).toFixed(numFiletem.formatter.num.digit)
        }
      }
    }
  }
  
  return result
}

HighgoDB.prototype.hanleData = async function(baseConfig, componentConfig, queryParams) {
  const selectList = []
  const groupByList = []
  let orderByList = []
  let whereList = ''
  
  if (!queryParams.workspaceId && queryParams.workspaceId !== 0) {
    throw new SeatomException(ERROR_CODES.PARAMETER_IS_REQUIRED, '缺失workspaceId参数')
  }

  // 处理维度字段，使用双引号
  componentConfig.fields.dimension.forEach(element => {
    if (element.name) {
      if (element.original_name) {
        if (element.name === element.original_name) {
          selectList.push('"' + element.original_name + '"')
        } else {
          selectList.push('"' + element.original_name + '" AS "' + element.name + '"')
        }
        groupByList.push('"' + element.original_name + '"')
      } else {
        selectList.push('"' + element.name + '" AS "' + element.name + '"')
        groupByList.push('"' + element.name + '"')
      }
    }
  })

  // 处理数值字段
  let hasNumField = false
  const numFile = []
  const datefile = []
  
  componentConfig.fields.numericalValue.forEach(element => {
    if (element.name && element.calculation) {
      hasNumField = true
      const fieldStr = wrapAggregate(
        element.calculation,
        element.original_name ? '"' + element.original_name + '"' : '"' + element.name + '"',
        'highgodb',
        element.data_type,
        element.name + '(' + element.calculation + ')'
      )
      numFile.push({
        name: element.name + '(' + element.calculation + ')',
        formatter: element.formatter
      })
      selectList.push(fieldStr)
    }
  })

  if (selectList.length === 0) {
    throw new SeatomException(ERROR_CODES.SQL_ERROR, '字段列表为空')
  }

  // 处理where条件
  if (componentConfig.where.enable) {
    whereList = this.processWhereCondition(componentConfig.where.whereCondition)
    
    // 处理排序
    if (componentConfig.where.orderCondition && componentConfig.where.orderCondition.length) {
      orderByList = processOrderCondition(componentConfig.where.orderCondition)
    }
  }

  const limitNum = componentConfig.isLimit ? componentConfig.limitNum : 500
  const sql = buildSelectSql(
    componentConfig.tbName,
    selectList,
    whereList,
    groupByList,
    orderByList,
    limitNum,
    'highgodb',
    hasNumField
  )

  return { sql, numFile, datefile }
}

/**
 * 查询方法
 */
HighgoDB.prototype.query = async function (sql) {
  // 校验SQL格式
  if (!sql || typeof sql !== 'string') {
    throw new Error('SQL语句格式不正确')
  }

  // 去掉SQL两端空格
  sql = sql.trim()

  // SQL安全校验，仅允许SELECT语句
  if (!sql.toLowerCase().startsWith('select')) {
    throw new Error("查询SQL必须以'SELECT'开头")
  }
  const res = await this.execute(sql)
  return res
}

/**
 * 获取连接池实例
 */
HighgoDB.prototype.getPool = function () {
  if (!this.pool) {
    this.pool = new pg.Pool({
      host: this.config.host,
      port: this.config.port,
      user: this.config.user,
      password: this.config.password,
      database: this.config.database,
      max: this.config.max,
      idleTimeoutMillis: this.config.idleTimeoutMillis,
      connectionTimeoutMillis: this.config.connectionTimeoutMillis,
      ssl: this.config.ssl,
      application_name: this.config.application_name
    })

    // 监听连接池事件
    this.pool.on('error', (err) => {
      this.service.ctx.logger.error('瀚高数据库连接池错误', err)
    })

    this.pool.on('connect', () => {
      this.service.ctx.logger.info('瀚高数据库连接池建立新连接')
    })
  }
  return this.pool
}

/**
 * 执行SQL方法（使用连接池）
 */
HighgoDB.prototype.execute = function (sql) {
  const pool = this.getPool()

  return new Promise((resolve, reject) => {
    // 记录连接开始时间
    const startTime = Date.now()

    pool.connect((err, client, done) => {
      if (err) {
        const duration = Date.now() - startTime

        // 详细的错误日志
        this.service.ctx.logger.error('瀚高数据库连接失败', {
          error: err.message,
          code: err.code,
          duration: `${duration}ms`,
          config: {
            host: this.config.host,
            port: this.config.port,
            database: this.config.database,
            user: this.config.user
          }
        })

        // 根据错误类型提供具体的错误信息
        let errorMessage = '瀚高数据库连接失败'
        if (err.code === 'ECONNREFUSED') {
          errorMessage = '瀚高数据库连接被拒绝，请检查数据库服务是否启动'
        } else if (err.code === 'ENOTFOUND') {
          errorMessage = '瀚高数据库主机地址无法解析，请检查主机配置'
        } else if (err.code === 'ETIMEDOUT' || err.message.includes('timeout')) {
          errorMessage = '瀚高数据库连接超时，请检查网络连接、防火墙设置和数据库负载'
        } else if (err.message && err.message.includes('authentication')) {
          errorMessage = '瀚高数据库认证失败，请检查用户名和密码'
        } else if (err.message && err.message.includes('database') && err.message.includes('does not exist')) {
          errorMessage = '瀚高数据库不存在，请检查数据库名称'
        }

        reject(new SeatomException(ERROR_CODES.DATABASE_CONNECT_ERROR, `${errorMessage}: ${err.message}`))
        return
      }

      const connectDuration = Date.now() - startTime
      this.service.ctx.logger.info(`瀚高数据库连接成功，耗时: ${connectDuration}ms`)

      client.query(sql, (err, result) => {
        done() // 释放连接回连接池

        if (err) {
          const totalDuration = Date.now() - startTime
          this.service.ctx.logger.error('瀚高数据库查询失败', {
            sql,
            error: err.message,
            code: err.code,
            duration: `${totalDuration}ms`
          })
          reject(new SeatomException(ERROR_CODES.SQL_ERROR, `瀚高数据库查询失败: ${err.message}`))
          return
        }

        const totalDuration = Date.now() - startTime
        this.service.ctx.logger.debug(`瀚高数据库查询成功，耗时: ${totalDuration}ms，返回 ${result.rowCount} 行`)
        resolve(result)
      })
    })
  })
}

/**
 * 处理WHERE条件（瀚高数据库特殊处理）
 */
HighgoDB.prototype.processWhereCondition = function(whereCondition) {
  const { generateSign } = require('../../extend/sql-utils')
  const { compare } = require('../../extend/sql-utils')
  const moment = require('moment')

  let whereSql = ''
  const relative = [
    'today',
    'lastday',
    'last7Days',
    'last30Days',
    'last90Days',
    'lastYear'
  ]

  whereCondition.forEach((item, index) => {
    if (relative.includes(item.compare)) {
      item.compareValue = this.relativeDate(item)
    }

    const isFirstItem = index === 0
    const isLastItem = whereCondition.length === index + 1
    const composeOperator = item.composeType === 'or' ? ' ) OR ' : ' ) AND '

    // 瀚高数据库使用双引号引用字段名（兼容PostgreSQL）
    if (item.compare === 'unequal' && item.type === 'date') {
      whereSql += isFirstItem
        ? ` ${generateSign(whereCondition.length)} COALESCE("${item.field}",'') `
        : `COALESCE("${item.field}",'') `
    } else if (item.compare === 'contained') {
      whereSql += isFirstItem
        ? ` ${generateSign(whereCondition.length)} '${item.compareValue[0]}'`
        : `'${item.compareValue[0]}'`
    } else {
      whereSql += isFirstItem
        ? ` ${generateSign(whereCondition.length)} "${item.field}" `
        : `"${item.field}" `
    }

    // 处理比较值
    switch (item.compareValue.length) {
      case 0:
        whereSql += compare[item.type][item.compare] + (isLastItem ? ' ' : composeOperator)
        break
      case 1:
        let value = item.compareValue[0]
        if (item.type === 'string' || item.type === 'date') {
          value = `'${value}'`
        }
        whereSql += compare[item.type][item.compare].replace('{0}', value) +
          (isLastItem ? ' ' : composeOperator)
        break
      case 2:
        let value1 = item.compareValue[0]
        let value2 = item.compareValue[1]
        if (item.type === 'string' || item.type === 'date') {
          value1 = `'${value1}'`
          value2 = `'${value2}'`
        }
        whereSql += compare[item.type][item.compare]
          .replace('{0}', value1)
          .replace('{1}', value2) + (isLastItem ? ' ' : composeOperator)
        break
      default:
        if (item.compareValue.length > 2) {
          const inItem = JSON.stringify(item.compareValue).replace(/\[|]/g, '')
          whereSql += compare[item.type][item.compare].replace('{0}', inItem) +
            (isLastItem ? ' ' : composeOperator)
        }
    }
  })

  return whereSql
}

/**
 * 连接测试方法
 */
HighgoDB.prototype.testConnection = async function () {
  try {
    const result = await this.execute('SELECT version() as version, current_database() as database')
    return {
      success: true,
      message: '瀚高数据库连接成功',
      data: result.rows[0]
    }
  } catch (error) {
    return {
      success: false,
      message: `瀚高数据库连接失败: ${error.message}`,
      error: error
    }
  }
}

/**
 * 关闭连接池
 */
HighgoDB.prototype.close = async function () {
  if (this.pool) {
    try {
      await this.pool.end()
      this.pool = null
      this.service.ctx.logger.info('瀚高数据库连接池已关闭')
    } catch (error) {
      this.service.ctx.logger.error('关闭瀚高数据库连接池失败', error)
    }
  }
}

module.exports = HighgoDB
