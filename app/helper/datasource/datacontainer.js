const DbConn = require('../base-datasource')
const util = require('util')
const csvtojson = require('csvtojson')
const path = require('path')
const SeatomException = require('../../exception/seatomException')
const { ERROR_CODES } = require('../../extend/constant')

function DataContainer(config, service) {
  this.appConfig = service.config
}

DataContainer.prototype.getData = async function (baseConfig, componentConfig) {
  return []
}

util.inherits(DataContainer, DbConn)

module.exports = DataContainer
