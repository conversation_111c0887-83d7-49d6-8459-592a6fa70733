var utils = require('util')
const DbConn = require('../base-datasource')
var pg = require('pg')
const { PG_TYPES } = require('../../extend/constant')
const SeatomException = require('../../exception/seatomException')
const { ERROR_CODES } = require('../../extend/constant')
const { Parser } = require('node-sql-parser')
const { formattedDate } = require('../../extend/sql-utils')
const parser = new Parser()

// 数据库配置
function PostGreSql(config, service) {
  this.config = {
    user: String(config.username) || 'postgres',
    database: config.database,
    host: String(config.host) || 'localhost',
    password: String(config.password) || '123456',
    port: config.port || 5432,
    // 扩展属性
    max: 20, // 连接池最大连接数
    idleTimeoutMillis: 3000 // 连接最大空闲时间 3s
  }
  // 拼接其他属性，已设置的属性优先级较高
  this.config = Object.assign(config, this.config)
  this.service = service
}

utils.inherits(PostGreSql, DbConn)

function handlegetData(data) {
  if(data.schema.length > 0 && data.data.length > 0) {
    let res = data.data.map((item)=> {
      const obj = {}
      data.schema.forEach((it,index) => {
        obj[it.name] = item[index]
      })
      return obj
    })
    console.log('处理后的res',res);
    return res
  }
}

PostGreSql.prototype._request = async function (url, sql) {
  const { host, port, user, password, database } = this.config
  const { ctx } = this.service
  const base = this.service.config.PDBC.address
  const connStr = `jdbc:postgresql://${host}:${port}/${database}?useCursorFetch=true&useUnicode=true&autoReconnect=true&useSSL=false`
  let params = {
    conn_str: connStr,
    driver_str: 'org.postgresql.Driver',
    driver_jar: '',
    username: user,
    password
  }
  if (sql) {
    params = Object.assign(params, { sql })
  }
  let result;
  try {
    result = await ctx.curl(base + url, {
      headers: {
        'Content-Type': 'application/json'
      },
      dataType: 'json',
      data: params,
      method: 'POST',
      timeout: 5 * 60 * 1000
    })
  } catch (error) {
    ctx.logger.info('jdbc接口报错', params, error)
    throw new SeatomException(400, error)
  }
  ctx.logger.info('_request返回值', result, result.data.status)
  if (result.data.status == 'success') {
    return result.data.result
  } else {
    throw new SeatomException(+result.code, result.data.reason)
  }
}

PostGreSql.prototype.dblist = async function () {
  // pg数据库好像用不上这个接口，先观望一下
  var sql = 'select schema_name from information_schema.schemata;'
  const res = await this.execute(sql)
  return res
}

// 查询所有表

PostGreSql.prototype.getTreeList = async function () {
  if(this.service.config.PDBC && this.service.config.PDBC.enable){
    const url = `/sqltree/tables?catalog=${this.config.database}&schema=public`
    const result = await this._request(url)
    const res = result.map((item) => {
      return {tablename:item}
    })
    return {rows:res}
  } else {
    var sql =
    "select table_name as tablename from information_schema.tables where table_schema = 'public'"
    const res = await this.execute(sql)
    return res
  }
}

// 查询所有字段
PostGreSql.prototype.getFieldList = async function (config) {
  const { ctx } = this.service
  let dateFields = [],
    stringFields = [],
    numberFields = []
  if (this.service.config.PDBC && this.service.config.PDBC.enable){
    const url = `/sqltree/columns?catalog=${config.database}&schema=public&table=${config.tbName}`
    const result = await this._request(url)
    ctx.logger.info('查询字段获取的数据', result)
    let list = Object.keys(result)
    list.forEach((key) => {
      if (result[key].type == 'string') {
        stringFields.push({name:result[key].name, data_type:result[key].raw_type})
      }
      if (result[key].type == 'number') {
        numberFields.push({name:result[key].name, data_type:result[key].raw_type})
      }
      if (result[key].type == 'date') {
        dateFields.push({name:result[key].name, data_type:result[key].raw_type})
      }
    })
  } else {
    const sql = `select column_name as name, data_type as data_type from information_schema.columns where table_schema ='public' and table_name = '${config.tbName}' ;`
    // 处理返回的数据类型
    const res = await this.execute(sql)
    res.rows.map((item, value) => {
      if (PG_TYPES.STRING_TYPE.includes(item.data_type.toUpperCase())) {
        stringFields.push(item)
      }
      if (PG_TYPES.NUMBER_TYPE.includes(item.data_type.toUpperCase())) {
        numberFields.push(item)
      }
      if (PG_TYPES.DATE_TYPE.includes(item.data_type.toUpperCase())) {
        dateFields.push(item)
      }
    })
  }
  const fieldList = []
  fieldList.push({
    type: 'date',
    fields: dateFields
  })
  fieldList.push({
    type: 'string',
    fields: stringFields
  })
  fieldList.push({
    type: 'number',
    fields: numberFields
  })
  return fieldList
}

// PostGreSql.prototype.getData = async function (baseConfig, data) {
//     var sql = data.sql;
//     return await this.query(sql);
// }

PostGreSql.prototype.getData = async function (
  baseConfig,
  componentConfig,
  queryParams
) {
  let sql, numFile, datefile
  if (componentConfig.advanced) {
    sql = queryParams.params.targetSql || componentConfig.sql
    try {
      const ast = parser.astify(sql.replace(/[\u4e00-\u9fa5]/g, 't'))
      if (Array.isArray(ast)) {
        for (let i = 0; i < ast.length; i++) {
          if (ast[i].type !== 'select') {
            throw new Error()
          }
        }
      } else if (ast.type !== 'select') {
        throw new Error()
      }
    } catch (error) {
      throw new SeatomException(
        ERROR_CODES.PARAMETER_IS_REQUIRED,
        'sql格式错误'
      )
    }
  } else {
    const obj = await this.hanleData(
      baseConfig,
      componentConfig,
      queryParams,
      'postgresql'
    )
    sql = obj.sql
    numFile = obj.numFile
    datefile = obj.datefile
  }

  const mysqlResult = (await this.query(sql)).rows || []
  console.log(mysqlResult, numFile, datefile)
  for (let index = 0; index < mysqlResult.length; index++) {
    const mysqlitem = mysqlResult[index]
    for (let d = 0; d < datefile.length; d++) {
      const dateitem = datefile[d]
      if (mysqlitem.hasOwnProperty(dateitem.name) && dateitem.dateFormatter) {
        mysqlResult[index][dateitem.name] = formattedDate(
          mysqlitem[dateitem.name],
          dateitem.dateFormatter
        )
      }
    }
    for (let d = 0; d < numFile.length; d++) {
      const numFiletem = numFile[d]
      if (
        mysqlitem.hasOwnProperty(numFiletem.name) &&
        numFiletem.formatter &&
        numFiletem.formatter.num.digit &&
        numFiletem.formatter.num.unit
      ) {
        mysqlResult[index][numFiletem.name] = (
          mysqlitem[numFiletem.name] / numFiletem.formatter.num.unit
        ).toFixed(numFiletem.formatter.num.digit)
      }
    }
  }
  return mysqlResult
}

PostGreSql.prototype.query = async function (sql) {
  // 校验sql变量格式
  if (!sql || typeof sql != 'string') {
    throw new Error('sql statement format is illegal')
  }

  // 去掉sql两端空格
  sql = sql.replace(/^\s+|\s+$/g, '')

  // 做一个简单的sql校验，防止用户写了个drop之类的东西搞事情
  if (sql.substring(0, 6).toLowerCase() != 'select') {
    throw new Error("query sql must start with 'select'")
  }
  if(this.service.config.PDBC && this.service.config.PDBC.enable) {
    const shortUrl = '/sqltree/query'
    const res = handlegetData(await this._request(shortUrl, sql))
    return res
  } else {
    const res = await this.execute(sql)
    return res
  }
}

PostGreSql.prototype.execute = function (sql) {
  // to_do: 目前为了防止连接数过多，采取查完即关闭连接，需要优化
  // var connection = pg.createConnection(this.config);
  var pool = new pg.Pool({
    host: this.config.host,
    port: this.config.port,
    user: this.config.username,
    password: this.config.password,
    database: this.config.database
  })
  return new Promise(function (resolve, reject) {
    pool.connect(function (err, client, done) {
      if (err) {
        reject(err)
        return
      }
      client.query(sql, function (err, result) {
        done()
        if (err) {
          reject(err)
          return
        }
        resolve(result)
      })
    })
  })
}

exports = module.exports = PostGreSql
