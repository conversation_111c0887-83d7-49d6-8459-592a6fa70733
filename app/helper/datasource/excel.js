const DbConn = require('../base-datasource')
const util = require('util')
const csvtojson = require('csvtojson')
const path = require('path')
const SeatomException = require('../../exception/seatomException')
const { ERROR_CODES } = require('../../extend/constant')
const alasql = require('alasql')
const { findIndex } = require('lodash')
const xlsx = require('node-xlsx')
const {
  wrapMongoAggregate,
  mongoProjectAggregate,
  mongoMatchAggregate,
  buildMongoSort

} = require('../../extend/mongo-utils')
function Excel(config, service) {
  this.appConfig = service.config || ''
  this.ctx = service.ctx
  this.app = service.app
}
const {
  buildSelectSql,
  replaceNameToFid,
  generateSign,
  wrapAggregate,
  compare,
  formattedDate
} = require('../../extend/sql-utils')
Excel.prototype.getData = async function (
  baseConfig,
  componentConfig,
  queryParams
) {
  try {
    let numFile, datefile, mysqlResult;
    if(queryParams.isNewFlag) {
      // 说明是新的表 走新逻辑
      const obj = await this.handleNewData(componentConfig, queryParams)
      numFile = obj.numFile
      datefile = []
      let pipe = obj.finalPipe
      let str = JSON.stringify(pipe)
      this.ctx.logger.info('finalPipe',str);
      mysqlResult = await this.mongoQuery(pipe, componentConfig.tbId)
    } else {
      // 旧表 走旧逻辑
      const obj = await this.hanleData(
        baseConfig,
        componentConfig,
        queryParams,
        'mysql'
      )
      let sql = obj.sql
      numFile = obj.numFile
      datefile = obj.datefile
      mysqlResult = await this.query(sql)
    }
    
    for (let index = 0; index < mysqlResult.length; index++) {
      const mysqlitem = mysqlResult[index]
      for (let d = 0; d < datefile.length; d++) {
        const dateitem = datefile[d]
        if (mysqlitem.hasOwnProperty(dateitem.name) && dateitem.dateFormatter) {
          mysqlResult[index][dateitem.name] = formattedDate(
            mysqlitem[dateitem.name],
            dateitem.dateFormatter
          )
        }
      }
      for (let d = 0; d < numFile.length; d++) {
        const numFiletem = numFile[d]
        if (
          mysqlitem.hasOwnProperty(numFiletem.name) &&
          numFiletem.formatter &&
          numFiletem.formatter.num.digit &&
          numFiletem.formatter.num.unit
        ) {
          mysqlResult[index][numFiletem.name] = (
            mysqlitem[numFiletem.name] / numFiletem.formatter.num.unit
          ).toFixed(numFiletem.formatter.num.digit)
        }
      }
    }
    return mysqlResult
  } catch (err) {
    throw new SeatomException(500, `getData错误${err}`)
  }
}
Excel.prototype.getTreeList = async function (config) {
  let { filePath } = config
  if (!filePath) {
    throw new SeatomException(ERROR_CODES.FILE_NOT_EXIST, '未找到excel文件')
  }
  if (config.isNewFlag) {
    // 如果说isNewFlag 是true 换句话说是新添加的excel，在excelBase里有能索引的到的东西的
     let excelRes = await this.ctx.model.Excel.findOne({id: config.id})
     const res = excelRes.sheetList.map((sheet) => {
        return {TABLE_NAME: sheet.sheetName, TABLE_ID: sheet.sheetId}
      })
      return res
  } else {
    // 原本的excel 走老逻辑
    filePath = filePath.replace('public/', '/')
    const excleFilePath = path.resolve(
      this.appConfig.resourcePath,
      `./${filePath}`
    )
    const res = []
    const excelArray = xlsx.parse(excleFilePath)
    for (let index = 0; index < excelArray.length; index++) {
      const element = excelArray[index]
      res.push({ TABLE_NAME: element.name })
    }
    return res
  }
}
Excel.prototype.getFieldList = async function (config) {
  let { filePath, fieldsType } = config
  if (!filePath) {
    throw new SeatomException(ERROR_CODES.FILE_NOT_EXIST, '未找到excel文件')
  }
  let all = [];
  let allFields;
  if (config.isNewFlag) {
    // 从excelBase里获取
    let excelRes = await this.ctx.model.Excel.findOne({id: config.id})
    const target = excelRes.sheetList.filter((item) => {
      return item.sheetName == config.tbName
    })
    all = target[0].sheetFields.map((item)=>{
      return item.fieldName
    })
  } else {
    // 旧方法 alasql
    filePath = filePath.replace('public/', '/')
    const excleFilePath = path.resolve(
      this.appConfig.resourcePath,
      `./${filePath}`
    )
    const excelArray = xlsx.parse(excleFilePath)
    allFields = excelArray.filter(item => {
      return item.name === config.tbName
    })
  }
  const stringFields = all.length == 0 ? allFields[0].data[0] : all
  const dateFields = [],
    numberFields = []
  const fieldList = []
  const newStringFields = []
  for (let index = 0; index < stringFields.length; index++) {
    const element = stringFields[index]
    if (fieldsType && fieldsType[element]) {
      switch (fieldsType[element]) {
        case 'number':
          numberFields.push({
            name: element,
            data_type: 'int'
          })
          break
        case 'string':
          newStringFields.push({
            name: element,
            data_type: 'varchar'
          })
          break
        case 'date':
          dateFields.push({
            name: element,
            data_type: 'date'
          })
          break
      }
      continue
    }
    newStringFields.push({
      name: element,
      data_type: 'varchar'
    })
  }
  fieldList.push({
    type: 'date',
    fields: dateFields
  })
  fieldList.push({
    type: 'string',
    fields: newStringFields
  })
  fieldList.push({
    type: 'number',
    fields: numberFields
  })
  return fieldList
}

Excel.prototype.preview = async function (baseConfig) {
  let { filePath } = baseConfig
  if (!filePath) {
    throw new SeatomException(ERROR_CODES.FILE_NOT_EXIST, '未找到excel文件')
  }
  filePath = filePath.replace('public/', '/')
  const excleFilePath = path.resolve(
    this.appConfig.resourcePath,
    `./${filePath}`
  )
  console.log('====',filePath, excleFilePath);
  const excelArraylist = await alasql([
    `select * from XLSX("${excleFilePath}")`
  ])
  const excelArray = excelArraylist[0]
  return excelArray.slice(0, excelArray.length >= 10 ? 10 : excelArray.length)
}

Excel.prototype.hanleData = async function (
  baseConfig,
  componentConfig,
  queryParams,
  dateBaseType
) {
  // 记录四个模块的内容
  let selectList = [],
    whereList = '',
    groupByList = [],
    orderByList = []
  if (
    !queryParams ||
    (!queryParams.workspaceId && queryParams.workspaceId !== 0)
  ) {
    throw new SeatomException(
      ERROR_CODES.PARAMETER_IS_REQUIRED,
      '缺失workspaceId参数'
    )
  }
  // 处理维度字段列表
  const fieldList = []
  let datefile = []
  const dimension = componentConfig.fields.dimension
  dimension.forEach((element, index) => {
    if (element.name) {
      if (element.original_name) {
        selectList.push(
          '`' + element.original_name + '` AS `' + element.name + '`'
        )
        groupByList.push('`' + element.original_name + '`')
        orderByList.push('`' + element.original_name + '`')
      } else {
        selectList.push('`' + element.name + '` AS `' + element.name + '`')
        groupByList.push('`' + element.name + '`')
        orderByList.push('`' + element.name + '`')
      }
      if (element.data_type == 'date') {
        datefile.push({
          name: element.name,
          dateFormatter: element.dateFormatter
        })
      }
      // 有别名加别名，没有别名字段先加名称
      fieldList.push('`' + element.name + '`')
    }
  })

  // 处理数值字段列表
  let numFile = []
  const numerical = componentConfig.fields.numericalValue
  numerical.forEach((element, index) => {
    if (element.name && element.calculation) {
      let fieldStr
      if (element.original_name) {
        fieldStr = wrapAggregate(
          element.calculation,
          '`' + element.original_name + '`',
          'excel',
          element.data_type,
          element.name + '_' + element.calculation
        )
      } else {
        fieldStr = wrapAggregate(
          element.calculation,
          '`' + element.name + '`',
          'excel',
          element.data_type
        )
      }
      selectList.push(fieldStr)
      numFile.push({
        name: element.name + '(' + element.calculation + ')',
        formatter: element.formatter
      })
    }
  })
  if (selectList.length == 0) {
    throw new SeatomException(ERROR_CODES.SQL_ERROR, '字段列表为空')
  }
  const where = componentConfig.where
  let whereSql = ''
  const relative = [
    'today',
    'lastday',
    'last7Days',
    'last30Days',
    'last90Days',
    'lastYear'
  ]
  if (where.enable) {
    
    where.whereCondition.forEach((item, index) => {
      console.log(item, '什么脚本')
      // 处理筛选条件，包括今天、昨天、最近7天、最近30天、最近90天、最近一年、全部
      if (relative.includes(item.compare)) {
        item.compareValue = this.relativeDate(item)
      }
      {
        if (
          item.compare == 'unequal' &&
          dateBaseType == 'mysql' &&
          item.type == 'date'
        ) {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                "IFNULL('" +
                item.field +
                "',' ')" +
                ' '
              : "IFNULL('" + item.field + "',' ')" + ' '
        } else if (item.compare == 'contained') {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                "'" +
                item.compareValue[0] +
                "'"
              : "'" + item.compareValue[0] + "'"
        } else {
          whereSql +=
            index == 0
              ? ` ${generateSign(where.whereCondition.length)} ` +
                '`' +
                item.field +
                '`' +
                ' '
              : '`' + item.field + '`' + ' '
        }
      }
      switch (item.compareValue.length) {
        case 0:
          whereSql +=
            compare[item.type][item.compare] +
            (where.whereCondition.length == index + 1
              ? ' '
              : item.composeType == 'or'
              ? ' ) OR '
              : ' ) AND ')
          break
        case 1:
          if (item.type == 'string') {
            if (
              ['matchOnEnd', 'matchOnStart', 'notContain', 'contain'].includes(
                item.compare
              )
            ) {
              if (dateBaseType == 'mysql') {
                whereSql +=
                  compare[item.type][item.compare].replace(
                    '{0}',
                    `${item.compareValue[0]}`
                  ) +
                  (where.whereCondition.length == index + 1
                    ? ' '
                    : item.composeType == 'or'
                    ? ' ) OR '
                    : ' ) AND ')
              } else {
                whereSql +=
                  compare[item.type][item.compare].replace(
                    '{0}',
                    `${item.compareValue[0]}`
                  ) +
                  (where.whereCondition.length == index + 1
                    ? ' '
                    : item.composeType == 'or'
                    ? ' ) OR '
                    : ' ) AND ')
              }
            } else if (item.compare == 'contained') {
              whereSql += compare[item.type][item.compare].replace(
                '{0}',
                `'${item.fid}'`
              )
              +(where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
            } else {
              whereSql +=
                compare[item.type][item.compare].replace(
                  '{0}',
                  `'${item.compareValue[0]}'`
                ) +
                (where.whereCondition.length == index + 1
                  ? ' '
                  : item.composeType == 'or'
                  ? ' ) OR '
                  : ' ) AND ')
            }
          }
          if (item.type == 'date') {
            const compareValue = moment(item.compareValue[0]).format(
              'YYYY-MM-DD HH:mm:ss'
            )
            whereSql +=
              compare[item.type][item.compare].replace(
                '{0}',
                `'${compareValue}'`
              ) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          }
          if (item.type == 'number') {
            whereSql +=
              compare[item.type][item.compare].replace(
                '{0}',
                item.compareValue[0]
              ) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          }
          break
        case 2:
          let compareValue1, compareValue2
          if (item.type == 'date') {
            compareValue1 = JSON.stringify(
              moment(item.compareValue[0]).format('YYYY-MM-DD HH:mm:ss')
            )
            compareValue2 = JSON.stringify(
              moment(item.compareValue[1]).format('YYYY-MM-DD HH:mm:ss')
            )
          } else {
            compareValue1 = item.compareValue[0]
            compareValue2 = item.compareValue[1]
          }
          if (relative.includes(item.compare)) {
            whereSql +=
              compare[item.type][item.compare]
                .replace('{0}', compareValue1)
                .replace('{1}', item.field)
                .replace('{2}', compareValue2) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          } else {
            whereSql +=
              compare[item.type][item.compare]
                .replace('{0}', compareValue1)
                .replace('{1}', compareValue2) +
              (where.whereCondition.length == index + 1
                ? ' '
                : item.composeType == 'or'
                ? ' ) OR '
                : ' ) AND ')
          }
          break
      }
    })

    whereList = whereSql
    // 兼容数据处理功能对orderby
    where.orderCondition.forEach((item, index) => {
      if (item.calculation) {
        // 按年、月、日、时、分、秒
        const field = calulateDate(item.calculation, item.fid)
        orderByList.unshift(field + ' ' + item.orderBy)
      } else {
        const orderByListIndex = findIndex(orderByList, o => {
          return o === '`' + item.field + '`'
        })
        if (orderByListIndex > -1) {
          orderByList.splice(orderByListIndex, 1)
          orderByList.unshift('`' + item.field + '`' + ' ' + item.orderBy)
        } else {
          orderByList.unshift('`' + item.field + '`' + ' ' + item.orderBy)
        }
      }
    })
    orderByList.forEach((item, index) => {
      if (item.search('desc') == -1 && item.search('asc') == -1) {
        orderByList[index] = orderByList[index] + ' ' + ' asc '
      }
    })
  } else {
    whereList = ''
    orderByList = []
  }
  let { filePath } = baseConfig
  if (!filePath) {
    throw new SeatomException(ERROR_CODES.FILE_NOT_EXIST, '未找到excle文件')
  }
  filePath = filePath.replace('public/', '/')
  filePath = filePath.replace('.xlsx', '')
  const excleFilePath = path.resolve(
    this.appConfig.resourcePath,
    `./${filePath}`
  )
  const tbNameStr = `XLSX("${excleFilePath}",{sheetid:"${componentConfig.tbName}"})`
  const sql = buildSelectSql(
    tbNameStr,
    selectList,
    whereList,
    // '`' + '流入1' + '`' + `like '%湖%'`,
    groupByList,
    orderByList,
    5000,
    queryParams.type || dateBaseType
  )
  this.ctx.logger.info('excel数据源的sql', sql, selectList)
  return {
    sql,
    numFile,
    datefile
  }
}

Excel.prototype.handleNewData = async function (
  componentConfig,
  queryParams,
) {
  this.ctx.logger.info('componentConfig',componentConfig.tbId);
  if (
    !queryParams ||
    (!queryParams.workspaceId && queryParams.workspaceId !== 0)
  ) {
    throw new SeatomException(
      ERROR_CODES.PARAMETER_IS_REQUIRED,
      '缺失workspaceId参数'
    )
  }
  const projectPipe = {
    $project:{
      _id:0,
    }
  }
  const groupPipe = {
    $group:{
      _id:{}
    }
  }
  const sortPipe = {
    $sort:{}
  }
  const limitPipe = {
    $limit:5000
  }
  const finalPipe = [{$unwind: "$sheetData"}]
  // 处理维度字段列表
  let datefile = []
  const dimension = componentConfig.fields.dimension
  dimension.forEach((element, index) => {
    if ( element.name ) {
      const name = element.name
      const originName = element.original_name ? element.original_name : name
      // group里添加对应的属性，类似groupBy 操作
      groupPipe.$group._id[ originName ] = `$sheetData.${ originName }`
      // project里添加对应的内容
      projectPipe.$project[ name ] = `$_id.${ originName }`
      // 排序
      sortPipe.$sort[ name ] = 1
    }
  })

  // 处理数值字段列表
  let numFile = []
  const numerical = componentConfig.fields.numericalValue
  numerical.forEach((element, index) => {
    if (element.name && element.calculation) {
      const name = element.name
      const originName = element.original_name ? element.original_name : name
      const newName = name + '(' + element.calculation + ')' // 拼完后的新名称
      // 拼group字段
      groupPipe.$group[ newName ] = wrapMongoAggregate(element.type, element.calculation, originName)
      // 拼project
      projectPipe.$project[newName] = mongoProjectAggregate(element.type, element.calculation,newName)
      // numfile 不知道干啥的 先放着 等会再看
      numFile.push({
        name: element.name + '(' + element.calculation + ')',
        formatter: element.formatter
      })
    }
  })
  if(dimension.length == 0 && numerical.length == 0) {
    throw new SeatomException(ERROR_CODES.SQL_ERROR, '字段列表为空!')
  }
  
  // console.log('numfile',numFile);

  const where = componentConfig.where
  let matchPipe = {}
  let functionRes;
  console.log('where', where);
  if (where.enable) {
    matchPipe = mongoMatchAggregate(where.whereCondition)
    console.log('matchPipe',matchPipe);
    console.log('whereorder',where.orderCondition);

    functionRes = buildMongoSort(where)
  } else {
    // sortPipe = {}
  }
  console.log('sortPipe',sortPipe);
  finalPipe.push(groupPipe,projectPipe,limitPipe)
  if(where.enable) {
    finalPipe.push(matchPipe)
    if (functionRes.addFields) {
      finalPipe.push({$addFields:functionRes.addFields})
    }
    if(functionRes.sortObj) {
      sortPipe["$sort"] = {...sortPipe["$sort"], ...functionRes.sortObj}
      let list = Object.keys(functionRes.sortObj)
      // 将更新过后的字段放在首位
      for (let i = 0;i<list.length;i++){
        const overrideName = list[i]
        const overrideVal = functionRes.sortObj[overrideName]
        
        delete sortPipe["$sort"][overrideName]
        sortPipe["$sort"] = {[overrideName]: overrideVal, ...sortPipe["$sort"]}
      }
      console.log('sortPipenew',sortPipe);
      finalPipe.push(sortPipe)
    }
  }
  return {finalPipe, numFile}
}

Excel.prototype.mongoQuery = async function (pipe, id) {
  const { app } = this
  try {
    const res = await app.mongoose.connection.collection( `Sheet_${ id }` ).aggregate( pipe ).toArray()
    // this.ctx.logger.info('res',res);
    return res
  } catch(err) {
    throw new SeatomException(401,'查询出错')
  }
  
}



Excel.prototype.query = async function (sql, timeout = 20 * 1000) {
  // 校验sql变量格式
  if (!sql || typeof sql !== 'string') {
    console.warn('ql statement format is illegal', sql)
    throw new Error('sql statement format is illegal')
  }

  // 去掉sql两端空格
  sql = sql.replace(/^\s+|\s+$/g, '')

  // 做一个简单的sql校验，防止用户写了个drop之类的东西搞事情
  if (sql.substring(0, 6).toLowerCase() != 'select') {
    throw new Error("query sql must start with 'select'")
  }
  try {
    const tokenstart = new Date().getTime()
    console.log(sql, 'original_namepppp-----')
    // 使用promise.race 增加超时机制
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Query timed out')), timeout);
    });
    const res = await Promise.race([
      alasql.promise([sql]),
      timeoutPromise
    ]);
    const tokenend = new Date().getTime()
    this.ctx.logger.info('请求dmc接口时间', `${tokenend - tokenstart}hzms`)
    return res[0]
  } catch (error) {
    return error
  }
}
// 根据sql查询excle内容
util.inherits(Excel, DbConn)
module.exports = Excel
