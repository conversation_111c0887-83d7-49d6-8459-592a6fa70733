/*
 * @Description: 编辑连接
 * @Date: 2022-11-14 11:03:10
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */
const isEmpty = require('lodash/isEmpty')
const { getLoggerModel } = require('../../extend/utils')
const {
  getCoeditRoomInfoBySocketId,
  getCoeditUsersByScreenId,
  getScreenCoeditRoomsRedisKey,
  getScreenCoeditRootRoom
} = require('../../utils/coedit')

// 删除房间
const deleteRoomsByIf = (rooms, ifFn) => {
  for (const key in rooms) {
    const room = rooms[key]
    let len = room.length - 1

    while (len >= 0) {
      if (room[len] && ifFn(room[len].socketId)) {
        room.splice(len, 1)

        if (room.length === 0) {
          delete rooms[key]
        }

        return {
          hasDelete: true
        }
      }
      len--
    }
  }

  return {
    hasDelete: false
  }
}

module.exports = app => {
  return async (ctx, next) => {
    // redis key
    const rdsKey = getScreenCoeditRoomsRedisKey(ctx.app.config.env)

    /**
     * socket 连接处理
     */
    const connectAction = async () => {
      try {
        // 获取房间信息
        const rootRoom = await getScreenCoeditRootRoom(
          ctx.service.cacheservicedb1,
          rdsKey
        )

        // 当前场景大屏房间组
        const currentScenePageRoomGroup =
          rootRoom.scenePageRoomGroups[rootRoom.socketIdMap[ctx.id]]

        const editorNsp = ctx.app.io.of('/editor')

        // 所有连接着的socket id
        // const sids = [ctx.id, ...Object.keys(editorNsp.adapter.sids)]

        // 所有连接着的socket id
        // 获取连接到此命名空间的客户端ID的列表（如果适用，则跨所有节点）
        editorNsp.clients(async (error, sids) => {
          if (error) {
            throw error
          }

          ctx.logger.info(
            getLoggerModel({
              message: 'editor socket connect editorNsp.clients',
              data: {
                sids: sids,
                'editorNsp.adapter.sids': Object.keys(editorNsp.adapter.sids)
              }
            })
          )

          // 删除大屏房间
          let deleteRes = deleteRoomsByIf(rootRoom.screenRooms, sid => {
            return sids.includes(sid) === false
          })

          let hasDelete = deleteRes.hasDelete

          // 删除删除场景大屏页面房间
          if (currentScenePageRoomGroup) {
            deleteRes = deleteRoomsByIf(currentScenePageRoomGroup, sid => {
              return sids.includes(sid) === false
            })

            if (isEmpty(currentScenePageRoomGroup)) {
              delete rootRoom.scenePageRoomGroups[rootRoom.socketIdMap[ctx.id]]
            }
            if (!hasDelete) {
              hasDelete = deleteRes.hasDelete
            }
          }

          // 删除不存在的socketId
          for (const key in rootRoom.socketIdMap) {
            if (!sids.includes(key)) {
              hasDelete = true
              delete rootRoom.socketIdMap[key]
            }
          }

          // 删除不存在分组
          for (const key in rootRoom.screenRooms) {
            if (!Object.values(rootRoom.socketIdMap).includes(key)) {
              hasDelete = true
              delete rootRoom.screenRooms[key]
            }
          }

          // 删除不存在分组
          for (const key in rootRoom.scenePageRoomGroups) {
            if (!Object.values(rootRoom.socketIdMap).includes(key)) {
              hasDelete = true
              delete rootRoom.scenePageRoomGroups[key]
            }
          }

          // console.log('clients', editorNsp.clients())
          // console.log('sids', Object.keys(editorNsp.adapter.sids))
          // console.log('ctx.client.sockets', ctx.client.sockets)
          // console.log('ctx.app.io.sockets.sockets', ctx.app.io.sockets.sockets)
          // console.log('=========')

          if (hasDelete) {
            await ctx.service.cacheservicedb1.set(rdsKey, rootRoom)
          }

          // 链接成功同步
          ctx.socket.emit('allScreenCoeditList', rootRoom.screenRooms)
        })

        ctx.logger.info(
          getLoggerModel({
            message: 'editor socket connect',
            data: {
              id: ctx.id
            }
          })
        )
      } catch (error) {
        ctx.logger.info(
          getLoggerModel({
            message: 'editor socket connect error',
            data: {
              id: ctx.id,
              error: error.toString()
            }
          })
        )
      }
    }

    await connectAction()

    // 放行
    await next()

    /**
     * socket 断开连接处理
     */
    const disconnectAction = async () => {
      try {
        // 获取房间信息
        const rootRoom = await getScreenCoeditRootRoom(
          ctx.service.cacheservicedb1,
          rdsKey
        )

        // 当前场景大屏房间组
        const currentScenePageRoomGroup =
          rootRoom.scenePageRoomGroups[rootRoom.socketIdMap[ctx.id]]

        // 当前场景大屏页面房间
        const currentScenePageRoom = getCoeditRoomInfoBySocketId(
          currentScenePageRoomGroup,
          ctx.id
        )

        const editorNsp = ctx.app.io.of('/editor')

        // 删除大屏房间
        let deleteRes = deleteRoomsByIf(rootRoom.screenRooms, sid => {
          return sid === ctx.id
        })

        // 是否有删除
        let hasDelete = deleteRes.hasDelete

        // 删除场景大屏页面房间
        if (currentScenePageRoomGroup) {
          deleteRes = deleteRoomsByIf(currentScenePageRoomGroup, sid => {
            return sid === ctx.id
          })

          // 如果分组为空，删除空对象
          if (isEmpty(currentScenePageRoomGroup)) {
            delete rootRoom.scenePageRoomGroups[rootRoom.socketIdMap[ctx.id]]
          }

          if (!hasDelete) {
            hasDelete = deleteRes.hasDelete
          }
        }

        // 断线广播
        editorNsp.emit('allScreenCoeditList', rootRoom.screenRooms)

        if (currentScenePageRoom) {
          // 进入房间广播
          editorNsp
            .to(`screen-editor-${currentScenePageRoom.screenId}`)
            .emit('screenCoeditInfo', {
              screenId: currentScenePageRoom.screenId,
              screenType: currentScenePageRoom.screenType,
              coeditUsers: getCoeditUsersByScreenId(
                rootRoom.scenePageRoomGroups[rootRoom.socketIdMap[ctx.id]],
                currentScenePageRoom.screenId
              )
            })
        }

        // 删除socketId关联
        if (rootRoom.socketIdMap[ctx.id]) {
          hasDelete = true
          delete rootRoom.socketIdMap[ctx.id]
        }

        if (hasDelete) {
          await ctx.service.cacheservicedb1.set(rdsKey, rootRoom)
        }

        ctx.logger.info(
          getLoggerModel({
            message: 'editor socket disconnect',
            data: {
              id: ctx.id
            }
          })
        )
      } catch (error) {
        ctx.logger.info(
          getLoggerModel({
            message: 'editor socket disconnect error',
            data: {
              id: ctx.id,
              error: error.toString()
            }
          })
        )
      }
    }

    await disconnectAction()
  }
}
