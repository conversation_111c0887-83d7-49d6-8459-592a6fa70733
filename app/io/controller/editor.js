/*
 * @Description: 编辑页socket Io
 * @Date: 2022-11-14 15:20:52
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */
const Controller = require('egg').Controller
const isFunction = require('lodash/isFunction')
const isEmpty = require('lodash/isEmpty')
const { getLoggerModel } = require('../../extend/utils')
const {
  getCoeditUsersByScreenId,
  getScreenCoeditRoomsRedisKey,
  getScreenCoeditRootRoom
} = require('../../utils/coedit')

const empty = () => {}

class EditorController extends Controller {
  // 加入大屏
  async joinScreen() {
    const { app, socket, logger } = this.ctx

    const params = this.ctx.args[0] || {}
    const callback = isFunction(this.ctx.args[1]) ? this.ctx.args[1] : empty

    try {
      // 房间名
      const room = `screen-editor-${params.screenId}`
      // 场景大屏
      const isSceneScreen = params.screenType === 'scene'

      const rdsKey = getScreenCoeditRoomsRedisKey(this.config.env)

      const rootRoom = await getScreenCoeditRootRoom(
        this.service.cacheservicedb1,
        rdsKey
      )

      if (!rootRoom.screenRooms[room]) {
        rootRoom.screenRooms[room] = []
      }

      // 如果这个房间已存在
      if (
        rootRoom.screenRooms[room] &&
        rootRoom.screenRooms[room].length !== 0
      ) {
        // const sockets = socket.nsp.adapter.rooms[room].sockets
        // const sockets = {}
        // rootRoom.screenRooms[room].forEach(item => {
        //   sockets[item.socketId] === true
        // })
        const coeditUsers = rootRoom.screenRooms[room]

        // for (const id in sockets) {
        //   const data = rootRoom.screenRooms[room].find(item => {
        //     return item.socketId === id
        //   })
        //   coeditUsers.push({
        //     ...data
        //   })
        // }

        let index
        let message
        // 场景大屏
        if (isSceneScreen) {
          index = coeditUsers.findIndex(user => {
            return user.socketId === socket.id
          })
          message = '你正在编辑中，无需重复进入'
        } else {
          index = coeditUsers.findIndex(user => {
            return user.screenId === params.screenId
          })
          message = `${coeditUsers[index].userName}编辑中，不允许同时编辑同一大屏`
        }

        if (index !== -1) {
          const res = {
            success: false,
            message: message,
            coeditUsers
          }
          callback(res)

          logger.info(
            getLoggerModel({
              message: 'joinScreenFail',
              data: {
                params,
                res
              }
            })
          )
          return
        }
      }

      // 加入房间
      socket.join(room)

      // 缓存数据
      if (rootRoom.screenRooms[room]) {
        rootRoom.screenRooms[room].push({
          ...params,
          socketId: socket.id
        })

        rootRoom.socketIdMap[socket.id] = room
      }

      await this.service.cacheservicedb1.set(rdsKey, rootRoom)

      callback({
        success: true,
        message: `${params.userName || ''}加入大屏成功`
      })

      // 连同自己一起广播
      const editorNsp = this.ctx.app.io.of('/editor')
      editorNsp.emit('allScreenCoeditList', rootRoom.screenRooms)

      // 场景大屏
      if (isSceneScreen) {
        //  发送给自己
        socket.emit('screenCoeditInfo', {
          screenId: params.screenId,
          screenType: 'scene',
          coeditUsers: getCoeditUsersByScreenId(
            rootRoom.scenePageRoomGroups[room],
            params.screenId
          )
        })
      }

      logger.info(
        getLoggerModel({
          message: 'joinScreenSuccess',
          data: {
            params,
            'socket.nsp.adapter.rooms': socket.nsp.adapter.rooms,
            'socket.adapter.rooms': socket.adapter.rooms
          }
        })
      )
    } catch (error) {
      callback({
        success: false,
        message: error.toString()
      })

      logger.info(
        getLoggerModel({
          message: 'joinScreenError',
          data: {
            params,
            error
          }
        })
      )
    }
  }

  // 退出大屏
  async exitScreen() {
    const { app, socket, logger } = this.ctx
    const params = this.ctx.args[0] || {}
    const callback = isFunction(this.ctx.args[1]) ? this.ctx.args[1] : empty

    try {
      // 房间名
      const room = `screen-editor-${params.screenId}`

      const rdsKey = getScreenCoeditRoomsRedisKey(this.config.env)

      // 检查是否在房间内
      // if (!socket.nsp.adapter.rooms[room]) {
      //   const res = {
      //     success: false,
      //     message: '退出大屏失败，请先加入房间',
      //     room
      //   }
      //   callback(res)

      //   logger.info(
      //     getLoggerModel({
      //       message: 'exitScreenFail',
      //       data: {
      //         params,
      //         res
      //       }
      //     })
      //   )
      //   return
      // }

      const rootRoom = await getScreenCoeditRootRoom(
        this.service.cacheservicedb1,
        rdsKey
      )

      if (!rootRoom.screenRooms[room] || !rootRoom.screenRooms[room].length) {
        const res = {
          success: false,
          message: `退出大屏失败，找不到房间`,
          screenRooms: rootRoom.screenRooms,
          room
        }
        callback(res)

        logger.info(
          getLoggerModel({
            message: 'exitScreenFail',
            data: {
              params,
              res
            }
          })
        )
        return
      }

      const index = rootRoom.screenRooms[room].findIndex(item => {
        return item.userId === params.userId
      })

      if (index !== -1) {
        rootRoom.screenRooms[room].splice(index, 1)

        if (rootRoom.screenRooms[room].length === 0) {
          delete rootRoom.screenRooms[room]
        }

        if (rootRoom.socketIdMap[socket.id]) {
          delete rootRoom.socketIdMap[socket.id]
        }

        await this.service.cacheservicedb1.set(rdsKey, rootRoom)
      }

      // 离开房间
      socket.leave(room)

      callback({
        success: true,
        message: `${params.userName || ''}退出大屏成功`
      })

      // 连同自己一起广播
      const editorNsp = this.ctx.app.io.of('/editor')
      editorNsp.emit('allScreenCoeditList', rootRoom.screenRooms)

      logger.info(
        getLoggerModel({
          message: 'exitScreenSuccess',
          data: {
            params
          }
        })
      )
    } catch (error) {
      callback({
        success: false,
        message: error.toString()
      })

      logger.info(
        getLoggerModel({
          message: 'exitScreenError',
          data: {
            params,
            error
          }
        })
      )
    }
  }

  // 加入场景页面
  async joinScenePage() {
    const { app, socket, logger } = this.ctx
    const params = this.ctx.args[0] || {}
    const callback = isFunction(this.ctx.args[1]) ? this.ctx.args[1] : empty

    // 失败执行
    const joinScenePageFail = (res = {}) => {
      callback(res)

      logger.info(
        getLoggerModel({
          message: 'joinScenePageFail',
          data: {
            params,
            res
          }
        })
      )
    }

    try {
      // 房间分组
      const scenePageRoomGroupName = `screen-editor-${params.screenId}`

      // 是否是页面
      const isPage = !!params.pageId

      // 是否是场景
      const isScene = !params.pageId

      // 房间名
      const room = isScene ? `scene-${params.sceneId}` : `page-${params.pageId}`

      const rdsKey = getScreenCoeditRoomsRedisKey(this.config.env)

      const rootRoom = await getScreenCoeditRootRoom(
        this.service.cacheservicedb1,
        rdsKey
      )

      if (!rootRoom.scenePageRoomGroups[scenePageRoomGroupName]) {
        rootRoom.scenePageRoomGroups[scenePageRoomGroupName] = {}
      }

      if (!rootRoom.scenePageRoomGroups[scenePageRoomGroupName][room]) {
        rootRoom.scenePageRoomGroups[scenePageRoomGroupName][room] = []
      }

      // 如果这个房间已存在
      if (
        rootRoom.scenePageRoomGroups[scenePageRoomGroupName][room] &&
        rootRoom.scenePageRoomGroups[scenePageRoomGroupName][room].length !== 0
      ) {
        // const sockets = socket.nsp.adapter.rooms[room].sockets
        // const sockets = {}
        // rootRoom.scenePageRoomGroups[scenePageRoomGroupName][room].forEach(
        //   item => {
        //     sockets[item.socketId] === true
        //   }
        // )

        const coeditUsers =
          rootRoom.scenePageRoomGroups[scenePageRoomGroupName][room]

        // for (const id in sockets) {
        //   const data = rootRoom.scenePageRoomGroups[scenePageRoomGroupName][
        //     room
        //   ].find(item => {
        //     return item.socketId === id
        //   })
        //   coeditUsers.push({
        //     ...data
        //   })
        // }

        let index
        if (isScene) {
          index = coeditUsers.findIndex(user => {
            return user.sceneId === params.sceneId
          })
        } else {
          index = coeditUsers.findIndex(user => {
            return user.pageId === params.pageId
          })
        }

        if (index !== -1) {
          joinScenePageFail({
            success: false,
            message: `${coeditUsers[index].userName}编辑中，不允许同时编辑同一页面`,
            coeditUsers
          })
          return
        }
      }

      const screenInfo = await this.service.screen.findOne({
        id: Number(params.screenId)
      })

      const sceneConfig =
        (screenInfo &&
          screenInfo.sceneConfig &&
          screenInfo.sceneConfig.toObject()) ||
        []

      // 检查场景是否被删除
      const currScene =
        sceneConfig.find(item => {
          return item.sceneId === params.sceneId
        }) || {}

      if (isScene && !currScene) {
        joinScenePageFail({
          success: false,
          message: `加入场景失败，该场景已被删除，请刷新`
        })
        return
      }

      // 检查场景页面是否被删除
      const currPage = (currScene.pageList || []).find(page => {
        return page.pageId === params.pageId
      })

      if (isPage && !currPage) {
        joinScenePageFail({
          success: false,
          message: `加入页面失败，该页面已被删除，请刷新`
        })
        return
      }

      // 加入房间
      socket.join(room)

      // 缓存数据
      if (rootRoom.scenePageRoomGroups[scenePageRoomGroupName][room]) {
        // 如果已加入其他页面（房间），先删除数据
        for (const key in rootRoom.scenePageRoomGroups[
          scenePageRoomGroupName
        ]) {
          const elements =
            rootRoom.scenePageRoomGroups[scenePageRoomGroupName][key]
          if (elements && elements[0] && elements[0].socketId === socket.id) {
            delete rootRoom.scenePageRoomGroups[scenePageRoomGroupName][key]
          }
        }
        // 添加新数据
        rootRoom.scenePageRoomGroups[scenePageRoomGroupName][room].push({
          ...params,
          socketId: socket.id
        })

        await this.service.cacheservicedb1.set(rdsKey, rootRoom)
      }

      callback({
        success: true,
        message: `${params.userName || ''}加入场景页面成功`
      })

      // 广播
      this.ctx.app.io
        .of('/editor')
        .to(`screen-editor-${params.screenId}`)
        .emit('screenCoeditInfo', {
          screenId: params.screenId,
          screenType: 'scene',
          coeditUsers: getCoeditUsersByScreenId(
            rootRoom.scenePageRoomGroups[scenePageRoomGroupName],
            params.screenId
          )
        })

      logger.info(
        getLoggerModel({
          message: 'joinScenePageSuccess',
          data: {
            params
          }
        })
      )
    } catch (error) {
      callback({
        success: false,
        message: error.toString()
      })

      logger.info(
        getLoggerModel({
          message: 'joinScenePageError',
          data: {
            params,
            error
          }
        })
      )
    }
  }

  // 退出场景页面
  async exitScenePage() {
    const { app, socket, logger } = this.ctx
    const params = this.ctx.args[0] || {}
    const callback = isFunction(this.ctx.args[1]) ? this.ctx.args[1] : empty

    // 失败执行
    const joinScenePageFail = (res = {}) => {
      callback(res)

      logger.info(
        getLoggerModel({
          message: 'joinScenePageFail',
          data: {
            params,
            res
          }
        })
      )
    }

    try {
      // 房间分组
      const scenePageRoomGroupName = `screen-editor-${params.screenId}`

      // 是否是页面
      const isPage = !!params.pageId

      // 是否是场景
      const isScene = !params.pageId

      // 房间名
      const room = isScene ? `scene-${params.sceneId}` : `page-${params.pageId}`

      const rdsKey = getScreenCoeditRoomsRedisKey(this.config.env)

      const rootRoom = await getScreenCoeditRootRoom(
        this.service.cacheservicedb1,
        rdsKey
      )

      // 检查是否在房间内
      // if (!socket.nsp.adapter.rooms[room]) {
      //   joinScenePageFail({
      //     success: false,
      //     message: '退出场景页面失败，请先加入房间',
      //     scenePageRoomGroupName,
      //     room
      //   })
      //   return
      // }

      // 当前场景页面房间组
      const currentScenePageRoomGroup =
        rootRoom.scenePageRoomGroups[scenePageRoomGroupName]

      if (currentScenePageRoomGroup && currentScenePageRoomGroup[room]) {
        const index = currentScenePageRoomGroup[room].findIndex(item => {
          return item.userId === params.userId
        })

        if (index !== -1) {
          currentScenePageRoomGroup[room].splice(index, 1)

          // 删除空
          if (currentScenePageRoomGroup[room].length === 0) {
            delete currentScenePageRoomGroup[room]
          }

          // 删除空
          if (isEmpty(currentScenePageRoomGroup)) {
            delete rootRoom.scenePageRoomGroups[scenePageRoomGroupName]
          }

          await this.service.cacheservicedb1.set(rdsKey, rootRoom)
        }
      } else {
        joinScenePageFail({
          success: false,
          message: '退出场景页面失败，请先加入房间',
          currentScenePageRoomGroup,
          scenePageRoomGroupName,
          room
        })
      }

      // 离开房间
      socket.leave(room)

      callback({
        success: true,
        message: `${params.userName || ''}退出场景页面成功`
      })

      // 广播
      this.ctx.app.io
        .of('/editor')
        .to(`screen-editor-${params.screenId}`)
        .emit('screenCoeditInfo', {
          screenId: params.screenId,
          screenType: 'scene',
          coeditUsers: getCoeditUsersByScreenId(
            rootRoom.scenePageRoomGroups[scenePageRoomGroupName],
            params.screenId
          )
        })

      logger.info(
        getLoggerModel({
          message: 'exitScenePageSuccess',
          data: {
            params
          }
        })
      )
    } catch (error) {
      callback({
        success: false,
        message: error.toString()
      })

      logger.info(
        getLoggerModel({
          message: 'exitScenePageError',
          data: {
            params,
            error
          }
        })
      )
    }
  }
}

module.exports = EditorController
