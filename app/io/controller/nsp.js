'use strict'

const Controller = require('egg').Controller

class NspController extends Controller {
  async exchange() {
    const { ctx, app } = this
    const nsp = app.io.of('/linkage')
    const message = ctx.args[0] || {}
    const socket = ctx.socket
    const client = socket.id
    const query = socket.handshake.query
    try {
      const { room, msg } = message
      nsp.to(room).emit('linkage', msg)
    } catch (error) {
      app.logger.error(error)
    }
  }
}

module.exports = NspController
