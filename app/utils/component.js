/*
 * @Description: 组件工具函数
 * @Date: 2023-05-18 17:16:31
 * @Author: chen<PERSON><PERSON><PERSON>
 * @LastEditors: chenxingyu
 * @FilePath: /gd-seatom-server/app/utils/component.js
 */

/**
 * 获取提示条件数据结构
 * @param {object} tips
 * @returns
 */
const getNewTipsConditionsModel = (tips = {}) => {
  return {
    type: tips.type || 'empty',
    field: tips.field || '',
    condition: tips.condition || '==',
    value: tips.value || '0',
    info: tips.info || '暂无数据',
    background: {
      show: true,
      type: 'pure',
      pure: tips.background || '#fff',
      gradient: {
        type: 'linear-gradient',
        deg: 0,
        start: '#0000ff',
        startScale: 0,
        end: '#ffc0cb',
        endScale: 100,
        shape: 'ellipse'
      },
      image: {
        url: '',
        size: '100% 100%',
        positionX: 'center',
        positionY: 'center',
        repeat: 'no-repeat'
      }
    },
    font: {
      fontSize: 14,
      color: tips.color || '#999',
      fontFamily: '系统自带字体',
      fontWeight: 'bold'
    }
  }
}

/**
 * 获取提示数据结构
 * @returns
 */
const getNewTipsModel = () => {
  return {
    open: false,
    type: 'empty',
    field: '',
    condition: '==',
    value: '0',
    info: '暂无数据',
    background: '#fff',
    color: '#999',
    conditions: [getNewTipsConditionsModel({})]
  }
}

module.exports = {
  getNewTipsConditionsModel,
  getNewTipsModel
}
