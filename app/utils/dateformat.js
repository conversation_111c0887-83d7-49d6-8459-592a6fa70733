/*
 * @Author: wuf<PERSON> <EMAIL>
 * @Date: 2024-04-19 17:00:50
 * @LastEditors: wufei <EMAIL>
 * @LastEditTime: 2024-04-29 11:11:40
 * @FilePath: /seatom-server/app/utils/dateformat.js
 * @Description: 时间状态处理函数
 */

const dateformat = {
    
    /**
     * @description: 处理时间格式
     * @param {*} date
     * @param {*} format yyyy-MM-dd等
     * @return {*}
     */    
    format: function ( date, format) {
        const year = date.getFullYear()
        const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份从 0 开始，需要加 1
        const day = date.getDate().toString().padStart(2, '0')
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
    
        const result = format
        .replace('yyyy', year)
        .replace('MM', month)
        .replace('dd', day)
        .replace('hh', hours)
        .replace('mm', minutes)
        .replace('ss', seconds)
        
        // console.log('result######',result)
        return result
    }
}

module.exports = dateformat
