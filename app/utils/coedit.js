/*
 * @Description: 协同编辑工具函数
 * @Date: 2022-11-25 17:12:22
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */

/**
 * 通过socketId获取协同编辑房间信息
 * @param {*} rooms
 * @param {*} socketId
 * @returns
 */
const getCoeditRoomInfoBySocketId = (rooms, socketId) => {
  let current
  for (const key in rooms) {
    const elements = rooms[key]
    if (!elements || !elements.length) {
      continue
    }
    const element = elements[0]
    if (element.socketId === socketId) {
      current = element
      break
    }
  }
  return current
}

/**
 * 通过screenId获取协同编辑用户
 * @param {*} rooms
 * @param {*} screenId
 * @returns
 */
const getCoeditUsersByScreenId = (rooms = {}, screenId) => {
  const coeditUsers = []

  for (const key in rooms) {
    const elements = rooms[key]
    if (!elements || !elements.length) {
      continue
    }
    const element = elements[0]
    if (element.screenId === screenId) {
      coeditUsers.push(element)
    }
  }
  return coeditUsers
}

/**
 * 通过 env 获取screenCoeditRooms key
 * @param {string} env
 * @returns
 */
const getScreenCoeditRoomsRedisKey = env => {
  return `screenCoeditRooms_${env}`
}

/**
 * 从redis获取screenCoeditRooms
 * @param {*} cacheservicedb1
 * @param {strign} rdsKey redis key
 * @returns
 */
const getScreenCoeditRootRoom = async (cacheservicedb1, rdsKey) => {
  return (
    (await cacheservicedb1.get(rdsKey)) || {
      socketIdMap: {},
      screenRooms: {},
      scenePageRoomGroups: {}
    }
  )
}

module.exports = {
  getCoeditRoomInfoBySocketId,
  getCoeditUsersByScreenId,
  getScreenCoeditRoomsRedisKey,
  getScreenCoeditRootRoom
}
