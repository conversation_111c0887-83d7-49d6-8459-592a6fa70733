/*
 * @Description: 大屏工具函数
 * @Date: 2022-11-18 17:26:46
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */
const _ = require('lodash')
const { encryptPath } = require('../extend/crypto')

/**
 * 大屏数据转换
 * @param {Object} screen
 * @returns
 */
exports.screenDataTransfer = screen => {
  let screenData = _.pick(screen, [
    'screenType',
    'type',
    'name',
    'createdAt',
    'updatedAt',
    'id',
    'templateId',
    'isScreentpl',
    'projectId',
    'isDynamicScreen',
    'coeditInfo',
    'coeditId',
    'shareCollection'
  ])
  let { config: screenConfig = {} } = screen
  const start =
    screenConfig['thumbnail'] && screenConfig['thumbnail'].indexOf('/public/')
  const thumbnail =
    screenConfig['thumbnail'] &&
    screenConfig['thumbnail'].substring(start + 7).split('?')[0]
  const encryptThumbnail = thumbnail && encryptPath(thumbnail)
  // let screenshare = await ctx.service.screenshare.findOne({ screenId: screen.id });
  //const domain = screenConfig['thumbnail']&& screenConfig['thumbnail'].substring(0,start)
  _.extend(screenData, {
    thumbnail: screenConfig['thumbnail'],
    encryptThumbnail: encryptThumbnail && encryptThumbnail,
    isPublic: false,
    shareUrl: '',
    width: screenConfig['width'],
    heiht: screenConfig['height'],
    isConnect: screen.isConnect ? screen.isConnect : false // 添加是否开启多端控制
  })
  return screenData
}

// 获取图层列表和索引数组
exports.getLayerListAndIndexByLayerIds = (originLayerTree, layerIds) => {
  const selectedIndexs = []
  const selectedList = []
  let currentList
  let currentParentList

  // 递归获取
  const getList = (list, parentList) => {
    for (let index = 0; index < list.length; index++) {
      const item = list[index]

      if (layerIds.includes(item.id)) {
        if (!currentList) {
          currentList = list
        }
        selectedIndexs.push(index)
        selectedList.push(list[index])

        currentParentList = parentList
      }

      if (selectedIndexs.length === layerIds.length) {
        return
      }

      // 如果是分组，递归
      if (item.type === 'group') {
        getList(item.children, list)
        if (selectedIndexs.length === layerIds.length) {
          return
        }
      }
    }
  }

  getList(originLayerTree)

  return {
    selectedIndexs,
    selectedList,
    currentList,
    currentParentList
  }
}
