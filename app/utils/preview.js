/*
 * @Description: 预览发布页工具
 * @Date: 2023-04-13 18:44:09
 * @Author: chen<PERSON>yu
 * @LastEditors: chenxingyu
 */

// 获取预览组件Projection
const getPrevComProjection = () => {
  return {
    _id: 0,
    __v: 0,
    controlConfig: 0,
    packageCreateDate: 0,
    requirePath: 0,
    createdAt: 0,
    updatedAt: 0,
    icon: 0,
    alias: 0,
    ordinary: 0,
    needCommon: 0,
    other: 0
  }
}

/**
 * 组件列表转换为对象
 * @param {array} componentList
 * @returns
 */
const componentListToObj = componentList => {
  // 删除不要的数据
  const components = componentList.reduce((obj, com) => {
    const comObj = com.toObject()
    const sourceType = comObj.dataConfig.dataResponse.sourceType

    if (sourceType === 'static') {
      comObj.dataConfig.dataResponse.source = {}
    } else {
      comObj.staticData = []
      comObj.dataConfig.dataResponse.source = {
        [sourceType]: comObj.dataConfig.dataResponse.source[sourceType]
      }
    }

    obj[comObj.id] = comObj
    return obj
  }, {})
  return components
}

/**
 * 获取大屏、面板组合后的数据结构
 * @param {object} screenInfo 大屏数据对象
 * @param {object} mergeObject 要合并覆盖的数据对象
 * @returns
 */
const getCombinationScreenModel = (screenInfo = {}, mergeObject = {}) => {
  return Object.assign(
    {
      id: screenInfo.id,
      name: screenInfo.name,
      parentId: screenInfo.parentId || null,
      relationCompId: screenInfo.relationCompId || null,
      config: screenInfo.config,
      layers: screenInfo.layerTree,
      workspaceId: screenInfo.workspaceId,
      sceneConfig: screenInfo.sceneConfig,
      screenType: screenInfo.screenType,
      variableList: screenInfo.variableList,
      type: screenInfo.type,
      voiceControl: screenInfo.voiceControl || false,
      permissionDataConfig: screenInfo.permissionDataConfig,
      filter: [],
      isDynamicScreen: screenInfo.isDynamicScreen || false,
      components: {}
    },
    mergeObject
  )
}

module.exports = {
  getPrevComProjection,
  componentListToObj,
  getCombinationScreenModel
}
