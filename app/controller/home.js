'use strict'
const _ = require('lodash')
const Controller = require('egg').Controller
const fs = require('fs-extra')
const path = require('path')
const { getResponseBody, uuid } = require('../extend/utils')
const alasql = require('alasql')
class HomeController extends Controller {
  async index() {
    const { ctx, config } = this
    try {
      const gitInfo = fs.readFileSync(
        path.resolve(__dirname, '../name.txt'),
        'utf-8'
      )
      ctx.body = gitInfo
      return
    } catch (error) {
      ctx.body = error
    }
  }
}

module.exports = HomeController
