'use strict'
// 版本信息
const _ = require('lodash')
const Controller = require('egg').Controller
const fs = require('fs-extra')
const path = require('path')
const { getResponseBody, uuid } = require('../extend/utils')
const alasql = require('alasql')

class HomeController extends Controller {
  async index() {
    const { ctx, config } = this
    console.log(this.config.env)
    if (this.config.env == "local") {
      ctx.body = "==============伏羲本地开发后台启动了=============="
      return
    }
    try {
      const gitInfo = fs.readFileSync(
        path.resolve(__dirname, '../name.txt'),
        'utf-8'
      )
      ctx.body = gitInfo
      return
    } catch (error) {
      ctx.body = error
    }
  }
}

module.exports = HomeController
