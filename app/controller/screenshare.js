const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
const _ = require('lodash')
const md5 = require('md5')

class ScreenShareController extends Controller {
  async index() {
    const { ctx, config } = this
    // const { screenId, shareToken } = ctx.query;
    const res = await ctx.service.screenshare.getScreenShareByShareToken(
      ctx.query
    )

    if (res) {
      ctx.body = getResponseBody(res)
    } else {
      ctx.body = getResponseBody(null, false, '请求失败', 500)
    }
  }

  async verify() {
    const { ctx } = this
    let { screenId, sharePassword } = ctx.request.body
    if (!sharePassword) {
      sharePassword = ctx.cookies.get('sharePassword')
    }
    if (!screenId || !sharePassword) {
      ctx.body = getResponseBody(null, false, '参数错误', 400)
      return
    }
    const res = await ctx.service.screenshare.findOne({
      screenId,
      sharePassword
    })
    if (res) {
      ctx.cookies.set('sharePassword', sharePassword)
      ctx.body = getResponseBody(true)
    } else {
      ctx.body = getResponseBody(false)
    }
  }

  async update() {
    const { ctx, config } = this
    const publicPath = config.publicPath
    const {
      screenId,
      isPublic,
      needPassword,
      sharePassword = '',
      needLoading,
      needAuth,
      // renderType,
      enableCache = false
    } = ctx.request.body
    let renderType = ctx.request.body.renderType || 'static'
    if (config.renderType === 'none') {
      renderType = 'none'
    }
    let sData = await ctx.service.screenshare.findOne({ screenId })
    if (!sData) {
      sData = await ctx.service.screenshare.create({ screenId })
    }
    let updateData = {
      isPublic: _.isUndefined(isPublic) ? sData.isPublic : isPublic,
      shareUrl: sData.shareUrl,
      shareToken: sData.shareToken,
      needPassword: _.isUndefined(needPassword)
        ? sData.needPassword
        : needPassword,
      sharePassword: _.isUndefined(sharePassword)
        ? sData.sharePassword
        : sharePassword,
      needLoading: _.isUndefined(needLoading) ? sData.needLoading : needLoading,
      enableCache: _.isUndefined(enableCache) ? sData.enableCache : enableCache,
      needAuth: _.isUndefined(needAuth) ? sData.needAuth : needAuth,
      renderType: _.isUndefined(renderType) ? sData.renderType : renderType
    }
    if (updateData.isPublic) {
      if (!updateData.shareUrl) {
        const shareToken = md5(`share_${screenId}_${+new Date()}`)
        updateData.shareUrl = `/screen/share/${shareToken}`
        updateData.shareToken = shareToken
      }
      if (updateData.needPassword && !updateData.sharePassword) {
        updateData.sharePassword = _.toString(
          Math.random().toString(36).substr(5)
        )
      } else if (!updateData.needPassword) {
        updateData.sharePassword = ''
      }
    } else {
      updateData.shareUrl = ''
      updateData.shareToken = ''
      updateData.needPassword = false
      updateData.needLoading = true
      updateData.sharePassword = ''
      updateData.enableCache = false
      updateData.needAuth = false
      updateData.renderType = renderType
      // 清除发布页缓存，要在screenshare.update之前
      await ctx.service.preview.clearCache({ screenId })
    }
    const updateRes = await ctx.service.screenshare.update(
      { screenId },
      updateData
    )
    if (updateRes.ok) {
      ctx.body = getResponseBody(
        _.pick(updateData, ['shareUrl', 'sharePassword', 'renderType'])
      )
    } else {
      ctx.body = getResponseBody(null, false, '更新大屏发布信息失败', 500)
    }
  }
}

module.exports = ScreenShareController
