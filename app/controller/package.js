'use strict'
// 内置组件
const _ = require('lodash')
const fs = require('fs-extra')
const path = require('path')
const compressing = require('compressing')
const Controller = require('egg').Controller
const { getResponseBody, compareVersion } = require('../extend/utils')
class PackagesController extends Controller {
  async index() {
    const { ctx } = this
    const data = await ctx.service.package.find()
    ctx.body = getResponseBody(data)
  }

  async list() {
    const { ctx } = this
    const projection = {
      createdAt: 0,
      config: 0
    }
    const data = (await ctx.service.package.find({}, projection)).map(d =>
      d.toJSON()
    )

    // 排序，只获取此组件的最新版本
    data.sort((a, b) => {
      const res = compareVersion(a.version, b.version)
      return -res
    })
    const list = _.uniqBy(data, 'name')
    const pkgMap = _.keyBy(list, 'name')
    // 处理子组件
    const children = list.reduce((arr, item) => {
      if (Array.isArray(item.children) && item.children.length) {
        arr.push(...item.children)
      }
      return arr
    }, [])
    const uniqueChildren = _.uniq(children).map(d => ({ name: d })) // 去重
    const newList = _.pullAllBy(list, uniqueChildren, 'name')
    for (const pkg of newList) {
      pkg.children =
        pkg.children && pkg.children.length
          ? pkg.children
              .map(childName => pkgMap[childName])
              .filter(d => d != null)
          : []
    }
    // const res = newList.map(item => {
    //   item.icon = '/public' + item.icon.split('public')[1]
    //   item.path = '/public' + item.path.split('public')[1]
    //   return item
    // })
    ctx.body = getResponseBody(newList)
  }

  async exist() {
    const { ctx } = this
    const { name, version } = ctx.params
    const data = await ctx.service.package.find({ name, version })
    ctx.body = getResponseBody(!!(data && data.length))
  }

  async map() {
    const { ctx, config } = this
    const res = await ctx.service.package.getPkgVersionNames()
    ctx.body = getResponseBody(res)
  }

  async create() {
    const { ctx, config } = this
    const body = ctx.request.body
    const comname = body.name
    const iscustom = comname.includes('custom-all-custom')
    if (config.env === 'prev' && !iscustom) {
      // 演示环境校验用户token
      try {
        const adminArr = ['wanghao', 'julei', 'zhangdong']
        const token = body.token
        if (!token) {
          ctx.body = getResponseBody(
            null,
            false,
            '请先升级到最新版seatom-cli，使用seatom login命令先登录！',
            400
          )
          return
        }
        const decode = ctx.app.jwt.verify(token, config.jwt.secret)
        const { userName, passWord } = decode
        const userData = await ctx.service.seatomcliuser.find({
          userName,
          passWord
        })
        if (!userData || userData.length === 0) {
          ctx.body = getResponseBody(null, false, '此用户不存在！', 400)
          return
        }
        if (
          userData[0].role !== 0 &&
          !adminArr.includes(userData[0].userName)
        ) {
          ctx.body = getResponseBody(
            null,
            false,
            '您没有权限发布组件到演示环境，请联系管理员！',
            402
          )
          return
        }
      } catch (e) {
        ctx.body = getResponseBody(
          null,
          false,
          '您没有权限发布组件到演示环境，请联系管理员！',
          402
        )
        return
      }
    }
    if (!ctx.request.files || !ctx.request.files.length) {
      ctx.body = getResponseBody(null, false, '无法创建，上传文件为空！', 400)
      return
    }
    const publicPath = config.publicPath
    const file = ctx.request.files[0]
    const uploadDir = path.resolve(config.resourcePath, './packages')
    const baseName = path.basename(file.filepath, '.gz')
    const unzipPath = path.resolve(uploadDir, baseName)
    // 解压
    await compressing.tgz.uncompress(file.filepath, unzipPath)
    // 读取 json 配置
    const jsonPath = path.resolve(unzipPath, './package.json')
    if (!fs.existsSync(jsonPath)) {
      ctx.body = getResponseBody(
        null,
        false,
        '无法创建，压缩包不存在package.json配置！',
        400
      )
      return
    }
    const pkgJSON = JSON.parse(fs.readFileSync(jsonPath, { encoding: 'utf-8' }))
    const name = pkgJSON.name
    const version = pkgJSON.version
    const chartConfig = pkgJSON.chartConfig

    // 判断包是否已存在
    const findData = await ctx.service.package.find({ name, version })
    if (findData && findData.length) {
      await ctx.service.package.delete({ name, version })
    }
    // 更新包名
    const newFilePath = path.resolve(uploadDir, `${name}@${version}`)
    if (fs.existsSync(newFilePath)) {
      fs.removeSync(newFilePath)
    }
    fs.renameSync(unzipPath, newFilePath)
    fs.removeSync(unzipPath)
    // 更新配置
    const key = `${name}@${version}`
    const newFileName = `${name}.${version}.js`
    const pathUrl = `/public/packages/${key}/${newFileName}`
    const data = await ctx.service.package.create({
      name,
      version,
      alias: chartConfig.cn_name,
      icon: chartConfig.icon,
      type: chartConfig.type,
      presetChildren: chartConfig.presetChildren,
      children: chartConfig.children,
      width: chartConfig.width,
      height: chartConfig.height,
      platform: chartConfig.platform || 'common',
      config: JSON.stringify(chartConfig),
      path: pathUrl
    })
    // 刷新缓存start
    const res = await ctx.service.package.getPkgVersionNames()
    // 缓存
    ctx.service.package.cachePackageMap(res)
    // 刷新缓存end
    ctx.body = getResponseBody(data)
  }

  async delete() {
    const { ctx, config } = this
    const { name, version } = ctx.query
    if (!name) {
      throw new Error('必须提供要删除的组件名!')
    }
    const filterObj = { name }
    if (version) {
      filterObj.version = version
    }
    const pkgsData = (await ctx.service.package.find(filterObj)).map(d =>
      d.toJSON()
    )
    for (const pkg of pkgsData) {
      await ctx.service.package.deleteOnePkg({
        name: pkg.name,
        version: pkg.version
      })
    }
    ctx.body = getResponseBody('成功删除' + pkgsData.length + '个组件包!')
  }
}

module.exports = PackagesController
