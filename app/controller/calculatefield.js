// 计算字段
const Controller = require('egg').Controller
const { getResponseBody, formatString } = require('../extend/utils')
const _ = require('lodash')
const dbconns = require('../helper/datasource')

class CalculateFieldsController extends Controller {
  async createField() {
    const { ctx } = this
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = ctx.header
    const config = Object.assign(queryParam, bodyParam, headerParam)
    const data = await ctx.service.calculatefield.createField(config)
    ctx.body = getResponseBody(data)
  }

  async getFieldList() {
    const { ctx } = this
    const body = ctx.request.body
    const data = await ctx.service.calculatefield.getFieldList(body)
    ctx.body = getResponseBody(data)
  }

  async deleteField() {
    const { ctx } = this
    const body = ctx.request.body
    const data = await ctx.service.calculatefield.deleteField(body)
    ctx.body = getResponseBody(data)
  }
}

module.exports = CalculateFieldsController
