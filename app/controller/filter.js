'use strict'

const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
const _ = require('lodash')
class FilterController extends Controller {
  async index() {
    const { ctx } = this
    const screenId = ctx.query.screenId
    const condition = {}
    if (screenId) {
      condition.screenId = screenId
    }
    const data = await ctx.service.filter.find(condition)
    ctx.body = getResponseBody(data)
  }

  async info() {
    const { ctx } = this
    const projection = {
      _id: 0
    }
    const data = await ctx.service.filter.findOne(
      { id: ctx.query.id },
      projection
    )
    ctx.body = getResponseBody(data)
  }

  async create() {
    const { ctx } = this
    const body = ctx.request.body
    if (typeof body.enable !== 'boolean') {
      ctx.body = getResponseBody(null, false, 'enable字段为boolean类型', 400)
      return
    }
    // 判断是否传入了过滤器id,如果有id则只添加组件与过滤器的关系,否则创建过滤器并添加组件和过滤器关系
    if (!body.id) {
      // 判断过滤器表里面是否存在相同名称的过滤器
      const defaultData = await ctx.service.filter.find({
        name: body.name,
        screenId: body.screenId
      })
      if (defaultData && defaultData.length) {
        ctx.body = getResponseBody(null, false, '该大屏已存在此过滤器名称', 400)
        return
      }
      const filters = _.omit(body, ['componentId', 'enable'])
      const componentId = body.componentId // 参数中的组件id
      const data = await ctx.service.filter.create(filters)
      const componentFilter = {
        id: data.id,
        name: data.name,
        enable: body.enable
      }
      // 根据component id 将过滤器关联到component表中。
      await ctx.service.component.update(
        { id: componentId },
        {
          $addToSet: { 'dataConfig.dataResponse.filters.list': componentFilter }
        }
      )
      ctx.body = getResponseBody(data)
      return
    }
    const componentId = body.componentId // 参数中的组件id
    const componentFilter = {
      id: body.id,
      name: body.name,
      enable: body.enable
    }
    const component = await ctx.service.component.update(
      { id: componentId },
      { $addToSet: { 'dataConfig.dataResponse.filters.list': componentFilter } }
    )
    ctx.body = getResponseBody(component)
  }

  // 创建过滤器
  async createFilter() {
    const { ctx } = this
    const body = ctx.request.body
    // 判断过滤器表里面是否存在相同名称的过滤器
    const defaultData = await ctx.service.filter.find({
      screenId: body.screenId,
      name: body.name
    })
    if (defaultData.length) {
      ctx.body = getResponseBody(null, false, '该大屏已存在此过滤器名称', 400)
      return
    }
    const data = await ctx.service.filter.create(body)
    ctx.body = getResponseBody(data)
  }

  // 删除组件与过滤器的关联关系，而非删除过滤器
  async delete() {
    const { ctx } = this
    const componentId = ctx.query.componentId
    const id = ctx.query.id
    const componentFilter = {
      id
    }
    const component = await ctx.service.component.update(
      { id: componentId },
      { $pull: { 'dataConfig.dataResponse.filters.list': componentFilter } }
    )
    ctx.body = getResponseBody(component)
  }

  // 删除过滤器
  async deleteFilter() {
    const { ctx } = this
    const id = ctx.query.id
    const screenId = ctx.query.screenId
    const componentFilter = {
      id
    }
    const componentList = await ctx.service.component.find({ screenId })
    for (let i = 0, len = componentList.length; i < len; i++) {
      const componentId = componentList[i].id
      await ctx.service.component.update(
        { id: componentId },
        { $pull: { 'dataConfig.dataResponse.filters.list': componentFilter } }
      )
    }
    const data = await ctx.service.filter.delete({ id })
    ctx.body = getResponseBody(data)
  }

  async update() {
    const { ctx } = this
    const body = ctx.request.body
    const defaultData = await ctx.service.filter.findOne({
      screenId: body.screenId,
      name: body.name
    })
    // 更新过滤器基本数据，判断是否重名
    if (defaultData && defaultData.id !== body.id) {
      ctx.body = getResponseBody(null, false, '该大屏已存在此过滤器名称', 400)
      return
    }
    if (body.componentId) {
      const enable = body.enable
      await ctx.service.component.update(
        {
          id: body.componentId,
          'dataConfig.dataResponse.filters.list.id': body.id
        },
        { $set: { 'dataConfig.dataResponse.filters.list.$.enable': enable } }
      )
    }
    // 同步修改组件中过滤器的开启状态
    const filters = _.omit(body, ['componentId', 'enable'])
    const data = await ctx.service.filter.update({ id: body.id }, filters)
    ctx.body = getResponseBody(data)
  }

  // 改变过滤器顺序
  async order() {
    const { ctx } = this
    const body = ctx.request.body
    const componentId = body.componentId
    // const originIndex = body.originIndex
    const targetIndex = body.targetIndex
    const id = body.id
    const enable = body.enable
    const name = body.name
    const componentFilter = {
      id,
      enable,
      name
    }
    // 删除原位置过滤器数据
    await ctx.service.component.update(
      { id: componentId },
      { $pull: { 'dataConfig.dataResponse.filters.list': componentFilter } }
    )
    // 向数组指定位置添加数据
    const componentData = await ctx.service.component.update(
      { id: componentId },
      {
        $push: {
          'dataConfig.dataResponse.filters.list': {
            $each: [componentFilter],
            $position: targetIndex
          }
        }
      }
    )
    ctx.body = getResponseBody(componentData)
  }
}

module.exports = FilterController
