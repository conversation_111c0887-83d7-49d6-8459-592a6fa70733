'use strict'
const Controller = require('egg').Controller
const { uuid, getResponseBody, getExpdate } = require('../extend/utils')
class seatomCliUserController extends Controller {
  async create() {
    const { ctx, config } = this
    const body = ctx.request.body
    const userName = body.username
    const passWord = body.password
    const role = body.role
    const userData = await ctx.service.seatomcliuser.find({ userName })
    if (userData && userData.length) {
      ctx.body = getResponseBody(null, false, '账号已存在', 400)
      return
    }
    const userId = uuid(userName)
    const token = ctx.app.jwt.sign({ userName, passWord }, config.jwt.secret, {
      expiresIn: '7 days'
    })
    const data = await ctx.service.seatomcliuser.create({
      userName,
      passWord,
      token: [token],
      role,
      userId
    })
    ctx.body = getResponseBody(data)
  }
  async login() {
    const { ctx, config } = this
    const body = ctx.request.body
    const userName = body.username
    const passWord = body.password
    const userData = await ctx.service.seatomcliuser.find({ userName })
    if (!userData || userData.length === 0) {
      ctx.body = getResponseBody(
        null,
        false,
        '此账号未注册，请先使用seatom register进行注册！',
        400
      )
      return
    } else if (userData[0].passWord !== passWord) {
      ctx.body = getResponseBody(null, false, '密码错误！', 400)
      return
    }
    const token = ctx.app.jwt.sign({ userName, passWord }, config.jwt.secret, {
      expiresIn: '7 days'
    })
    await ctx.service.seatomcliuser.update({ token: [token] })
    ctx.body = getResponseBody({
      userName: userData[0].userName,
      token
    })
  }
}
module.exports = seatomCliUserController
