'use strict'
const Controller = require('egg').Controller
const {
  getResponseBody,
  getResponseError,
  getLoggerModel
} = require('../extend/utils')
const { getLayerListAndIndexByLayerIds } = require('../utils/screen')

// 指定index添加元素
const addChildAtIndex = (originList = [], index, ...childs) => {
  const defaultGroup = originList.find(ol => ol.id === 'groups_default') // 默认分组
  if (defaultGroup) {
    defaultGroup.children.splice(index, 0, ...childs)
  } else {
    originList.splice(index, 0, ...childs)
    originList = [
      {
        id: 'groups_default',
        groupName: '默认分组',
        type: 'group',
        children: originList
      }
    ]
  }
  return originList
}

// 选中的列表从源列表删除
const removeBySelectedList = (originList = [], selectedList = []) => {
  selectedList.forEach(item => {
    const index = originList.findIndex(i => {
      return item.id === i.id
    })
    // originList[index].isDelete = true
    originList.splice(index, 1)
  })
  return originList
}

// 删除空分组
const removeEmptyGroup = (originList = []) => {
  for (let index = originList.length - 1; index >= 0; index--) {
    const item = originList[index]
    if (item.type === 'group' && item.children.length === 0) {
      originList.splice(index, 1)
    }
  }
}

/**
 * @Controller 大屏图层
 */
class ScreenLayerController extends Controller {
  /**
   * @Summary 获取大屏页面LayerTree列表
   * @Description 获取大屏页面LayerTree列表
   * @Router get /screenlayer/layers
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async screenLayers() {
    const { ctx } = this
    const screenId = ctx.query.screenId
    let originLayerTree = []
    // 过滤菜单
    // function filterMenu(tree, delMenuId) {
    //   var newArr = [];
    //   for (var i = 0; i < tree.length; i++) {
    //     var item = tree[i];
    //     if (item.id === delMenuId) {
    //       tree.splice(i--, 1);
    //     } else {
    //       if (item.children) {
    //         item.children = filterMenu(item.children, delMenuId);
    //       }
    //       newArr.push(item);
    //     }
    //   }
    //   return newArr;
    // }
    try {
      const res = await ctx.service.screen.findOne({ id: screenId })
      originLayerTree = (res && res.layerTree) || []
      ctx.body = getResponseBody(originLayerTree)
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screenlayer/screenLayers error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  /**
   * @Summary 通过layerIds删除大屏页面LayerTree
   * @Description 通过layerIds删除大屏页面LayerTree
   * @Router post /screenlayer/deleteLayerByIds
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async deleteLayerByIds() {
    const { ctx, app } = this
    const body = ctx.request.body || {}
    const screenId = body.screenId || ''
    const layerIds = body.layerIds || []

    try {
      // 参数校验
      const errors = app.validator.validate(
        {
          // 大屏ID
          screenId: {
            type: 'number',
            required: true
          },
          // 图层ID数组
          layerIds: {
            type: 'array',
            required: true
          }
        },
        body
      )
      // 参数校验
      if (errors && errors.length) {
        ctx.body = getResponseError({
          message: JSON.stringify(errors)
        })
        return
      }

      // 检查该用户的大屏编辑权限
      const { success, res } =
        await ctx.service.screen.checkScreenEditingPermissionByScreenId({
          screenId: screenId
        })

      if (!success) {
        ctx.body = getResponseError({
          ...res
        })
        return
      }

      const screenInfo = await ctx.service.screen.findOne({ id: screenId })
      const originLayerTree =
        (screenInfo &&
          screenInfo.layerTree &&
          screenInfo.layerTree.toObject()) ||
        []

      const { selectedList, currentList } = getLayerListAndIndexByLayerIds(
        originLayerTree,
        layerIds
      )

      // 从原来的列表删除
      removeBySelectedList(currentList, selectedList)
      console.log(
        originLayerTree,
        'selectedList',
        selectedList,
        'currentList',
        currentList
      )
      // 更新layerTree
      const data = await ctx.service.screen.update(
        { id: screenId },
        {
          layerTree: originLayerTree
        }
      )
      ctx.body = getResponseBody()
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      ctx.logger.info(
        getLoggerModel({
          message: '/screenlayer/deleteLayerByIds error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  /**
   * @Summary 通过layerIds移动大屏页面LayerTree
   * @Description 通过layerIds移动大屏页面LayerTree
   * @Router post /screenlayer/moveLayerByIds
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async moveLayerByIds() {
    const { ctx, app } = this
    const body = ctx.request.body || {}
    const screenId = body.screenId || ''
    const layerIds = body.layerIds || []
    const action = body.action

    // 数组元素互换位置
    const swapArray = (arr, index1, index2) => {
      arr[index1] = arr.splice(index2, 1, arr[index1])[0]
      return arr
    }

    // 图层置顶
    const bringToTop = originLayerTree => {
      const { selectedList, currentList } = getLayerListAndIndexByLayerIds(
        originLayerTree,
        layerIds
      )

      // 从原来的列表删除
      removeBySelectedList(currentList, selectedList)

      // 头部添加
      currentList.unshift(...selectedList)

      return originLayerTree
    }

    // 图层置底
    const bringToBottom = originLayerTree => {
      const { selectedList, currentList } = getLayerListAndIndexByLayerIds(
        originLayerTree,
        layerIds
      )

      // 从原来的列表删除
      removeBySelectedList(currentList, selectedList)

      // 底部添加
      currentList.push(...selectedList)

      return originLayerTree
    }

    // 图层上移一层
    const moveUp = originLayerTree => {
      const { selectedIndexs, currentList } = getLayerListAndIndexByLayerIds(
        originLayerTree,
        layerIds
      )
      selectedIndexs.forEach(index => {
        if (index > 0) {
          swapArray(currentList, index - 1, index)
        }
      })
      return originLayerTree
    }

    // 图层下移一层
    const moveDown = originLayerTree => {
      const { selectedIndexs, currentList } = getLayerListAndIndexByLayerIds(
        originLayerTree,
        layerIds
      )

      selectedIndexs.forEach(index => {
        if (index + 1 < currentList.length) {
          swapArray(currentList, index + 1, index)
        }
      })
      return originLayerTree
    }

    const actionFns = {
      bringToTop: bringToTop,
      bringToBottom: bringToBottom,
      moveUp: moveUp,
      moveDown: moveDown
    }

    try {
      // 参数校验
      const errors = app.validator.validate(
        {
          // 大屏ID
          screenId: {
            type: 'number',
            required: true
          },
          // 图层ID数组
          layerIds: {
            type: 'array',
            required: true
          },
          // 动作
          action: {
            type: 'string',
            required: true
          }
        },
        body
      )
      // 参数校验
      if (errors && errors.length) {
        ctx.body = getResponseError({
          message: JSON.stringify(errors)
        })
        return
      }

      if (!actionFns[action]) {
        ctx.body = getResponseError({
          message: `找不到 action:${action} 方法`
        })
        return
      }

      const screenInfo = await ctx.service.screen.findOne({ id: screenId })
      const originLayerTree =
        (screenInfo &&
          screenInfo.layerTree &&
          screenInfo.layerTree.toObject()) ||
        []

      // 获取排序后的layerTree
      const newLayerTree =
        actionFns[action] && actionFns[action](originLayerTree)

      // 更新layerTree
      const data = await ctx.service.screen.update(
        { id: screenId },
        {
          layerTree: newLayerTree
        }
      )

      ctx.body = getResponseBody()
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      ctx.logger.info(
        getLoggerModel({
          message: '/screenlayer/moveLayerByIds error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  /**
   * @Summary 处理拖动插入大屏LayerTree
   * @Description 处理拖动插入大屏LayerTree
   * @Router post /screenlayer/dragInsertLayer
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async dragInsertLayer() {
    const { ctx, app } = this
    const body = ctx.request.body || {}
    const screenId = body.screenId || ''
    const insertArr = body.insertArr

    try {
      // 参数校验
      const errors = app.validator.validate(
        {
          // 大屏ID
          screenId: {
            type: 'number',
            required: true
          },
          // 插入数组
          insertArr: {
            type: 'array',
            required: true
          }
        },
        body
      )
      // 参数校验
      if (errors && errors.length) {
        ctx.body = getResponseError({
          message: JSON.stringify(errors)
        })
        return
      }

      const screenInfo = await ctx.service.screen.findOne({ id: screenId })
      const originLayerTree =
        (screenInfo &&
          screenInfo.layerTree &&
          screenInfo.layerTree.toObject()) ||
        []

      insertArr.forEach((insertItem, index) => {
        // 两个节点相同则不处理
        if (insertItem.nodeData.id === insertItem.anchorNodeData.id) {
          return
        }

        // 获取节点数据
        const nodeDataRes = getLayerListAndIndexByLayerIds(originLayerTree, [
          insertItem.nodeData.id
        ])

        // 获取锚点的数据
        const anchorNodeDataRes = getLayerListAndIndexByLayerIds(
          originLayerTree,
          [insertItem.anchorNodeData.id]
        )

        // 处理节点被另外一个人删除找不到的情况
        if (nodeDataRes.selectedList.length === 0) {
          return
        }

        // 处理锚点节点被另外一个人删除找不到的情况
        if (anchorNodeDataRes.selectedList.length === 0) {
          return
        }

        // 从原来的列表删除
        removeBySelectedList(nodeDataRes.currentList, nodeDataRes.selectedList)

        // 重新找到锚点节点的index
        const anchorNodeIndex = anchorNodeDataRes.currentList.findIndex(
          item => {
            return item.id === insertItem.anchorNodeData.id
          }
        )
        const actionFns = {
          // 添加到锚点节点之前
          insertBefore: () => {
            // 指定index添加元素
            addChildAtIndex(
              anchorNodeDataRes.currentList,
              anchorNodeIndex,
              insertItem.nodeData
            )
          },
          // 添加到锚点节点之后
          insertAfter: () => {
            // 指定index添加元素
            addChildAtIndex(
              anchorNodeDataRes.currentList,
              anchorNodeIndex + 1,
              insertItem.nodeData
            )
          }
        }
        // 执行插入
        actionFns[insertItem.action] && actionFns[insertItem.action]()

        // 删除空组
        if (nodeDataRes.currentList.length === 0) {
          removeEmptyGroup(nodeDataRes.currentParentList || [])
        }
      })

      // 更新layerTree
      const data = await ctx.service.screen.update(
        { id: screenId },
        {
          layerTree: originLayerTree
        }
      )
      ctx.body = getResponseBody()
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      ctx.logger.info(
        getLoggerModel({
          message: '/screenlayer/dragInsertLayer error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  /**
   * @Summary 处理插入大屏LayerTree
   * @Description 处理插入大屏LayerTree
   * @Router post /screenlayer/insertLayers
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async insertLayers() {
    const { ctx, app } = this
    const body = ctx.request.body || {}
    const screenId = body.screenId || ''
    const layers = body.layers
    const insertIndex = body.insertIndex || 0

    try {
      // 参数校验
      const errors = app.validator.validate(
        {
          // 大屏ID
          screenId: {
            type: 'number',
            required: true
          },
          // layers数组
          layers: {
            type: 'array',
            required: true
          },
          // 插入位置
          insertIndex: {
            type: 'number',
            required: false,
            min: 0
          }
        },
        body
      )

      // 参数校验
      if (errors && errors.length) {
        ctx.body = getResponseError({
          message: JSON.stringify(errors)
        })
        return
      }

      const screenInfo = await ctx.service.screen.findOne({ id: screenId })
      const originLayerTree =
        (screenInfo &&
          screenInfo.layerTree &&
          screenInfo.layerTree.toObject()) ||
        []

      // 指定index添加元素
      addChildAtIndex(originLayerTree, insertIndex, ...layers)

      // 更新layerTree
      const data = await ctx.service.screen.update(
        { id: screenId },
        {
          layerTree: originLayerTree
        }
      )
      ctx.body = getResponseBody()
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      ctx.logger.info(
        getLoggerModel({
          message: '/screenlayer/insertLayers error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  /**
   * @Summary 更新大屏选中LayerTree
   * @Description 更新大屏选中LayerTree
   * @Router post /screenlayer/updateSelectedLayers
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async updateSelectedLayers() {
    const { ctx, app } = this
    const body = ctx.request.body || {}
    const screenId = body.screenId || ''
    const layers = body.layers
    const onlyUpdateKeys = body.onlyUpdateKeys || []

    try {
      // 参数校验
      const errors = app.validator.validate(
        {
          // 大屏ID
          screenId: {
            type: 'number',
            required: true
          },
          // layers数组
          layers: {
            type: 'array',
            required: true
          },
          // 只更新一些keys
          onlyUpdateKeys: {
            type: 'array',
            required: false
          }
        },
        body
      )

      // 参数校验
      if (errors && errors.length) {
        ctx.body = getResponseError({
          message: JSON.stringify(errors)
        })
        return
      }

      const screenInfo = await ctx.service.screen.findOne({ id: screenId })
      const originLayerTree =
        (screenInfo &&
          screenInfo.layerTree &&
          screenInfo.layerTree.toObject()) ||
        []

      layers.forEach(layer => {
        const { selectedIndexs, currentList } = getLayerListAndIndexByLayerIds(
          originLayerTree,
          [layer.id]
        )

        // 处理找不到的情况
        if (!selectedIndexs.length) {
          return
        }

        // 只更新一些值
        if (onlyUpdateKeys && onlyUpdateKeys.length) {
          for (const key of onlyUpdateKeys) {
            currentList[selectedIndexs[0]][key] = layer[key]
          }
        } else {
          // 全量更新
          currentList[selectedIndexs[0]] = layer
        }
      })

      // 更新layerTree
      const data = await ctx.service.screen.update(
        { id: screenId },
        {
          layerTree: originLayerTree
        }
      )
      ctx.body = getResponseBody()
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      ctx.logger.info(
        getLoggerModel({
          message: '/screenlayer/updateSelectedLayers error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  /**
   * @Summary 创建图层分组
   * @Description 创建图层分组
   * @Router post /screenlayer/createLayerGroups
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async createLayerGroups() {
    const { ctx, app } = this
    const body = ctx.request.body || {}
    const screenId = body.screenId || ''
    const groups = body.groups || []

    try {
      // 参数校验
      const errors = app.validator.validate(
        {
          // 大屏ID
          screenId: {
            type: 'number',
            required: true
          },
          groups: {
            type: 'array',
            required: true
          }
        },
        body
      )

      // 参数校验
      if (errors && errors.length) {
        ctx.body = getResponseError({
          message: JSON.stringify(errors)
        })
        return
      }

      const screenInfo = await ctx.service.screen.findOne({ id: screenId })
      const originLayerTree =
        (screenInfo &&
          screenInfo.layerTree &&
          screenInfo.layerTree.toObject()) ||
        []

      groups.forEach(group => {
        const layerIds = group.children.map(item => {
          return item.id
        })

        const { selectedIndexs, selectedList, currentList } =
          getLayerListAndIndexByLayerIds(originLayerTree, layerIds)

        // 考虑分组下的元素被删除的情况
        group.children = selectedList

        // 指定index添加元素
        addChildAtIndex(currentList, selectedIndexs[0], group)

        // 删除旧的节点
        removeBySelectedList(currentList, selectedList)
      })

      // 更新layerTree
      const data = await ctx.service.screen.update(
        { id: screenId },
        {
          layerTree: originLayerTree
        }
      )
      ctx.body = getResponseBody()
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      ctx.logger.info(
        getLoggerModel({
          message: '/screenlayer/updateSelectedLayers error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  /**
   * @Summary 取消图层分组
   * @Description 取消图层分组
   * @Router post /screenlayer/cancelLayerGroups
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async cancelLayerGroups() {
    const { ctx, app } = this
    const body = ctx.request.body || {}
    const screenId = body.screenId || ''
    const layerGroupIds = body.layerGroupIds || []

    try {
      // 参数校验
      const errors = app.validator.validate(
        {
          // 大屏ID
          screenId: {
            type: 'number',
            required: true
          },
          layerGroupIds: {
            type: 'array',
            required: true
          }
        },
        body
      )

      // 参数校验
      if (errors && errors.length) {
        ctx.body = getResponseError({
          message: JSON.stringify(errors)
        })
        return
      }

      const screenInfo = await ctx.service.screen.findOne({ id: screenId })
      const originLayerTree =
        (screenInfo &&
          screenInfo.layerTree &&
          screenInfo.layerTree.toObject()) ||
        []

      layerGroupIds.forEach(layerId => {
        const { selectedIndexs, selectedList, currentList } =
          getLayerListAndIndexByLayerIds(originLayerTree, [layerId])

        // 处理找不到的情况
        if (!selectedList.length) {
          return
        }

        // 指定index添加元素
        addChildAtIndex(
          currentList,
          selectedIndexs[0],
          ...selectedList[0].children
        )

        // 删除分组
        removeBySelectedList(currentList, selectedList)
      })

      // 更新layerTree
      const data = await ctx.service.screen.update(
        { id: screenId },
        {
          layerTree: originLayerTree
        }
      )
      ctx.body = getResponseBody()
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      ctx.logger.info(
        getLoggerModel({
          message: '/screenlayer/updateSelectedLayers error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }
}

module.exports = ScreenLayerController
