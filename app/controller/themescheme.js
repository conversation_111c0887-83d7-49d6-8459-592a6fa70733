'use strict'

const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
class ThemeschemeController extends Controller {
  async index() {
    const { ctx } = this
    const data = await ctx.service.themescheme.find()
    ctx.body = getResponseBody(data)
  }

  async create() {
    const { ctx } = this
    const body = ctx.request.body
    const data = await ctx.service.themescheme.create(body)
    ctx.body = getResponseBody(data)
  }

  async getComtheme() {
    const { ctx } = this
    const body = ctx.request.body
    const screenId = body.screenId
    const themeSchemeId = body.themeSchemeId
    // const condition = {}
    let coms = await ctx.service.component.find({ screenId })
    let data = {}
    for (let index = 0; index < coms.length; index++) {
      const comconfig = await ctx.service.comtheme.find(
        { comType: coms[index].comType, themeSchemeId },
        null,
        { sort: { createdAt: -1 } }
      )
      if (comconfig && comconfig.length) {
        data[coms[index].id] = {
          config: comconfig[0].config,
          other: {
            themeUrl: comconfig[0].icon
          }
        }
      }
    }
    ctx.body = getResponseBody(data)
  }

  async update() {
    const { ctx } = this
    const id = ctx.query.id
    const body = ctx.request.body
    const data = await ctx.service.themescheme.update({ id: id }, body)
    ctx.body = getResponseBody(data)
  }

  async delete() {
    const { ctx } = this
    const id = ctx.request.body.id
    await ctx.service.comtheme.update(
      { themeSchemeId: id },
      { themeSchemeId: '' },
      { multi: true }
    )
    const data = await ctx.service.themescheme.delete({ id })
    ctx.body = getResponseBody(data)
  }
}

module.exports = ThemeschemeController
