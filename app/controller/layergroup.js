'use strict'
const _ = require('lodash')
const fs = require('fs-extra')
const path = require('path')
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')

class LayergroupController extends Controller {
  // 查询图层信息
  async index() {
    const { ctx } = this
    const body = ctx.request.body || {}
    const data = await ctx.service.layergroup.find(body)
    ctx.body = getResponseBody(data)
  }
  // 创建图层信息
  async create() {
    const { ctx } = this
    const body = ctx.request.body
    const data = await ctx.service.layergroup.create(body)
    ctx.body = getResponseBody(data)
  }
  // 更新图层信息
  async update() {
    const { ctx } = this
    const id = ctx.query.id
    const body = ctx.request.body
    const data = await ctx.service.layergroup.update({ id }, body)
    ctx.body = getResponseBody(data)
  }
  // 删除图层信息
  async delete() {
    const { ctx } = this
    const { id } = ctx.request.body
    const data = await ctx.service.layergroup.delete({ id })
    ctx.body = getResponseBody(data)
  }
  getTree(data) {
    let newTree = []
    for (let index = 0; index < data.length; index++) {
      const element = data[index]
      if (element.parentId === '') {
      }
    }
  }
}

module.exports = LayergroupController
