'use strict'
// 发布页面
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
const { cipher, decipher } = require('../extend/crypto')
const { EXPORT_COMTYPE } = require('../extend/constant')
const { getPrevComProjection } = require('../utils/preview')
const md5 = require('md5')

class PreviewController extends Controller {
  async map() {
    const { ctx, config } = this
    // 获取缓存
    let value = await ctx.service.package.getCachePackageMap()

    if (value) {
      if (typeof value === 'string') {
        value = JSON.parse(value)
      }
      ctx.body = getResponseBody(value)
      return
    }
    const res = await ctx.service.package.getPkgVersionNames()

    // 缓存
    ctx.service.package.cachePackageMap(res)

    ctx.body = getResponseBody(res)
  }
  /**
   * 发布页获取面板组件
   * @returns
   */
  async getScreensComs() {
    const { ctx } = this
    const body = ctx.request.body
    const screenIds = body.screenIds
    const key = md5(JSON.stringify(screenIds))
    const updateCatch = ctx.request.header['update-cache'] || false
    let value = await ctx.service.cacheservice.get(key)

    if (value && typeof value === 'string') {
      value = JSON.parse(value)
    }

    // 走缓存的条件
    if (value && !updateCatch) {
      ctx.body = getResponseBody(value)
      return
    }

    this.ctx.logger.info(
      '不走缓存',
      'fn:preview.getScreensComs',
      `key: ${key}`,
      `screenIds:${screenIds}`
    )
    let screensComsList = []

    const projection = {
      ...getPrevComProjection(),
      staticData: 0,
      dataConfig: 0
    }

    let promiseList = []
    if (screenIds && screenIds.length > 0) {
      const screensComs = await ctx.service.screen.find({
        id: { $in: screenIds }
      })
      for (let index = 0; index < screensComs.length; index++) {
        promiseList.push(
          new Promise(async (res, rej) => {
            try {
              const screensComselement = screensComs[index]
              const comData = await ctx.service.component.find(
                { screenId: screensComselement.id },
                projection
              )

              res({
                id: screensComselement.id,
                screenType: screensComselement.screenType,
                comList: comData.map(com => {
                  return {
                    id: com.id,
                    comName: com.comName,
                    interactionConfig: com.interactionConfig
                  }
                })
                // layerTree: screensComs[index].layerTree
              })
            } catch (error) {
              res(error)
            }
          })
        )
      }
      screensComsList = await Promise.all(promiseList)
      await ctx.service.cacheservice.set(key, screensComsList, 15768000)
      ctx.body = getResponseBody(screensComsList)
    } else {
      ctx.body = getResponseBody(null, false, '请传入伏羲大屏id', 400)
    }
  }
  /**
   * 发布页获取主面板数据
   * @returns
   */
  async screen() {
    console.log('========',);
    const { ctx } = this
    const { id, shareToken } = ctx.query

    const res = await ctx.service.preview.getPreviewScreenOrCache({
      screenId: id,
      shareToken
    })
    const tokenStart = new Date().getTime()
    // 从缓存获取大屏ID对应的面板列表
    if (res.success && res.data && !res.data.isDynamicScreen) {
      const panelScreens = await ctx.service.cacheservice.get(
        `${res.data.id}_preview_panel_screens`
      )
      ctx.logger.info('panelScreens===', panelScreens)
      res.data.panelScreens = panelScreens
    }
    const tokenEnd = new Date().getTime()
    this.ctx.logger.info('请求screen的时间', `${tokenEnd - tokenStart}hzms`)
    ctx.body = res
  }

  async getData() {
    const { ctx } = this
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = {
      systoken: ctx.header.systoken,
      sysuserid: ctx.header.sysuserid
    }
    const key = JSON.stringify(Object.assign(bodyParam, queryParam))
    const updateCatch = ctx.request.header['update-cache'] || false
    let value = await ctx.service.cacheservice.get(key)
    if (value && !updateCatch) {
      ctx.body = getResponseBody(value)
      return
    }
    // 合并接口对特殊处理
    if (Object.keys(bodyParam)[0] == 0) {
      let resData = []
      bodyParam.forEach(value => {
        resData.push(
          new Promise(async (resolve, reject) => {
            try {
              const config = Object.assign(value, queryParam, headerParam)
              const res = await ctx.service.datastorage.getData(config)
              resolve(res)
            } catch (e) {
              resolve(e)
            }
          })
        )
      })
      const ret = await Promise.all(resData)
      const returnValue = ret.map(item => {
        return getResponseBody(item)
      })
      ctx.body = returnValue
      await ctx.service.cacheservice.set(key, returnValue)
    } else {
      const config = Object.assign(queryParam, bodyParam, headerParam)
      const res = await ctx.service.datastorage.getData(config)
      ctx.body = getResponseBody(res)
      await ctx.service.cacheservice.set(key, res)
    }
  }

  /**
   * @Summary 清除发布页缓存
   * @Description 清除发布页缓存
   * @Router get /preview/clearCache
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async clearCache() {
    const { ctx, config } = this
    const screenId = ctx.query.screenId

    ctx.body = await ctx.service.preview.clearCache({ screenId })
  }

  /**
   * @Summary 发布更新，更新发布页缓存
   * @Description 发布更新，更新发布页缓存
   * @Router get /preview/updateCache
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async updateCache() {
    const { ctx, config } = this
    const screenId = ctx.query.screenId
    try {
      const screenshareInfo = await ctx.service.screenshare.findOne({
        screenId
      })
      const comInfo = await ctx.service.component.find({
        screenId,
        comType: { $in: EXPORT_COMTYPE.COPYCOMTYPE }
      })
      // 记录面板id，需要id对应一个value缓存的
      const cacheSingleScreenIds = []
      // 记录面板id，需要放在一个列表一起缓存的
      const cachePanelScreenListIds = []

      if (comInfo) {
        // 需要放在一个列表缓存的面板列表类型
        const cachePanelScreenListTypes = [
          'interaction-container-dynamicpanel',
          'interaction-container-newdynamicpanel',
          'interaction-container-loop-pitch',
          'interaction-container-roll-pitch',
          'interaction-container-list-pitch',
          'interaction-container-affixPanel',
          'interaction-container-fold-panel'
        ]

        // 获取面板大屏id
        for (let index = 0; index < comInfo.length; index++) {
          const com = comInfo[index]
          if (com.config.screens && com.config.screens.length) {
            const ids = com.config.screens.map(item => item.id)

            // 记录面板id，需要放在一个列表一起缓存的
            if (cachePanelScreenListTypes.includes(com.comType)) {
              cachePanelScreenListIds.push(...ids)
            } else {
              // 记录面板id，需要id对应一个value缓存的
              cacheSingleScreenIds.push(...ids)
            }
          }
        }
      }

      // 清除缓存
      // 是否有缓存当前大屏ID，可能被引用面板引用了
      const hasCacheCurrScreenId = await this.app.redis
        .get('db0')
        .get(`preview_screen_${screenshareInfo.screenId}`)
      const pipeline = this.app.redis.get('db0').pipeline()

      const deleteByScreenIds = screenIds => {
        for (let k = 0; k < screenIds.length; k++) {
          const screenId = screenIds[k]
          pipeline.del(`preview_screen_${screenId}`)
        }
      }

      deleteByScreenIds(cacheSingleScreenIds)
      deleteByScreenIds(cachePanelScreenListIds)

      pipeline.del(`preview_screen_${screenshareInfo.shareToken}`)
      if (hasCacheCurrScreenId) {
        pipeline.del(`preview_screen_${screenshareInfo.screenId}`)
      }
      await pipeline.exec()

      // 重新写入缓存
      const promiseList = []
      for (let k = 0; k < cacheSingleScreenIds.length; k++) {
        const screenId = cacheSingleScreenIds[k]
        promiseList.push(
          ctx.service.preview.getPreviewScreenOrCache({
            screenId
          })
        )
      }
      // 重新写入缓存
      promiseList.push(
        ctx.service.preview.getPreviewScreenOrCache({
          shareToken: screenshareInfo.shareToken
        })
      )
      // 有缓存当前大屏ID也要重新缓存
      if (hasCacheCurrScreenId) {
        promiseList.push(
          ctx.service.preview.getPreviewScreenOrCache({
            screenId: screenshareInfo.screenId
          })
        )
      }

      // 缓存面板大屏列表
      const cachePanelScreenList = async () => {
        // 获取面板大屏列表
        const panelScreenList = await ctx.service.preview.getCombinationScreens(
          cachePanelScreenListIds
        )
        // 缓存大屏ID对应的面板列表
        await ctx.service.cacheservice.set(
          `${screenId}_preview_panel_screens`,
          panelScreenList,
          15768000
        )
      }

      promiseList.push(cachePanelScreenList())

      await Promise.all(promiseList)

      ctx.body = getResponseBody('', true, '缓存更新成功', 200)
    } catch (error) {
      ctx.body = getResponseBody(error, false, '缓存更新失败', 400)
      this.ctx.logger.error('缓存更新失败', error)
    }
  }
}

module.exports = PreviewController
