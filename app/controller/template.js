'use strict'

const path = require('path')
const fs = require('fs-extra')
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
class TemplatesController extends Controller {
  async index() {
    const { ctx } = this
    const data = await ctx.service.template.find()
    ctx.body = getResponseBody(data)
  }

  async create() {
    const { ctx, config } = this
    if (!ctx.request.files || !ctx.request.files.length) {
      ctx.body = getResponseBody(null, false, '无法创建，上传文件为空！', 400)
      return
    }
    const file = ctx.request.files[0]
    const { name, cn_name, version } = ctx.request.body
    const findData = await ctx.service.template.find({ name, version })
    if (findData && findData.length) {
      ctx.body = getResponseBody(null, false, '无法创建，模板已存在！', 400)
      return
    }
    const fileName = `${name}-${version}.tar.gz`
    const uploadDir = path.resolve(
      config.resourcePath,
      './templates/' + fileName
    )
    fs.moveSync(file.filepath, uploadDir)
    const res = await ctx.service.template.create({ name, cn_name, version })
    ctx.body = getResponseBody(res)
  }

  async delete() {
    const { ctx, config } = this
    const { name, version } = ctx.query
    const fileName = `${name}-${version}.tar.gz`
    const tplDir = path.resolve(config.resourcePath, './templates/' + fileName)
    fs.removeSync(tplDir)
    const res = await ctx.service.template.delete({ name, version })
    ctx.body = getResponseBody(res)
  }

  async file() {
    const { ctx, service, config } = this
    const name = ctx.params.name
    const version = ctx.params.version
    const fileName = `${name}-${version}.tar.gz`
    const filePath = path.join(config.resourcePath, './templates/', fileName)

    const data = fs.readFileSync(filePath)

    ctx.set({
      'Content-Type': 'application/octet-stream', //告诉浏览器这是一个二进制文件
      'Content-Disposition': `attachment;filename=${fileName}` //告诉浏览器这是一个需要下载的文件
    })
    ctx.body = data
    ctx.status = 200
  }
}

module.exports = TemplatesController
