'use strict'
// 对接平台的指标卡
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
const { encryptPath } = require('../extend/crypto')
const _ = require('lodash')
class IndicatorDmcController extends Controller {
  async com() {
    const { ctx } = this
    const body = ctx.query ? ctx.query : {}
    const indicatorCard = await ctx.service.indicatordmc.findOne({
      componentId: body.id
    })
    let data = await ctx.service.component.findOne(body)
    if (indicatorCard) {
      data.alias = indicatorCard.name
    }
    ctx.body = getResponseBody(data)
  }
  async myIndicatorList() {
    const { ctx } = this
    const headerParam = {
      systoken: ctx.header.systoken,
      sysuserid: ctx.header.sysuserid
    }
    const indicatorIp = this.config.indicatorIp
    const result = await ctx.curl(`${indicatorIp}/api/dmc/myIndicatorList`, {
      method: 'GET',
      dataType: 'json',
      data: {
        userId: headerParam.sysuserid
      },
      rejectUnauthorized: false
    })
    if (result.data.status == 0) {
      for (let index = 0; index < result.data.data.length; index++) {
        const element = result.data.data[index]
        const indicatordmcList = await ctx.service.indicatordmc.find({
          id: { $in: element.chartCodeList }
        })
        result.data.data[index].indicatordmcList = indicatordmcList
      }
      ctx.body = getResponseBody(result.data.data)
    } else {
      ctx.body = getResponseBody(result, false, '接口错误', 400)
    }
  }
  async publicIndicatorList() {
    const { ctx } = this
    const headerParam = {
      systoken: ctx.header.systoken,
      sysuserid: ctx.header.sysuserid
    }
    const indicatorIp = this.config.indicatorIp
    const result = await ctx.curl(
      `${indicatorIp}/api/dmc/publicIndicatorList`,
      {
        method: 'GET',
        dataType: 'json',
        data: {
          userId: headerParam.sysuserid
        },
        rejectUnauthorized: false
      }
    )
    if (result.data.status == 0) {
      for (let index = 0; index < result.data.data.length; index++) {
        const element = result.data.data[index]
        const indicatordmcList = await ctx.service.indicatordmc.find({
          id: { $in: element.chartCodeList }
        })
        result.data.data[index].indicatordmcList = indicatordmcList
      }
      ctx.body = getResponseBody(result.data.data)
    } else {
      ctx.body = getResponseBody(result, false, '接口错误', 400)
    }
  }
  async getIndicatorDmc() {
    const { ctx } = this
    const indicatorIp = this.config.indicatorIp
    const result = await ctx.curl(
      `${indicatorIp}/api/dmc/queryIndicatorDetail`,
      {
        method: 'GET',
        dataType: 'json',
        data: {
          indicatorId: '2505327878216877053'
        },
        rejectUnauthorized: false
      }
    )
    if (result.data.status == 0) {
      ctx.body = getResponseBody(result.data.data)
    } else {
      ctx.body = getResponseBody(result, false, '接口错误', 400)
    }
  }

  async save() {
    const { ctx } = this
    const body = ctx.request.body ? ctx.request.body : {}
    const indicatorIp = this.config.indicatorIp
    const result = await ctx.curl(`${indicatorIp}/api/dmc/saveChart`, {
      method: 'POST',
      dataType: 'json',
      contentType: 'json',
      data: body,
      rejectUnauthorized: false
    })
    if (result.data.status == 0) {
      ctx.body = getResponseBody(result.data.data)
    } else {
      ctx.body = getResponseBody(result, false, '接口错误', 400)
    }
  }

  async index() {
    const { ctx } = this
    const body = ctx.query ? ctx.query : {}
    const screenInfo = await ctx.service.screen.findOne({ id: 1 })
    if (!screenInfo) {
      const params = {
        id: 1,
        projectId: 0,
        workspaceId: 0,
        name: '指标卡模版大屏',
        level: 0,
        type: 'pc',
        templateId: 1,
        screenType: 'common',
        isScreentpl: true
      }
      await ctx.service.screen.create(params)
    }
    const indicatorCard = await ctx.service.indicatordmc.find(body)
    ctx.body = getResponseBody(indicatorCard)
  }

  async create() {
    const { ctx } = this
    const body = ctx.request.body
    const workspaceId = ctx.query.workspaceId
    const indicatorDmcId = body.indicatorDmcId
    const indicatorIp = this.config.indicatorIp
    let indicatorDetail
    const result = await ctx.curl(
      `${indicatorIp}/api/dmc/queryIndicatorDetail`,
      {
        method: 'GET',
        dataType: 'json',
        data: {
          indicatorId: indicatorDmcId || '2505327878216877053'
        },
        rejectUnauthorized: false
      }
    )
    if (result && result.data && result.data.status == 0) {
      indicatorDetail = result.data.data
    } else {
      ctx.body = getResponseBody(null, false, result.message || '接口访问出错', 400)
    }
    if (indicatorDetail.belongUserId != body.userId) {
      ctx.body = getResponseBody(
        { userId: body.userId, belongUserId: indicatorDetail.belongUserId },
        false,
        '用户id不匹配,请重新登录',
        401
      )
      return
    }
    const indicatorCard = await ctx.service.indicatordmc.findOne({
      name: body.name
    })
    if (indicatorCard) {
      ctx.body = getResponseBody(null, false, '名称已存在', 400)
      return
    }
    if (body.componentId) {
      body.indicatorName = indicatorDetail.indicatorName
      try {
        const data = await ctx.service.indicatordmc.create(body)
        const projectData = await ctx.service.project.findOne({ type: 0 })
        const params = {
          name: '子组件编辑指标卡',
          projectId: projectData.id,
          workspaceId: workspaceId,
          level: 0,
          type: 'pc',
          templateId: 1,
          config: {
            height: 400,
            width: 500,
            backgroundImage: ''
          },
          'config.scaleType': 'no_scale',
          isDynamicScreen: true,
          relationCompId: body.componentId,
          parentId: 1,
          screenType: 'common'
        }
        const screenInfo = await ctx.service.screen.create(params)
        let datastoragedata
        const defaultData = await ctx.service.datastorage.findOne({
          workspaceId: workspaceId,
          name: '指标平台默认api'
        })
        if (defaultData) {
          datastoragedata = defaultData
        } else {
          const datastorageparams = {
            workspaceId: workspaceId,
            type: 'api',
            name: '指标平台默认api',
            description: '指标平台默认api',
            config: {
              baseUrl: `${indicatorIp}/api/dmc/queryData`
            }
          }
          datastoragedata = await ctx.service.datastorage.create(
            datastorageparams
          )
        }
        await ctx.service.component.update(
          { id: body.componentId },
          {
            'config.screens': [{ id: screenInfo.id }],
            'dataConfig.dataResponse.sourceType': 'api',
            'dataConfig.dataResponse.source.api.data': {
              sourceId: datastoragedata.id,
              baseUrl: `${indicatorIp}/api/dmc/queryData`,
              method: 'POST',
              headers: '{}',
              path: '',
              params: '',
              body: JSON.stringify({
                indicatorId: indicatorDmcId,
                userId: body.userId
              }),
              reqFromBack: false,
              needCookie: false,
              isLimit: false,
              limitNum: 150,
              func: '// 可在此处生成自定义的动态js参数\n// 此处可使用callbackArgs.xxx引用回调参数\n// 必须返回对象形式数据，即 return {...}\n// 动态参数可在header/body/参数里通过 "${_var.xxx}" 引用\n\nreturn {}'
            }
          }
        )
        ctx.body = getResponseBody(data)
      } catch (error) {
        ctx.body = getResponseBody(error, false, '参数错误', 400)
      }
    } else {
      ctx.body = getResponseBody(null, false, '参数错误', 400)
    }
  }

  async delete() {
    const { ctx } = this
    const id = ctx.request.body.id
    const indicatorDmcId = ctx.request.body.indicatorDmcId
    const componentId = ctx.request.body.componentId
    const headerParam = {
      systoken: ctx.header.systoken,
      sysuserid: ctx.header.sysuserid
    }
    const indicatorIp = this.config.indicatorIp
    const result = await ctx.curl(`${indicatorIp}/api/dmc/deleteChart`, {
      method: 'GET',
      dataType: 'json',
      data: {
        userId: headerParam.sysuserid,
        indicatorId: indicatorDmcId,
        chartCode: id
      },
      rejectUnauthorized: false
    })
    await ctx.service.indicatordmc.delete({ id: { $in: [id] } })
    await ctx.service.component.deleteMany({
      screenId: 1,
      id: { $in: [componentId] }
    })
    ctx.body = getResponseBody(result)
  }

  async update() {
    const { ctx } = this
    const body = ctx.request.body
    if (body.name) {
      const indicatorCard = await ctx.service.indicatordmc.findOne({
        name: body.name
      })
      if (indicatorCard) {
        ctx.body = getResponseBody(null, false, '名称已存在', 400)
        return
      }
    }
    const data = await ctx.service.indicatordmc.update(
      { id: ctx.query.id },
      body
    )
    ctx.body = getResponseBody(data)
  }
}

module.exports = IndicatorDmcController
