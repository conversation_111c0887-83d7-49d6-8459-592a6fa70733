'use strict'
const _ = require('lodash')
const {
  uuid,
  getResponseBody,
  getResponseError,
  compareVersion,
  addConfig,
  randomStr,
  getLoggerModel
} = require('../extend/utils')
const { EXPORT_COMTYPE } = require('../extend/constant')
const parseConfig = require('../extend/parse-config')
const Controller = require('egg').Controller
const fs = require('fs')
const path = require('path')
const { exec } = require('child_process')
const compressing = require('compressing')
const os = require('os')

class ComponentsController extends Controller {
  async index() {
    const { ctx } = this
    const data = await ctx.service.component.find()
    ctx.body = getResponseBody(data)
  }

  // 新建组件时，获取公共配置字段
  getComCommonConfig(define, type = 'com') {
    const cfg = JSON.parse(define.config)
    return {
      id: uuid(define.name),
      type,
      comName: define.name,
      comType: define.type,
      alias: define.alias,
      orginName: define.alias,
      version: define.version,
      icon: define.icon,
      packageCreateDate: define.createdAt,
      show: cfg.show,
      needCommon: cfg.needCommon,
      config: parseConfig(cfg.config),
      controlConfig: JSON.stringify(cfg.config),
      dataConfig: {
        fields: cfg.data.fields,
        fieldMapping: cfg.data.fields.map(f => {
          return {
            source: f.name,
            target: f.name,
            description: f.description,
            type: f.type,
            status: 0
          }
        })
      },
      interactionConfig: {},
      staticData: cfg.data.source,
      requirePath: define.path,
      events: cfg.events,
      actions: cfg.actions,
      callbacks: cfg.callbacks,
      ordinary: cfg.ordinary || [],
      other: {
        themeUrl: cfg.icon
      }
    }
  }

  async create() {
    const { ctx } = this
    const { screenId } = ctx.query
    const com = ctx.request.body
    if (!com.version) {
      const datapak = await ctx.service.package.find(
        {
          name: com.name
        },
        {
          createdAt: 0,
          config: 0
        }
      )
      datapak.sort((a, b) => {
        const res = compareVersion(a.version, b.version)
        return -res
      })
      com.version = datapak[0].version
    }
    const screenInfo = await ctx.service.screen.findOne({ id: screenId })

    if (!screenInfo && screenId != 0) {
      ctx.body = getResponseError({
        message: '该屏已删除'
      })

      ctx.logger.info(
        getLoggerModel({
          message: '/component/create fail',
          data: {
            message: '该屏已删除',
            screenId: screenId
          }
        })
      )
      return
    }

    // 检查有没有大屏的编辑权限
    const permissionRes =
      await ctx.service.screen.checkScreenEditingPermissionByScreenId({
        screenId
      })

    if (!permissionRes.success && screenInfo.screenType !== 'child') {
      ctx.body = getResponseError({
        ...permissionRes.res
      })
      return
    }

    // 检查场景和页面是否被删除了
    const { success, errorRes } =
      await ctx.service.screen.checkSceneAndPageIsDel({
        screenInfo: screenInfo,
        sceneId: com.sceneId,
        pageId: com.pageId
      })

    if (!success) {
      ctx.body = getResponseError({
        ...errorRes
      })
      return
    }

    const comDefine = await ctx.service.package.findOne({
      name: com.name,
      version: com.version
    })
    if (!com.id) {
      com.id = uuid(comDefine.name)
    }
    // 处理预设子组件
    let subComDefines, subData
    if (
      comDefine &&
      comDefine.presetChildren &&
      comDefine.presetChildren.length
    ) {
      subComDefines = await ctx.service.package.find({
        name: { $in: comDefine.presetChildren }
      })
      // 抽取出最新版本配置
      subComDefines.sort((a, b) => {
        return -compareVersion(a.version, b.version)
      })
      subComDefines = _.uniqBy(subComDefines, 'name')
      if (subComDefines && subComDefines.length) {
        subData = subComDefines.map(subCom => {
          return _.assign(this.getComCommonConfig(subCom, 'subCom'), {
            parent: com.id,
            screenId
          })
        })
      }
    }
    const comData = _.assign(this.getComCommonConfig(comDefine, 'com'), {
      children: (subData && subData.map(d => d.id)) || [],
      screenId,
      ...com
    })

    // 合并主题配置
    const comthemeData = await ctx.service.comtheme.findOne({
      comType: comData.name,
      isDefault: true
    })
    if (screenInfo.themeSchemeId) {
      const comconfig = await ctx.service.comtheme.find(
        { comType: com.name, themeSchemeId: screenInfo.themeSchemeId },
        null,
        { sort: { createdAt: -1 } }
      )
      if (comconfig && comconfig.length) {
        comData.config = _.defaultsDeep(comconfig[0].config, comData.config)
        comData.other.themeUrl = comconfig[0].icon
      }
    } else if (comthemeData) {
      comData.config = _.defaultsDeep(comthemeData.config, comData.config) // 将默认设置的主题和组件原本的样式合并
      comData.other.themeUrl = comthemeData.icon
    }

    // 判断是否是模版关联组件,或者指标卡关联组件
    if (screenInfo.isScreentpl && screenId != 0) {
      comData.other.isScreentpl = true
      let controlConfig = JSON.parse(comData.controlConfig)
      addConfig(controlConfig, 'tplPicture')
      comData.controlConfig = JSON.stringify(controlConfig)
    }

    const allData = [comData, ...(subData ? subData : [])]
    const createRes = await ctx.service.component.create(allData)

    const res = createRes.map(d => {
      return _.omit(d.toJSON(), ['_id', '__v', 'staticData'])
    })
    // let layerTree = screenInfo.layerTree
    // screenInfo.layerTree.push({
    //   id: createRes.id,
    //   type: createRes.type
    // })
    // await ctx.service.screen.update({ id: screenId }, {
    //   layerTree
    // });
    ctx.body = getResponseBody(res)
  }

  // 创建子组件
  async createChild() {
    const { ctx } = this
    const { screenId } = ctx.query
    const { name, version, pid } = ctx.request.body
    if (!name || !pid) throw new Error('未提供父组件id和子组件名称！')
    const parentData = await ctx.service.component.findOne({ id: pid })
    if (!parentData) throw new Error('未找到父组件！')
    const childComDefine = await ctx.service.package.findOne({ name, version })
    const childComData = _.assign(
      this.getComCommonConfig(childComDefine, 'subCom'),
      {
        parent: pid,
        screenId
      }
    )
    const createRes = await ctx.service.component.create(childComData)
    const res = _.omit(createRes.toJSON(), ['_id', '__v', 'staticData'])
    // 更新父组件的子组件数组
    await ctx.service.component.update(
      { id: pid },
      { $addToSet: { children: res.id } }
    )
    ctx.body = getResponseBody(res)
  }

  async delete() {
    const { ctx } = this
    const screenId = ctx.query.id // 大屏id
    const sceneId = ctx.query.sceneId
    const pageId = ctx.query.pageId
    const { deleteIds } = ctx.request.body // 要删除的组件 id 数组
    if (_.isNil(deleteIds) || !deleteIds.length) {
      ctx.body = getResponseBody({ deleteIds: [] })
      return
    }

    const screenInfo = await ctx.service.screen.findOne({ id: screenId })

    if (!screenInfo) {
      ctx.body = getResponseError({
        message: '该屏已删除'
      })

      ctx.logger.info(
        getLoggerModel({
          message: '/component/delete fail',
          data: {
            message: '该屏已删除',
            screenId: screenId
          }
        })
      )
      return
    }

    // 检查有没有大屏的编辑权限
    const permissionRes =
      await ctx.service.screen.checkScreenEditingPermissionByScreenId({
        screenId: screenId
      })

    if (!permissionRes.success) {
      ctx.body = getResponseError({
        ...permissionRes.res
      })
      return
    }

    // 检查场景和页面是否被删除了
    const { success, errorRes } =
      await ctx.service.screen.checkSceneAndPageIsDel({
        screenInfo: screenInfo,
        sceneId: sceneId,
        pageId: pageId
      })

    if (!success) {
      ctx.body = getResponseError({
        ...errorRes
      })
      return
    }

    const res = await ctx.service.component.deleteMany({
      screenId: screenId,
      id: { $in: deleteIds }
    })

    setImmediate(() => {
      ctx.logger.info(
        getLoggerModel({
          message: '/component/delete success',
          data: {
            screenId: screenId,
            sceneId: sceneId,
            pageId: pageId,
            deleteIds: deleteIds
          }
        })
      )
    })

    ctx.body = getResponseBody({
      deleteIds
    })
  }

  // 删除子组件
  async deleteChild() {
    const { ctx } = this
    const { id, pid } = ctx.request.body
    if (!id || !pid) throw new Error('未提供父子组件id！')
    const parentData = await ctx.service.component.findOne({ id: pid })
    if (!parentData) throw new Error('未找到父组件！')
    const res = await ctx.service.component.deleteOne({ id })
    await ctx.service.component.update({ id: pid }, { $pull: { children: id } })
    ctx.body = getResponseBody(res)
  }

  async update() {
    const { ctx } = this
    const screenId = ctx.query.id
    const sceneId = ctx.query.sceneId
    const pageId = ctx.query.pageId
    const body = ctx.request.body
    const userId = ctx.header.sysuserid

    const screenInfo = await ctx.service.screen.findOne({ id: screenId })
    if (screenInfo.isScreentpl) {
      const updateArr = body.map(d => {
        return {
          updateOne: {
            filter: { screenId: screenId, id: d.id },
            update: d.data
          }
        }
      })
      const res = await ctx.service.component.bulkWrite(updateArr)
      ctx.body = getResponseBody(res)
      return
    }

    if (!screenInfo) {
      ctx.body = getResponseError({
        message: '该屏已删除'
      })

      ctx.logger.info(
        getLoggerModel({
          message: '/component/update fail',
          data: {
            message: '该屏已删除',
            screenId: screenId
          }
        })
      )
      return
    }

    // 检查有没有大屏的编辑权限
    const permissionRes =
      await ctx.service.screen.checkScreenEditingPermissionByScreenId({
        screenId: screenId
      })

    if (!permissionRes.success && screenInfo.screenType !== 'child') {
      ctx.body = getResponseError({
        ...permissionRes.res
      })
      return
    }

    // 检查场景和页面是否被删除了
    const { success, errorRes } =
      await ctx.service.screen.checkSceneAndPageIsDel({
        screenInfo: screenInfo,
        sceneId,
        pageId
      })

    if (!success) {
      ctx.body = getResponseError({
        ...errorRes
      })
      return
    }

    const updateArr = body.map(d => {
      return {
        updateOne: {
          filter: { screenId: screenId, id: d.id },
          update: d.data
        }
      }
    })
    const res = await ctx.service.component.bulkWrite(updateArr)
    ctx.body = getResponseBody(res)
  }

  async copy() {
    const { ctx } = this
    const screenId = ctx.query.screenId
    const body = ctx.request.body
    const t = new Date().getTime()
    if (_.isNil(body) || !body.length) {
      ctx.body = getResponseBody(null, false, '复制的组件不存在', 400)
      return
    }
    const allData = []
    const screenInfo = await ctx.service.screen.findOne({ id: screenId })

    if (!screenInfo) {
      ctx.body = getResponseError({
        message: '该屏已删除'
      })

      ctx.logger.info(
        getLoggerModel({
          message: '/component/update fail',
          data: {
            message: '该屏已删除',
            screenId: screenId
          }
        })
      )
      return
    }

    // 检查有没有大屏的编辑权限
    const permissionRes =
      await ctx.service.screen.checkScreenEditingPermissionByScreenId({
        screenId: screenId
      })

    if (!permissionRes.success) {
      ctx.body = getResponseError({
        ...permissionRes.res
      })
      return
    }

    let comIdMapping = {}
    for (const { originId, newData } of body) {
      comIdMapping[originId] = newData.id
    }
    for (let { originId, newData } of body) {
      const originCom = await ctx.service.component.findOne({ id: originId })
      // console.log(originCom, 'shuju==========')
      if (!originCom || !originCom.id) {
        ctx.body = getResponseBody(null, false, '复制的组件不存在', 400)
        return
      }
      const copyCom = originCom.toJSON()
      copyCom.screenId = screenId
      copyCom.id = newData.id
      copyCom.pageId = newData.pageId
      copyCom.sceneId = newData.sceneId
      copyCom.attr.x = newData.attr.x
      copyCom.attr.y = newData.attr.y
      copyCom.indicatorContainer = newData.indicatorContainer || false
      copyCom.children = []

      // 合并config配置
      if (_.isObject(newData.config)) {
        copyCom.config = _.merge(copyCom.config, newData.config)
      }
      // console.log(copyCom.interactionConfig, '数据=================')
      if (
        copyCom.interactionConfig.drillDown &&
        copyCom.interactionConfig.drillDown.length
      ) {
        for (let i = 0; i < copyCom.interactionConfig.drillDown.length; i++) {
          copyCom.interactionConfig.drillDown[i].triggerId = newData.id
        }
      }
      // 对于事件的复制映射
      if (copyCom.interactionConfig.events.length) {
        for (let i = 0; i < copyCom.interactionConfig.events.length; i++) {
          let componentIdList = []
          for (
            let m = 0;
            m < copyCom.interactionConfig.events[i].componentId.length;
            m++
          ) {
            const element = copyCom.interactionConfig.events[i].componentId[m]
            if (comIdMapping[element] && element === originId) {
              componentIdList.push(comIdMapping[element])
            } else {
              const haveCom = await ctx.service.component.findOne({
                id: element,
                screenId
              })
              if (haveCom) {
                componentIdList.push(element)
              }
            }
          }
          copyCom.interactionConfig.events[i].componentId = componentIdList
          let connectCompIdList = []
          for (
            let n = 0;
            n < copyCom.interactionConfig.events[i].connectCompId.length;
            n++
          ) {
            const element = copyCom.interactionConfig.events[i].connectCompId[n]
            if (comIdMapping[element]) {
              connectCompIdList.push(comIdMapping[element])
            }
          }
          copyCom.interactionConfig.events[i].connectCompId = connectCompIdList
          copyCom.interactionConfig.events[i].id = 'event_' + randomStr(10)
          copyCom.interactionConfig.events[i].triggerId = newData.id
        }
      }
      // 对于联动的组件映射
      if (
        copyCom.interactionConfig.linkAge &&
        copyCom.interactionConfig.linkAge.length
      ) {
        for (let i = 0; i < copyCom.interactionConfig.linkAge.length; i++) {
          copyCom.interactionConfig.linkAge[i].triggerId = newData.id
          const linkageComList =
            copyCom.interactionConfig.linkAge[i].linkageComList
          if (linkageComList.length == 0) {
            continue
          }
          copyCom.interactionConfig.linkAge[i].linkageComList = []
          if (linkageComList && linkageComList.length) {
            for (let index = 0; index < linkageComList.length; index++) {
              if (!comIdMapping[linkageComList[index].componentId]) {
                continue
              }
              copyCom.interactionConfig.linkAge[i].linkageComList[index] = {}
              copyCom.interactionConfig.linkAge[i].linkageComList[index][
                'componentId'
              ] = comIdMapping[linkageComList[index].componentId]
              copyCom.interactionConfig.linkAge[i].linkageComList[index][
                'componentName'
              ] = linkageComList[index].componentName
            }
          }
          const linkageComs = copyCom.interactionConfig.linkAge[i].coms
          copyCom.interactionConfig.linkAge[i].coms = []
          if (linkageComs && linkageComs.length) {
            for (let index = 0; index < linkageComs.length; index++) {
              if (!comIdMapping[linkageComs[index].id]) {
                continue
              }
              copyCom.interactionConfig.linkAge[i].coms[index] = {}
              copyCom.interactionConfig.linkAge[i].coms[index]['id'] =
                comIdMapping[linkageComs[index].id]
              copyCom.interactionConfig.linkAge[i].coms[index]['name'] =
                linkageComs[index].name
            }
          }
          if (
            copyCom.interactionConfig.linkAge[i].linkageConfig &&
            copyCom.interactionConfig.linkAge[i].linkageConfig.length
          ) {
            for (
              let j = 0;
              j < copyCom.interactionConfig.linkAge[i].linkageConfig.length;
              j++
            ) {
              const componentList =
                copyCom.interactionConfig.linkAge[i].linkageConfig[j]
                  .componentList
              copyCom.interactionConfig.linkAge[i].linkageConfig[
                j
              ].componentList = []
              if (componentList && componentList.length) {
                for (let k = 0; k < componentList.length; k++) {
                  if (!comIdMapping[componentList[k].componentId]) {
                    continue
                  }
                  copyCom.interactionConfig.linkAge[i].linkageConfig[
                    j
                  ].componentList[k] = {}
                  copyCom.interactionConfig.linkAge[i].linkageConfig[
                    j
                  ].componentList[k]['componentId'] =
                    comIdMapping[componentList[k].componentId]
                  copyCom.interactionConfig.linkAge[i].linkageConfig[
                    j
                  ].componentList[k]['componentName'] =
                    componentList[k].componentName
                  copyCom.interactionConfig.linkAge[i].linkageConfig[
                    j
                  ].componentList[k]['fieldId'] = componentList[k].fieldId
                  copyCom.interactionConfig.linkAge[i].linkageConfig[
                    j
                  ].componentList[k]['fieldRequestType'] =
                    componentList[k].fieldRequestType
                  copyCom.interactionConfig.linkAge[i].linkageConfig[
                    j
                  ].componentList[k]['fieldType'] = componentList[k].fieldType
                  copyCom.interactionConfig.linkAge[i].linkageConfig[
                    j
                  ].componentList[k]['fieldValue'] = componentList[k].fieldValue
                }
              }
            }
          }
        }
      }

      // copyCom.interactionConfig = {
      //   callbackParams: [],
      //   events: [],
      //   linkAge: []
      //   drillDown: []
      // };
      let screenIdMapping = {}
      let filterIdMapping = {}

      // 过滤器映射复制
      if (screenId !== originCom.screenId) {
        for (
          let index = 0;
          index < originCom.dataConfig.dataResponse.filters.list.length;
          index++
        ) {
          const filterId =
            originCom.dataConfig.dataResponse.filters.list[index].id
          const element = await ctx.service.filter.findOne({ id: filterId })
          // const element = params.filterInfo[index];
          const newfilter = await this.ctx.service.filter.create({
            screenId: screenId,
            name: element.name,
            content: element.content,
            callbackKeys: element.callbackKeys,
            type: element.type || 'custom',
            systemParams: element.systemParams || []
          })
          filterIdMapping[filterId] = newfilter.id
          // filterIdMapping[element.id] = newfilter.id
        }

        if (copyCom.dataConfig.dataResponse.filters.list.length !== 0) {
          for (
            let i = 0;
            i < copyCom.dataConfig.dataResponse.filters.list.length;
            i++
          ) {
            copyCom.dataConfig.dataResponse.filters.list[i].id =
              filterIdMapping[
                copyCom.dataConfig.dataResponse.filters.list[i].id
              ]
          }
        }
      }
      // 针对面板类组件进行复制
      if (EXPORT_COMTYPE.COPYCOMTYPE.includes(originCom.comType)) {
        console.log(originCom.config.screens, 'sdaad')
        for (let i = 0; i < originCom.config.screens.length; i++) {
          const quoteScreenId = originCom.config.screens[i].id
          console.log(copyCom.config.screens, '00000')
          const screenInfoNow = await ctx.service.screen.copy({
            screenIdOrigin: quoteScreenId,
            workspaceId: screenInfo.workspaceId,
            projectId: screenInfo.projectId,
            name: originCom.alias,
            isScreentpl: false,
            tag: [],
            t
          })
          console.log(copyCom.config.screens, '001111')
          screenIdMapping[quoteScreenId] = screenInfoNow.id
        }
      }
      if (EXPORT_COMTYPE.COPYCOMTYPE.includes(originCom.comType)) {
        for (let i = 0; i < copyCom.config.screens.length; i++) {
          copyCom.config.screens[i].id =
            screenIdMapping[copyCom.config.screens[i].id]
          await this.ctx.service.screen.update(
            { id: copyCom.config.screens[i].id },
            { relationCompId: copyCom.id }
          )
        }
      }
      allData.push(copyCom)
      // 子组件处理
      if (originCom.children && originCom.children.length) {
        let childs = await ctx.service.component.find({
          id: { $in: originCom.children }
        })
        childs = childs.map(d => d.toJSON())
        for (let child of childs) {
          child.id = uuid(child.name)
          child.parent = copyCom.id
          child.screenId = screenId
          child.interactionConfig = {
            callbackParams: [],
            events: [],
            drillDown: [],
            linkAge: []
          }
          copyCom.children.push(child.id)
          allData.push(child)
        }
      }
    }
    const copyRes = await ctx.service.component.insertMany(allData)
    const res = copyRes.map(d => {
      return _.omit(d.toJSON(), ['_id', '__v', 'staticData'])
    })
    ctx.body = getResponseBody(res)
  }

  // 复制子组件
  async copyChild() {
    const { ctx } = this
    const { id, pid } = ctx.request.body
    const originCom = await ctx.service.component.findOne({ id })
    const parentCom = await ctx.service.component.findOne({ id: pid })
    if (!originCom || !parentCom) throw new Error('复制的组件不存在！')
    const copyCom = originCom.toJSON()
    copyCom.id = uuid(copyCom.name)
    copyCom.interactionConfig.callbackParams = []
    copyCom.interactionConfig.events = []
    copyCom.interactionConfig.drillDown = []
    copyCom.interactionConfig.linkAge = []
    copyCom.dataConfig.dataResponse.filters.enable = false
    copyCom.dataConfig.dataResponse.filters.list = []
    copyCom.parent = pid
    const copyRes = await ctx.service.component.create(copyCom)
    const res = _.omit(copyRes.toJSON(), ['_id', '__v', 'staticData'])
    await ctx.service.component.update(
      { id: pid },
      { $addToSet: { children: res.id } }
    )
    ctx.body = getResponseBody(res)
  }

  // 已弃用！！！！！
  async data() {
    const { ctx } = this
    const screenId = ctx.query.screenId
    const query = ctx.request.body.query
    if (!query || !query.length) {
      ctx.body = getResponseBody(null, false, '请求参数错误', 400)
      return
    }
    const coms = await ctx.service.component.find({
      id: { $in: query },
      screenId
    })
    const res = {}
    for (let i = 0; i < coms.length; i++) {
      const com = coms[i]
      const dataResponse = com.dataConfig.dataResponse
      const type = dataResponse.sourceType
      if (type === 'static') {
        res[com.id] = com.staticData
      } else {
        // TODO: 其他数据类型待定
      }
    }
    ctx.body = getResponseBody(res)
  }

  async upgrade() {
    const { ctx } = this
    const comVersionList = ctx.request.body
    let data = []
    for (let i = 0; i < comVersionList.length; i++) {
      const comVersion = comVersionList[i]
      const comData = await ctx.service.component.upgrade(comVersion)
      data.push(comData)
    }
    ctx.body = getResponseBody(data)
  }

  async info() {
    const { ctx } = this
    const sid = ctx.query.id
    const body = ctx.request.body
    if (!sid || !body || !body.cid) {
      ctx.body = getResponseBody(null, false, '请求参数错误', 400)
      return
    }
    const data = await ctx.service.component.findOne({
      screenId: sid,
      id: body.cid
    })
    const where = data.dataConfig.dataResponse.source.dmc.data.where
    ctx.body = getResponseBody(where)
  }

  async cominfo() {
    const { ctx } = this
    const sid = ctx.query.id
    const body = ctx.request.body
    if (!sid || !body || !body.cid) {
      ctx.body = getResponseBody(null, false, '请求参数错误', 400)
      return
    }
    const data = await ctx.service.component.findOne({
      screenId: sid,
      id: body.cid
    })
    // const where = data.dataConfig.dataResponse.source.dmc.data.where;
    ctx.body = getResponseBody(data)
  }

  // 组件下载
  async download() {
    const { ctx, config } = this
    const comNames = JSON.parse(ctx.query.comNames) || []
    const atomDir = path.resolve(config.resourcePath, `./atom`)
    const tmpDir = fs.mkdtempSync(`${os.tmpdir()}${path.sep}`)
    const server = ''
    const pList = []
    async function setStream() {
      const time = new Date().getTime()
      return new Promise((resolve, reject) => {
        console.log('开始打包...................')
        exec(
          `seatom config set server ${server} && seatom packp && mv atom atom${time} && rm -rf atom.tar.gz`,
          {
            cwd: tmpDir
          },
          async (error, stdout, stderr) => {
            if (error) {
              ctx.body = getResponseBody(
                null,
                false,
                '组件打包出错：' + error,
                400
              )
              reject(error)
            }
            try {
              const targetDir = path.resolve(tmpDir, `atom${time}`)
              console.log('开始压缩...................')
              await compressing.tgz.compressDir(
                targetDir,
                `${targetDir}.tar.gz`
              )
              console.log('压缩完成.........')
              ctx.attachment(`${targetDir}.tar.gz`)
              ctx.set('Content-Type', 'application/octet-stream')
              ctx.body = fs.createReadStream(`${targetDir}.tar.gz`)
              resolve()
            } catch (err) {
              reject(err)
            }
          }
        )
      })
    }
    for (let i = 0; i < comNames.length; i++) {
      const comName = comNames[i]
      const comDir = path.resolve(atomDir, `./${comName}`)
      if (!fs.existsSync(atomDir)) fs.mkdirSync(atomDir)
      if (fs.existsSync(comDir)) {
        // 已存在该组件目录，拉取代码后执行打包
        const p = new Promise((resolve, reject) => {
          console.log('开始准备依赖...................' + comName)
          exec(
            `cd ${comDir} && git pull && npm install --production && cp -r ${comDir} ${tmpDir}`,
            async (error, stdout, stderr) => {
              if (error) {
                ctx.body = getResponseBody(
                  null,
                  false,
                  '组件代码拉取出错：' + error,
                  400
                )
                reject(error)
              }
              console.log('安装依赖完成........')
              resolve()
            }
          )
        })
        pList.push(p)
      } else {
        // 组件目录不存在，执行git clone
        const p = new Promise((resolve, reject) => {
          console.log('开始初始化仓库并安装依赖...................')
          exec(
            `git clone ssh://******************:10022/fuxi-arms/${comName}.git && cp -r ${comDir} ${tmpDir} && cd ${tmpDir}/${comName} && npm install --production`,
            { cwd: atomDir },
            async (error, stdout, stderr) => {
              if (error) {
                ctx.body = getResponseBody(
                  null,
                  false,
                  '组件仓库初始化出错：' + error,
                  400
                )
                reject(error)
              }
              resolve()
            }
          )
        })
        pList.push(p)
      }
    }
    await Promise.all(pList)
    await setStream()
  }
}

module.exports = ComponentsController
