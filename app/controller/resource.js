'use strict'
// 静态资源
const _ = require('lodash')
const Controller = require('egg').Controller
let fs = require('fs-extra')
const { getResponseBody, uuid } = require('../extend/utils')
const { cipher, decipher, encryptPath } = require('../extend/crypto')
const path = require('path')
const moment = require('moment')
class ResourceController extends Controller {
  // 创建文件夹和库
  async createfolder() {
    const { ctx } = this
    const body = ctx.request.body
    const res = await ctx.service.resource.findOne({ body })
    if (res) {
      ctx.body = getResponseBody(null, false, '文件夹名称已存在', 400)
      return
    }
    if (body.resourceLibraryCode !== 'private') {
      delete body.userId
    }
    const result = await ctx.service.resource.create(body)
    ctx.body = getResponseBody(result)
  }
  // 大屏上传icon
  async uploadScreenIcon() {
    const { ctx, config } = this
    const body = ctx.request.body
    const screenId = body.screenId
    // const resourceUrl = body.resourceUrl
    const resourceUrlList = body.resourceUrlList || [body.resourceUrl]
    const changeColor = body.changeColor || false

    for (let index = 0; index < resourceUrlList.length; index++) {
      const element = resourceUrlList[index]
      const svgItem = element
      const svgPath = svgItem.split('/public/')[1]
      const filePath = path.resolve(config.resourcePath, `./${svgPath}`)
      if (!fs.existsSync(filePath)) {
        ctx.body = getResponseBody(null, false, 'icon文件不存在', 400)
        return
      }
    }

    await ctx.service.screen.update(
      { id: screenId },
      {
        $addToSet: {
          'screenIcons.iconList': {
            $each: resourceUrlList
          }
        }
      }
    )
    const screenInfo = await ctx.service.screen.findOne({ id: screenId })
    let screenIcons = screenInfo.screenIcons || {
      mergeSvg: '',
      iconList: []
    }
    try {
      await ctx.service.common.mergeSvg({
        changeColor,
        screenId,
        ...screenIcons,
        resourceUrlList
      })
      const mergeSvgPath = `/public/system/screenIcons/symbol/screen_${screenId}_icon.svg`
      let svgIdList = []
      resourceUrlList.map(item => {
        const svgNameArr = item.split('/')
        const svgId = `${svgNameArr[svgNameArr.length - 1]}`
        if (changeColor) {
          svgIdList.push(`changeColor${svgId.split('.sv')[0]}`)
        } else {
          svgIdList.push(svgId.split('.sv')[0])
        }
      })
      screenIcons.mergeSvg = mergeSvgPath
      await ctx.service.screen.update(
        { id: screenId },
        { 'screenIcons.mergeSvg': mergeSvgPath }
      )
      ctx.body = getResponseBody({
        mergeSvgPath,
        svgId: svgIdList
      })
    } catch (error) {
      ctx.body = getResponseBody(null, false, '合图报错！', 400)
    }
  }
  // 资源上传
  async upload() {
    const { ctx } = this
    const body = ctx.request.body
    const userId = ctx.request.header.sysuserid
    if (!ctx.request.files || !ctx.request.files.length) {
      ctx.body = getResponseBody(null, false, '无法创建，上传文件为空！', 400)
      return
    }

    const date = moment().format('YYYY-MM')

    let resourcePath
    if (body.resourceLibraryCode === 'system') {
      if (
        body.resourceType === 'themeVideo' ||
        body.resourceType === 'themePicture'
      ) {
        resourcePath = `${body.resourceLibraryCode}/theme/${date}`
      } else if (
        body.resourceType === 'tplVideo' ||
        body.resourceType === 'tplPicture'
      ) {
        resourcePath = `${body.resourceLibraryCode}/tpl/${date}`
      } else {
        resourcePath = `${body.resourceLibraryCode}/common/${date}`
      }
    }
    if (body.resourceLibraryCode === 'personal') {
      // 将个人库变为公共库
      resourcePath = `personal/${date}`
    }
    if (body.resourceLibraryCode === 'private') {
      // 新增个人私有库
      resourcePath = userId ? `private/${userId}/${date}` : `private/${date}`
    }
    const filefolder = resourcePath // 上传的文件夹路径
    const file = ctx.request.files[0]
    const fileData = {
      file,
      filefolder
    }
    let data
    // 对于合图信息做单独处理
    if (body.svgType && body.svgType === 'merge') {
      data = await ctx.service.common.uploadMergeSvg(fileData)
    } else {
      data = await ctx.service.common.upload(fileData)
    }
    let resourceData = {
      resourceType: body.resourceType,
      resourceLibrary: body.resourceLibrary,
      resourceFolder: body.resourceFolder,
      resourceLibraryCode: body.resourceLibraryCode,
      userId: userId
    }
    if (body.resourceLibraryCode !== 'private') {
      resourceData = {
        resourceType: body.resourceType,
        resourceLibrary: body.resourceLibrary,
        resourceFolder: body.resourceFolder,
        resourceLibraryCode: body.resourceLibraryCode
      }
    }
    const res = await ctx.service.resource.findOne(resourceData)
    if (res) {
      let result
      if (data.length && data.length > 0) {
        result = await ctx.service.resource.update(resourceData, {
          resourceList: [...res.resourceList, ...data]
        })
      } else {
        result = await ctx.service.resource.update(resourceData, {
          $addToSet: {
            resourceList: {
              resourceId: uuid(),
              resourceUrl: data.url,
              resourceName: data.fileName
            }
          }
        })
      }
      ctx.body = getResponseBody(result)
    } else {
      let params
      if (data.length && data.length > 0) {
        params = {
          resourceList: data,
          ...resourceData
        }
      } else {
        params = {
          resourceList: [
            {
              resourceUrl: data.url,
              resourceName: data.fileName,
              resourceId: uuid()
            }
          ],
          ...resourceData
        }
      }
      const result = await ctx.service.resource.create(params)
      ctx.body = getResponseBody(result)
    }
  }

  async index() {
    const { ctx } = this
    const resourceType = ctx.query.resourceType
    const userId = ctx.request.header.sysuserid
    await ctx.service.resource.update(
      { resourceLibrary: '个人库' },
      { resourceLibrary: '公共库' },
      { multi: true }
    )
    let params = resourceType ? ctx.query : {}
    let res
    if (params.resourceType && params.resourceType === 'tplPicture') {
      params = {
        resourceType: { $in: ['tplPicture', 'picture'] }
      }
    }
    if (params.resourceType && params.resourceType === 'themePicture') {
      params = {
        resourceType: { $in: ['themePicture', 'picture'] }
      }
    }
    if (params.resourceType && params.resourceType === 'picture') {
      if (
        !(await ctx.service.resource.findOne({
          resourceLibraryCode: 'personal',
          resourceFolder: '默认文件夹',
          resourceType: 'picture'
        }))
      ) {
        const parms = {
          resourceType: 'picture',
          resourceLibrary: '公共库',
          resourceLibraryCode: 'personal',
          resourceFolder: '默认文件夹',
          resourceList: []
        }
        await ctx.service.resource.create(parms)
      }
      if (
        !(await ctx.service.resource.findOne({
          resourceLibraryCode: 'private',
          resourceFolder: '默认文件夹',
          resourceType: 'picture',
          userId
        }))
      ) {
        const parms = {
          resourceType: 'picture',
          resourceLibrary: '私有库',
          resourceLibraryCode: 'private',
          resourceFolder: '默认文件夹',
          resourceList: [],
          userId
        }
        await ctx.service.resource.create(parms)
      }
    }
    if (params.resourceType && params.resourceType === 'video') {
      if (
        !(await ctx.service.resource.findOne({
          resourceLibraryCode: 'personal',
          resourceFolder: '默认文件夹',
          resourceType: 'video'
        }))
      ) {
        const parms = {
          resourceType: 'video',
          resourceLibrary: '公共库',
          resourceLibraryCode: 'personal',
          resourceFolder: '默认文件夹',
          resourceList: []
        }
        await ctx.service.resource.create(parms)
      }
      if (
        !(await ctx.service.resource.findOne({
          resourceLibraryCode: 'private',
          resourceFolder: '默认文件夹',
          resourceType: 'video',
          userId
        }))
      ) {
        const parms = {
          resourceType: 'video',
          resourceLibrary: '私有库',
          resourceLibraryCode: 'private',
          resourceFolder: '默认文件夹',
          resourceList: [],
          userId
        }
        await ctx.service.resource.create(parms)
      }
    }
    if (params.resourceType && params.resourceType === 'docfile') {
      if (
        !(await ctx.service.resource.findOne({
          resourceLibraryCode: 'personal',
          resourceFolder: '默认文件夹',
          resourceType: 'docfile'
        }))
      ) {
        const parms = {
          resourceType: 'docfile',
          resourceLibrary: '公共库',
          resourceLibraryCode: 'personal',
          resourceFolder: '默认文件夹',
          resourceList: []
        }
        await ctx.service.resource.create(parms)
      }
    }
    if (params.resourceType && params.resourceType === 'icon') {
      if (
        !(await ctx.service.resource.findOne({
          resourceLibraryCode: 'system',
          resourceFolder: '默认文件夹',
          resourceType: 'icon'
        }))
      ) {
        const parms = {
          resourceType: 'icon',
          resourceLibrary: '系统库',
          resourceLibraryCode: 'system',
          resourceFolder: '默认文件夹',
          resourceList: []
        }
        await ctx.service.resource.create(parms)
      }
      if (
        !(await ctx.service.resource.findOne({
          resourceLibraryCode: 'personal',
          resourceFolder: '默认文件夹',
          resourceType: 'icon'
        }))
      ) {
        const parms = {
          resourceType: 'icon',
          resourceLibrary: '公共库',
          resourceLibraryCode: 'personal',
          resourceFolder: '默认文件夹',
          resourceList: []
        }
        await ctx.service.resource.create(parms)
      }
      if (
        !(await ctx.service.resource.findOne({
          resourceLibraryCode: 'private',
          resourceFolder: '默认文件夹',
          resourceType: 'icon',
          userId
        }))
      ) {
        const parms = {
          resourceType: 'icon',
          resourceLibrary: '私有库',
          resourceLibraryCode: 'private',
          resourceFolder: '默认文件夹',
          resourceList: [],
          userId
        }
        await ctx.service.resource.create(parms)
      }
    }
    const publicData = await ctx.service.resource.find({
      resourceLibraryCode: { $in: ['system', 'personal'] },
      ...params
    })
    const privateData = await ctx.service.resource.find({
      userId,
      resourceLibraryCode: 'private',
      ...params
    })
    res = [...publicData, ...privateData]
    let library = []
    let data = []
    res.map(item => {
      library.unshift({
        resourceLibrary: item.resourceLibrary,
        resourceLibraryCode: item.resourceLibraryCode
      })
    })
    library = _.uniqBy(library, 'resourceLibrary')
    if (library && library.length >= 3) {
      library = [
        { resourceLibrary: '私有库', resourceLibraryCode: 'private' },
        { resourceLibrary: '公共库', resourceLibraryCode: 'personal' },
        { resourceLibrary: '系统库', resourceLibraryCode: 'system' }
      ]
    }
    for (let index = 0; index < library.length; index++) {
      let folderList
      if (params.resourceType && params.resourceType === 'tplPicture') {
        folderList = res.filter(item => {
          return (
            (item.resourceType == 'tplPicture' &&
              item.resourceLibrary == library[index].resourceLibrary) ||
            (item.resourceType == 'picture' &&
              item.resourceLibrary == library[index].resourceLibrary)
          )
        })
      } else if (
        params.resourceType &&
        params.resourceType === 'themePicture'
      ) {
        folderList = res.filter(item => {
          return (
            (item.resourceType == 'themePicture' &&
              item.resourceLibrary == library[index].resourceLibrary) ||
            (item.resourceType == 'picture' &&
              item.resourceLibrary == library[index].resourceLibrary)
          )
        })
      } else {
        folderList = res.filter(item => {
          return (
            item.resourceType == resourceType &&
            item.resourceLibrary == library[index].resourceLibrary
          )
        })
      }
      //给folderList 添加字段
      folderList = folderList.map(folder => {
        folder.resourceList.map(item => {
          return Object.assign(item, {
            ecryptUrl: encryptPath(item.resourceUrl.replace(/^\/public/, ''))
          })
        })
        return folder
      })
      data.push({
        name: library[index].resourceLibrary,
        resourceLibraryCode: library[index].resourceLibraryCode,
        folderList: folderList
      })
    }
    ctx.body = getResponseBody(data)
  }

  // 删除文件
  async delete() {
    const { ctx } = this
    const body = ctx.request.body
    const resourceData = body
    try {
      for (let index = 0; index < resourceData.length; index++) {
        // console.log(resourceData[index], 'dasdsada')
        const res = await ctx.service.resource.findOne({
          id: resourceData[index].id
        })
        let resourceList = res.resourceList.filter(item => {
          return item.resourceId !== resourceData[index].resourceId
        })
        await ctx.service.resource.update(
          { id: resourceData[index].id },
          { resourceList }
        )
      }
      ctx.body = getResponseBody()
    } catch {
      ctx.body = getResponseBody(null, false, '部分文件删除失败', 400)
    }
  }

  // 删除文件夹
  async deletefolder() {
    const { ctx } = this
    const body = ctx.request.body
    const idList = body
    try {
      for (let index = 0; index < idList.length; index++) {
        await ctx.service.resource.delete({ id: idList[index] })
      }
      ctx.body = getResponseBody()
    } catch {
      ctx.body = getResponseBody(null, false, '部分文件夹删除失败', 400)
    }
  }

  // 更新库名和文件夹名
  async updatefolder() {
    const { ctx } = this
    // const resourceLibraryCode = ctx.query.resourceLibraryCode
    const id = ctx.query.id
    const body = ctx.request.body
    const data = await ctx.service.resource.update({ id }, body)
    ctx.body = getResponseBody(data)
  }
  // 更新文件名
  async updateFileName() {
    const { ctx } = this
    const resourceLibraryCode = ctx.query.resourceLibraryCode
    const id = ctx.query.id
    const body = ctx.request.body
    const data = await ctx.service.resource.findOne({ id, resourceLibraryCode })
    data.resourceList.map(item => {
      if (item.resourceId === body.resourceId) {
        item.resourceName = body.resourceName
      }
    })
    const res = await ctx.service.resource.update(
      { id, resourceLibraryCode },
      data
    )
    ctx.body = getResponseBody(res)
  }

  // 移动文件
  async moveFile() {
    const { ctx } = this
    const body = ctx.request.body
    const id = body.id //文件的id
    const moveId = body.moveId
    const resourceList = body.resourceList
    for (let index = 0; index < resourceList.length; index++) {
      const element = resourceList[index]
      await ctx.service.resource.update(
        { id },
        { $pull: { resourceList: { resourceId: element.resourceId } } }
      ) // 删除原来的资源
    }
    const resourcedata = await ctx.service.resource.update(
      { id: moveId },
      { $push: { resourceList: { $each: resourceList } } }
    )
    ctx.body = getResponseBody(resourcedata)
  }
}

module.exports = ResourceController
