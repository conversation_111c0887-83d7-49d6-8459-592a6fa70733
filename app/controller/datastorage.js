'use strict'

const Controller = require('egg').Controller
const { values } = require('lodash')
const { getResponseBody } = require('../extend/utils')
const md5 = require('md5')
class DataStorageController extends Controller {
  async index() {
    const { ctx } = this
    const data = await ctx.service.datastorage.find()
    ctx.body = getResponseBody(data)
  }

  async list() {
    const { ctx } = this
    const data = await ctx.service.datastorage.find(ctx.query)
    ctx.body = getResponseBody(data)
  }

  async create() {
    const { ctx } = this
    const body = ctx.request.body
    const defaultData = await ctx.service.datastorage.find({
      workspaceId: body.workspaceId,
      name: body.name
    })
    if (defaultData && defaultData.length) {
      ctx.body = getResponseBody(null, false, '数据源名称已存在')
      return
    }
    const data = await ctx.service.datastorage.create(body)
    ctx.body = getResponseBody(data)
  }

  async delete() {
    const { ctx } = this
    const id = ctx.query.id
    const res = await ctx.service.datastorage.delete({ id })
    ctx.body = getResponseBody(res)
  }

  async update() {
    const { ctx } = this
    const id = ctx.query.id
    const body = ctx.request.body
    const res = await ctx.service.datastorage.update({ id: id }, body)
    ctx.body = getResponseBody(res)
  }

  async getDbList() {
    const { ctx } = this
    const body = ctx.query
    const res = await ctx.service.datastorage.getDbList(body)
    ctx.body = getResponseBody(res)
  }

  // async getData() {
  //   const { ctx } = this
  //   const componentId = ctx.query.componentId
  //   const type = ctx.query.type
  //   const queryParam = ctx.query
  //   const bodyParam = ctx.request.body
  //   const headerParam = {
  //     systoken: ctx.header.systoken,
  //     sysuserid: ctx.header.sysuserid
  //   }
  //   // 合并接口对特殊处理
  //   const ret = []
  //   if (Object.keys(bodyParam)[0] == 0) {
  //     let resData = []
  //     bodyParam.forEach(value => {
  //       resData.push(
  //         new Promise(async (resolve, reject) => {
  //           try {
  //             const config = Object.assign(value, queryParam, headerParam)
  //             const res = await ctx.service.datastorage.getData(config)
  //             resolve(res)
  //           } catch (e) {
  //             resolve(e)
  //           }
  //         })
  //       )
  //     })

  //     const ret = await Promise.all(resData)
  //     const returnValue = ret.map(item => {
  //       return getResponseBody(item)
  //     })
  //     ctx.body = returnValue
  //   } else {
  //     const config = Object.assign(queryParam, bodyParam, headerParam)
  //     const res = await ctx.service.datastorage.getData(config)
  //     ctx.body = getResponseBody(res)
  //   }
  // }
  async verifySql() {
    const { ctx } = this
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = {
      systoken: ctx.header.systoken,
      sysuserid: ctx.header.sysuserid
    }
    const key = md5(JSON.stringify(Object.assign(bodyParam, queryParam)))
    const enableCache = ctx.request.header['enable-cache'] === 'true' || false
    let value = await ctx.service.cacheservice.get(key)
    if (Object.keys(bodyParam)[0] == 0) {
      if (value && enableCache) {
        ctx.body = value
        const env = ctx.app.config.env
        const tasklist = await ctx.service.cacheservice.hget(
          `tasklist_${env}`,
          key
        )
        if (!tasklist) {
          await ctx.service.cacheservice.hset(`tasklist_${env}`, key, {
            list: Object.assign(bodyParam, queryParam),
            headerParam
          })
        }
        return
      }
      let resData = []
      let excelData = []
      for (let v = 0; v < bodyParam.length; v++) {
        const value = bodyParam[v]
        if (value.sourceType && value.sourceType === 'excel') {
          const config = Object.assign(value, queryParam, headerParam)
          excelData.push({
            config,
            index: v
          })
        } else {
          resData.push(
            new Promise(async (resolve, reject) => {
              try {
                const config = Object.assign(value, queryParam, headerParam)
                const res = await ctx.service.datastorage.getData(config)
                resolve(res)
              } catch (e) {
                resolve({
                  code: e.code,
                  message: e.message
                })
              }
            })
          )
        }
      }
      let ret = await Promise.all(resData)
      for (let e = 0; e < excelData.length; e++) {
        const config = excelData[e].config
        const i = excelData[e].index
        let excelres
        try {
          excelres = await ctx.service.datastorage.getData(config)
        } catch (error) {
          excelres = {
            error: error
          }
        }
        ret.splice(i, 0, excelres)
      }
      const returnValue = ret.map(item => {
        if (item.code == 801) {
          return getResponseBody(null, false, item.message, 400)
        }
        return getResponseBody(null, true, item.message)
      })
      if (!value || !enableCache) {
        ctx.body = returnValue
      }
      await ctx.service.cacheservice.set(key, returnValue, 60000)
    } else {
      if (value && enableCache) {
        ctx.body = getResponseBody(value)
        return
      }
      const config = Object.assign(queryParam, bodyParam, headerParam)
      const res = await ctx.service.datastorage.getData(config)
      if (!value || !enableCache) {
        ctx.body = getResponseBody(res)
      }
      await ctx.service.cacheservice.set(key, res, 60000)
    }
  }
  async getData() {
    const { ctx } = this
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = {
      systoken: ctx.header.systoken,
      sysuserid: ctx.header.sysuserid
    }
    const key = md5(JSON.stringify(Object.assign(bodyParam, queryParam)))
    const enableCache = ctx.request.header['enable-cache'] === 'true' || false
    let value = await ctx.service.cacheservice.get(key)
    console.log('getData', key, value)
    // 合并接口对特殊处理
    if (Object.keys(bodyParam)[0] == 0) {
      if (value && enableCache) {
        this.ctx.logger.info('getData数据缓存', key, bodyParam, queryParam)
        ctx.body = value
        const env = ctx.app.config.env
        // await this.app.redis.get('db0').del(`tasklist_${env}`)
        const tasklist = await ctx.service.cacheservice.hget(
          `tasklist_${env}`,
          key
        )
        if (!tasklist) {
          await ctx.service.cacheservice.hset(`tasklist_${env}`, key, {
            list: Object.assign(bodyParam, queryParam),
            headerParam
          })
        }
        return
      }
      this.ctx.logger.info('getData数据不缓存', key, bodyParam, queryParam)
      let resData = []
      let excelData = []
      for (let v = 0; v < bodyParam.length; v++) {
        const value = bodyParam[v]
        if (value.sourceType && value.sourceType === 'excel') {
          const config = Object.assign(value, queryParam, headerParam)
          excelData.push({
            config,
            index: v
          })
        } else {
          resData.push(
            new Promise(async (resolve, reject) => {
              try {
                const config = Object.assign(value, queryParam, headerParam)
                const res = await ctx.service.datastorage.getData(config)
                resolve(res)
              } catch (e) {
                resolve({
                  code: e.code,
                  message: e.message
                })
              }
            })
          )
        }
      }
      // bodyParam.forEach(value => {
      // })
      let ret = await Promise.all(resData)
      // 针对无法并发读取同一个excel表进行修改
      for (let e = 0; e < excelData.length; e++) {
        const config = excelData[e].config
        const i = excelData[e].index
        let excelres
        try {
          excelres = await ctx.service.datastorage.getData(config)
        } catch (error) {
          excelres = {
            error: error
          }
        }
        ret.splice(i, 0, excelres)
      }
      const returnValue = ret.map(item => {
        if (item.code == 801) {
          return getResponseBody(null, false, item.message, 400)
        }
        return getResponseBody(item)
      })
      if (!value || !enableCache) {
        ctx.body = returnValue
      }
      await ctx.service.cacheservice.set(key, returnValue, 60000)
      return
    } else {
      if (value && enableCache) {
        this.ctx.logger.info('getData数据缓存1', key, bodyParam, queryParam)
        ctx.body = getResponseBody(value)
        return
      }
      this.ctx.logger.info('getData数据不缓存1', key, bodyParam, queryParam)
      const config = Object.assign(queryParam, bodyParam, headerParam)
      const res = await ctx.service.datastorage.getData(config)
      if (!value || !enableCache) {
        ctx.body = getResponseBody(res)
      }
      await ctx.service.cacheservice.set(key, res, 60000)
    }
  }
  async getFieldList() {
    const { ctx } = this
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = ctx.header
    if (Object.keys(bodyParam)[0] == 0) {
      let resData = []
      bodyParam.forEach(value => {
        resData.push(
          new Promise(async (resolve, reject) => {
            try {
              const config = Object.assign(value, queryParam, headerParam)
              const res = await ctx.service.datastorage.getFieldList(config)
              resolve(res)
            } catch (e) {
              resolve(e)
            }
          })
        )
      })

      const ret = await Promise.all(resData)
      const returnValue = ret.map(item => {
        return getResponseBody(item)
      })
      ctx.body = returnValue
    } else {
      const config = Object.assign(queryParam, bodyParam, headerParam)
      const res = await ctx.service.datastorage.getFieldList(config)
      ctx.body = getResponseBody(res)
    }
  }

  async getTreeList() {
    const { ctx } = this
    // to_do：不能放在这里拼参数，可能有其他类型的数据源要用的
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = ctx.header
    const config = Object.assign(queryParam, bodyParam, headerParam)
    const res = await ctx.service.datastorage.getTreeList(config)
    ctx.body = getResponseBody(res)
  }

  async getFolderTbList() {
    const { ctx } = this
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = ctx.header
    const config = Object.assign(queryParam, bodyParam, headerParam)
    const res = await ctx.service.datastorage.getFolderTbList(config)
    ctx.body = getResponseBody(res)
  }

  async searchTb() {
    const { ctx } = this
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = ctx.header
    const config = Object.assign(queryParam, bodyParam, headerParam)
    const res = await ctx.service.datastorage.searchTb(config)
    ctx.body = getResponseBody(res)
  }

  async preview() {
    const { ctx } = this
    const queryParam = ctx.query
    const headerParam = ctx.header
    const config = Object.assign(queryParam, headerParam)
    const res = await ctx.service.datastorage.preview(config)
    ctx.body = getResponseBody(res)
  }

  async getFunctionList() {
    const { ctx } = this
    const queryParam = ctx.query
    const headerParam = ctx.header
    const config = Object.assign(queryParam, headerParam)
    const res = await ctx.service.datastorage.getFunctionList(config)
    ctx.body = getResponseBody(res)
  }
}

module.exports = DataStorageController
