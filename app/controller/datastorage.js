'use strict'
// 数据源
const Controller = require('egg').Controller
const { values } = require('lodash')
const { getResponseBody, getResponseError } = require('../extend/utils')
const md5 = require('md5')
class DataStorageController extends Controller {
  async index() {
    const { ctx } = this
    const data = await ctx.service.datastorage.find()
    ctx.body = getResponseBody(data)
  }

  async list() {
    const { ctx } = this
    const data = await ctx.service.datastorage.find(ctx.query)
    ctx.body = getResponseBody(data)
  }

  async create() {
    const { ctx } = this
    const body = ctx.request.body
    const defaultData = await ctx.service.datastorage.find({
      workspaceId: body.workspaceId,
      name: body.name
    })
    if (defaultData && defaultData.length) {
      ctx.body = getResponseBody(null, false, '数据源名称已存在')
      return
    }
    const data = await ctx.service.datastorage.create(body)
    ctx.body = getResponseBody(data)
  }

  async delete() {
    const { ctx } = this
    const id = ctx.query.id
    const res = await ctx.service.datastorage.delete({ id })
    ctx.body = getResponseBody(res)
  }

  async update() {
    const { ctx } = this
    const id = ctx.query.id
    const body = ctx.request.body
    const res = await ctx.service.datastorage.update({ id: id }, body)
    ctx.body = getResponseBody(res)
  }

  async diagnose() {
    const { ctx } = this
    const body = ctx.request.body

    try {
      // 根据数据库类型创建连接实例
      const dbconns = {
        mysql: require('../helper/datasource/mysql'),
        postgresql: require('../helper/datasource/postgresql'),
        highgodb: require('../helper/datasource/highgodb'),
        oracle: require('../helper/datasource/oracle'),
        dmdb: require('../helper/datasource/dmdb'),
        gbase8s: require('../helper/datasource/gbase8s')
      }

      if (!dbconns[body.type]) {
        ctx.body = getResponseBody(null, false, `不支持的数据库类型: ${body.type}`)
        return
      }

      const dbconn = new dbconns[body.type](body, ctx.service.datastorage)

      let result
      if (body.type === 'highgodb' && dbconn.diagnoseConnection) {
        // 瀚高数据库使用详细诊断
        result = await dbconn.diagnoseConnection()
      } else if (dbconn.testConnection) {
        // 其他数据库使用标准连接测试
        result = await dbconn.testConnection()
      } else {
        // 简单的连接测试
        try {
          await dbconn.execute('SELECT 1')
          result = {
            success: true,
            message: '数据库连接成功',
            timestamp: new Date().toISOString()
          }
        } catch (error) {
          result = {
            success: false,
            message: `数据库连接失败: ${error.message}`,
            error: error.code,
            timestamp: new Date().toISOString()
          }
        }
      }

      ctx.body = getResponseBody(result, result.success, result.message)

    } catch (error) {
      ctx.logger.error('数据库诊断失败', error)
      ctx.body = getResponseBody(null, false, `诊断失败: ${error.message}`)
    }
  }

  async getDbList() {
    const { ctx } = this
    const body = ctx.query
    const res = await ctx.service.datastorage.getDbList(body)
    ctx.body = getResponseBody(res)
  }

  // async getData() {
  //   const { ctx } = this
  //   const componentId = ctx.query.componentId
  //   const type = ctx.query.type
  //   const queryParam = ctx.query
  //   const bodyParam = ctx.request.body
  //   const headerParam = {
  //     systoken: ctx.header.systoken,
  //     sysuserid: ctx.header.sysuserid
  //   }
  //   // 合并接口对特殊处理
  //   const ret = []
  //   if (Object.keys(bodyParam)[0] == 0) {
  //     let resData = []
  //     bodyParam.forEach(value => {
  //       resData.push(
  //         new Promise(async (resolve, reject) => {
  //           try {
  //             const config = Object.assign(value, queryParam, headerParam)
  //             const res = await ctx.service.datastorage.getData(config)
  //             resolve(res)
  //           } catch (e) {
  //             resolve(e)
  //           }
  //         })
  //       )
  //     })

  //     const ret = await Promise.all(resData)
  //     const returnValue = ret.map(item => {
  //       return getResponseBody(item)
  //     })
  //     ctx.body = returnValue
  //   } else {
  //     const config = Object.assign(queryParam, bodyParam, headerParam)
  //     const res = await ctx.service.datastorage.getData(config)
  //     ctx.body = getResponseBody(res)
  //   }
  // }
  async verifySql() {
    const { ctx } = this
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = {
      systoken: ctx.header.systoken,
      sysuserid: ctx.header.sysuserid,
    }
    const key = md5(JSON.stringify(Object.assign(bodyParam, queryParam)))
    const enableCache = ctx.request.header['enable-cache'] === 'true' || false
    let value = await ctx.service.cacheservice.get(key)
    if (Object.keys(bodyParam)[0] == 0) {
      if (value && enableCache) {
        ctx.body = value
        const env = ctx.app.config.env
        const tasklist = await ctx.service.cacheservice.hget(
          `tasklist_${env}`,
          key
        )
        if (!tasklist) {
          await ctx.service.cacheservice.hset(`tasklist_${env}`, key, {
            list: Object.assign(bodyParam, queryParam),
            headerParam
          })
        }
        return
      }
      let resData = []
      let excelData = []
      for (let v = 0; v < bodyParam.length; v++) {
        const value = bodyParam[v]
        if (value.sourceType && value.sourceType === 'excel') {
          const config = Object.assign(value, queryParam, headerParam)
          excelData.push({
            config,
            index: v
          })
        } else {
          resData.push(
            new Promise(async (resolve, reject) => {
              try {
                const config = Object.assign(value, queryParam, headerParam)
                const res = await ctx.service.datastorage.getData(config)
                resolve(res)
              } catch (e) {
                resolve({
                  code: e.code,
                  message: e.message
                })
              }
            })
          )
        }
      }
      let ret = await Promise.all(resData)
      for (let e = 0; e < excelData.length; e++) {
        const config = excelData[e].config
        const i = excelData[e].index
        let excelres
        try {
          excelres = await ctx.service.datastorage.getData(config)
        } catch (error) {
          excelres = {
            code: error.code,
            message: error.message
          }
        }
        ret.splice(i, 0, excelres)
      }
      const returnValue = ret.map(item => {
        if (item.code == 801) {
          return getResponseBody(null, false, item.message, 400)
        }
        return getResponseBody(null, true, item.message)
      })
      if (!value || !enableCache) {
        ctx.body = returnValue
      }
      await ctx.service.cacheservice.set(key, returnValue, 60000)
    } else {
      if (value && enableCache) {
        ctx.body = getResponseBody(value)
        return
      }
      const config = Object.assign(queryParam, bodyParam, headerParam)
      const res = await ctx.service.datastorage.getData(config)
      if (!value || !enableCache) {
        ctx.body = getResponseBody(res)
      }
      await ctx.service.cacheservice.set(key, res, 60000)
    }
  }
  async getData() {
    const { ctx } = this
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = {
      systoken: ctx.header.systoken,
      sysuserid: ctx.header.sysuserid,
      env: ctx.header.env,
    }
    const key = md5(JSON.stringify(Object.assign(bodyParam, queryParam)))
    // ctx.logger.info('redis查询的key', key)
    const enableCache = ctx.request.header['enable-cache'] === 'true' || false
    let value = await ctx.service.cacheservice.get(key)
    // ctx.logger.info('redis获取的数据', value)
    const updateDataTTL = await ctx.service.cacheservice.getTTL(key)
    // this.ctx.logger.info('old -- updateDataTTL', updateDataTTL)
    // 合并接口对特殊处理
    if (Object.keys(bodyParam)[0] == 0) {
      if (value && enableCache) {
        this.ctx.logger.info('getData数据缓存')
        ctx.body = value //返回缓存的数据
        setImmediate(async ()=> {
          // 异步操作放这里
          // 请求接口更新缓存开始
        let resData = []
        let excelData = []
        // 设置一个flag为true，遍历的过程中出现有801，或者有空的情况，不设置缓存，否则更新缓存
        let allPassFlag = true
        for (let v = 0; v < bodyParam.length; v++) {
          const value = bodyParam[v]
          if (value.sourceType && value.sourceType === 'excel') {
            const config = Object.assign(value, queryParam, headerParam)
            excelData.push({
              config,
              index: v
            })
          } else {
            resData.push(
              new Promise(async (resolve, reject) => {
                try {
                  const config = Object.assign(value, queryParam, headerParam)
                  // ctx.logger.info('value.sourceType', value.sourceType, config.type)
                  let type = config.type ? config.type : value.sourceType
                  let res;
                  // if (type === 'dmc'){
                  //   const target = await ctx.service.datastorage.getData(config, key)
                  //   res = target.res
                  //   const flag = target.flag
                  //   // this.ctx.logger.info('service里获取到的 flag', flag)
                  //   if(!flag){
                  //     // 如果 flag 为false，将 allpassFlag取 false
                  //     allPassFlag = false
                  //   }
                  // } else {
                    // modify here
                    res = await ctx.service.datastorage.getData(config)
                  // }
                  resolve(res)
                } catch (e) {
                  resolve({
                    code: e.code,
                    message: e.message
                  })
                }
              })
            )
          }
        }
        const ret = await Promise.all(resData)
        //针对无法并发读取同一个excel表进行修改
        for (let e = 0; e < excelData.length; e++) {
          const config = excelData[e].config
          const i = excelData[e].index
          let excelres
          try {
            excelres = await ctx.service.datastorage.getData(config)
          } catch (error) {
            excelres = {
              code: error.code,
              message: error.message
            }
          }
          ret.splice(i, 0, excelres)
        }
        // ctx.logger.info('请求接口更新缓存后的res的长度', ret.length)
        const returnValue = ret.map(item => {
          if(item && item.length){
            // 如果说遍历的对象还是数组的话
            // 再次进行遍历
            item.forEach((temp)=>{
              if(temp.code == 801){
                allPassFlag = false
              }
              if(Object.keys(item).length == 0){
                allPassFlag = false
              }
            })
          }
          if (item && item.code == 801) {
            // 表示请求错误，不更新数据
            allPassFlag = false
            return null
          }
          if (item && Object.keys(item).length == 0) {
            allPassFlag = false
          }
          return getResponseBody(item)
        })
        
        // ctx.logger.info('allPassFlag', allPassFlag)
        if(allPassFlag){
          await ctx.service.cacheservice.set(key, returnValue, 60 * 60 * 24 * 7)
          const updateDataTTL = await ctx.service.cacheservice.getTTL(key)
          // this.ctx.logger.info('updateDataTTL', updateDataTTL)
        }
        const env = ctx.app.config.env
        const tasklist = await ctx.service.cacheservice.hget(
          `tasklist_${env}`,
          key
        )
        if (!tasklist) {
          await ctx.service.cacheservice.hset(`tasklist_${env}`, key, {
            list: Object.assign(bodyParam, queryParam),
            headerParam
          })
        }
        })
        return
      } else {
        this.ctx.logger.info('getData数据不缓存')
      let resData = []
      let excelData = []
      let allPassFlag = true
      for (let v = 0; v < bodyParam.length; v++) {
        const value = bodyParam[v]
        if (value.sourceType && value.sourceType === 'excel') {
          const config = Object.assign(value, queryParam, headerParam)
          excelData.push({
            config,
            index: v
          })
        } else {
          resData.push(
            new Promise(async (resolve, reject) => {
              try {
                const config = Object.assign(value, queryParam, headerParam)
                let type = config.type ? config.type : value.sourceType
                  let res;
                  // modify here
                  // if (type === 'dmc'){
                  //   const target = await ctx.service.datastorage.getData(config, key)
                  //   res = target.res
                  //   const flag = target.flag
                  //   if(!flag){
                  //     // 如果 flag 为false，将 allpassFlag取 false
                  //     allPassFlag = false
                  //   }
                  // } else {
                    res = await ctx.service.datastorage.getData(config)
                  // }
                resolve(res)
              } catch (e) {
                console.log('e',e);
                resolve({
                  code: e.code,
                  message: e.message
                })
              }
            })
          )
        }
      }
      let ret = await Promise.all(resData)
      //针对无法并发读取同一个excel表进行修改
      for (let e = 0; e < excelData.length; e++) {
        const config = excelData[e].config
        const i = excelData[e].index
        let excelres
        try {
          excelres = await ctx.service.datastorage.getData(config)
        } catch (error) {
          excelres = {
            code: error.code,
            message: error.message
          }
        }
        ret.splice(i, 0, excelres)
      }
      // ctx.logger.info('不缓存的 ret长度',ret.length);
      const returnValue = ret.map(item => {
        if(item && typeof item == 'Array' && item.length){
          // 如果说遍历的对象还是数组的话
          // 再次进行遍历
          item.forEach((temp)=>{
            if(temp.code == 801){
              allPassFlag = false
            }
            if(Object.keys(item).length == 0){
              allPassFlag = false
            }
          })
        }
        if (item && item.code == 801) {
          allPassFlag = false
          return getResponseBody(null, false, item.message, 400)
        }
        // item也可能是一个数组,比如一个大屏的组合请求
        // 现在定义的两个情况全部都是建立在 DMC 返回的数据是组件返回的情况，不是大屏返回的情况
        
        if (item && Object.keys(item).length == 0) {
          allPassFlag = false
        }
        return getResponseBody(item)
      })
      ctx.logger.info('allPassFlag', allPassFlag)
      if (allPassFlag) {
        await ctx.service.cacheservice.set(key, returnValue, 60 * 60 * 24 * 7)
        const updateDataTTL = await ctx.service.cacheservice.getTTL(key)
        // this.ctx.logger.info('updateDataTTL', updateDataTTL)
      }
      if (!value || !enableCache) {
        ctx.body = returnValue
      }
      return
      }
    } else {
      if (value && enableCache) {
        this.ctx.logger.info('getData数据缓存1')
        ctx.body = getResponseBody(value)
        return
      }
      this.ctx.logger.info('getData数据不缓存1')
      const config = Object.assign(queryParam, bodyParam, headerParam)
      const res = await ctx.service.datastorage.getData(config)
      if (!value || !enableCache) {
        ctx.body = getResponseBody(res)
      }
      await ctx.service.cacheservice.set(key, res, 60 * 60 * 24 * 7)
    }
  }
  async getFieldList() {
    const { ctx } = this
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = ctx.header
    if (Object.keys(bodyParam)[0] == 0) {
      let resData = []
      bodyParam.forEach(value => {
        resData.push(
          new Promise(async (resolve, reject) => {
            try {
              const config = Object.assign(value, queryParam, headerParam)
              const res = await ctx.service.datastorage.getFieldList(config)
              resolve(res)
            } catch (e) {
              resolve(e)
            }
          })
        )
      })

      const ret = await Promise.all(resData)
      const returnValue = ret.map(item => {
        return getResponseBody(item)
      })
      ctx.body = returnValue
    } else {
      const config = Object.assign(queryParam, bodyParam, headerParam)
      const res = await ctx.service.datastorage.getFieldList(config)
      ctx.body = getResponseBody(res)
    }
  }

  async getTreeList() {
    const { ctx } = this
    // to_do：不能放在这里拼参数，可能有其他类型的数据源要用的
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = ctx.header
    const config = Object.assign(queryParam, bodyParam, headerParam)
    const res = await ctx.service.datastorage.getTreeList(config)
    ctx.body = getResponseBody(res)
  }

  async getFolderTbList() {
    const { ctx } = this
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = ctx.header
    const config = Object.assign(queryParam, bodyParam, headerParam)
    const res = await ctx.service.datastorage.getFolderTbList(config)
    ctx.body = getResponseBody(res)
  }

  async searchTb() {
    const { ctx } = this
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = ctx.header
    const config = Object.assign(queryParam, bodyParam, headerParam)
    const res = await ctx.service.datastorage.searchTb(config)
    ctx.body = getResponseBody(res)
  }

  async preview() {
    const { ctx } = this
    const queryParam = ctx.query
    const headerParam = ctx.header
    const config = Object.assign(queryParam, headerParam)
    const res = await ctx.service.datastorage.preview(config)
    ctx.body = getResponseBody(res)
  }

  async getFunctionList() {
    const { ctx } = this
    const queryParam = ctx.query
    const headerParam = ctx.header
    const config = Object.assign(queryParam, headerParam)
    const res = await ctx.service.datastorage.getFunctionList(config)
    ctx.body = getResponseBody(res)
  }

  async clearCache() {
    const { ctx } = this
    const queryParam = ctx.request.body
    const res = await ctx.service.datastorage.clearCache(queryParam)
    ctx.body = getResponseBody(res)
  }

  async validateSql() {
    const { ctx } = this
    const queryParam = ctx.request.body
    console.log('queryParams####',queryParam);
    const res = await ctx.service.datastorage.validateSql(queryParam)
    ctx.body = getResponseBody(res)
  }
}

module.exports = DataStorageController
