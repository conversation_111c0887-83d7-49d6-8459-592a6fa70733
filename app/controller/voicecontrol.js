/*
 * @Description: 语音控制工呢哥
 * @Date: 2022-11-07 10:28:13
 * @Author: lzz
 * @LastEditors: lzz
 */
'use strict'
const _ = require('lodash')
const fs = require('fs-extra')
const path = require('path')
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
const levenshtein = require('levenshtein')
function getFileContentAsBase64(a) {
  try {
    return fs.readFileSync(a, { encoding: 'base64' })
  } catch (err) {
    throw new Error(err)
  }
}
function findMostSimilar(arr, target) {
  let minIndex = -1
  let arrlength = 100
  arr.map(function (item, index) {
    if (item.length < arrlength) {
      arrlength = item.length
    }
  })

  let minDistance = target.length - arrlength + 1.5
  if (target.length - arrlength < 0) {
    minDistance = arrlength - 1.5
  }
  for (let i = 0; i < arr.length; i++) {
    const distance = levenshtein(arr[i], target)
    if (distance < minDistance) {
      minDistance = distance
      minIndex = i
    }
  }
  return minIndex
}
function getWords(trigger, eventsList) {
  if (trigger == 'compHover') {
    return eventsList.focus
  } else if (trigger == 'compMouseleave') {
    return eventsList.defocus
  } else if (trigger == 'close') {
    return []
  } else {
    return eventsList.open
  }
}

class Voicecontrol extends Controller {
  async index() {
    const { ctx, config } = this
    const body = ctx.request.body
    const speech = body.speech
    const speech2ie = config.ai.speech2ie || 'http://127.0.0.1:18213/speech2ie'
    // const speech2ie = 'http://*************:8018/speech/'
    const text2ie = config.ai.text2ie || 'http://127.0.0.1:18213/text2ie'
    // const text2ie = 'http://127.0.0.1:18213/text2ie'
    const screenId = body.screenId
    const type = body.type
    const len = body.len
    const AK = 'Zu76TKi5CDk6Yl7Vq6EecNfz'
    const SK = 'l2GpR2uE2Lw7qj5pkqmaCT1i2KAlP633'
    const bdapi =
      'https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=' +
      AK +
      '&client_secret=' +
      SK
    // const AccessToken = await ctx.curl(bdapi, {
    //   method: 'POST',
    //   data: {},
    //   rejectUnauthorized: false,
    //   dataType: 'json'
    // })
    // const vopapi = 'https://vop.baidu.com/server_api'
    // const vopData = await ctx.curl(vopapi, {
    //   method: 'POST',
    //   data: JSON.stringify({
    //     format: 'pcm',
    //     rate: 16000,
    //     channel: 1,
    //     cuid: 'f0:18:98:0d:73:31',
    //     speech,
    //     len,
    //     token: AccessToken.data.access_token
    //   }),
    //   rejectUnauthorized: false,
    //   dataType: 'json'
    // })
    // // const str = '关闭弹窗'
    // const str = vopData.data.result[0]
    const voicecontrolData = await ctx.service.voicecontrol.find({ screenId })
    let aiValueList = []
    let str = ''
    let entity = {}
    for (let v = 0; v < voicecontrolData.length; v++) {
      const element = voicecontrolData[v]
      for (let ve = 0; ve < element.eventsConfig.length; ve++) {
        const vedata = element.eventsConfig[ve]
        if (vedata.type == 'ai') {
          aiValueList = aiValueList.concat(vedata.aiValueList)
        }
      }
    }
    if (type == 'text' && aiValueList.length == 0) {
      str = speech
    } else {
      let hzvopData
      try {
        if (type == 'text') {
          hzvopData = await ctx.curl(text2ie, {
            method: 'POST',
            dataType: 'json',
            data: {
              text: speech,
              schema:
                aiValueList.length == 0
                  ? JSON.stringify([])
                  : JSON.stringify(aiValueList)
            },
            timeout: 10000,
            rejectUnauthorized: false
          })
        } else {
          // hzvopData = await ctx.curl(speech2ie, {
          //   method: 'POST',
          //   dataType: 'json',
          //   data: {
          //     audio_base64: speech,
          //     schema:
          //       aiValueList.length == 0
          //         ? JSON.stringify([])
          //         : JSON.stringify(aiValueList)
          //   },
          //   timeout: 10000,
          //   rejectUnauthorized: false
          // })
          hzvopData = await ctx.curl(speech2ie, {
            method: 'POST',
            dataType: 'json',
            data: {
              speech: speech,
              model: 'asr',
              cr: true
            },
            timeout: 10000,
            rejectUnauthorized: false
          })
        }
      } catch (error) {
        ctx.body = getResponseBody(error, false, '语音识别服务未启动', 400)
        return
      }
      this.ctx.logger.info('语音识别接口返回参数', hzvopData)
      if (
        hzvopData &&
        hzvopData.data &&
        hzvopData.data.result &&
        hzvopData.data.result.text
      ) {
        str = hzvopData.data.result.text || ''
        entity = hzvopData.data.result.entity || {}
      } else {
        ctx.body = getResponseBody(
          hzvopData,
          false,
          '语音识别服务接口数据错误',
          400
        )
        return
      }
    }
    str = str
      .replace(/\s+/g, '')
      .replace(/[.,，。：；‘“、】【·？?/#!$%^&*;:{}=\-_`~()]/g, '')
    if (str) {
      let eventCollection = []
      if (str.indexOf('退出操控') !== -1) {
        eventCollection.push({
          comId: '',
          events: 'exit',
          alias: '',
          params: {},
          eventWords: ''
        })
        ctx.body = getResponseBody({ str, eventCollection })
        return
      }
    }
    const closeWords = config.eventList.closeWords
    let closeflag = ''
    for (let i = 0; i < closeWords.length; i++) {
      const eventWords = closeWords[i]
      if (str.indexOf(eventWords) !== -1) {
        closeflag = eventWords
      }
    }
    if (closeflag) {
      let cominfo = await ctx.service.component.find({
        screenId,
        comType: 'interaction-container-carousepanel'
      })
      if (cominfo && cominfo.length > 0) {
        const eventCollection = []
        cominfo.map(item => {
          eventCollection.push({
            comId: item.id,
            events: 'close',
            alias: item.alias,
            params: {},
            eventWords: closeflag
          })
        })
        ctx.body = getResponseBody({ str, eventCollection })
        return
      }
    }

    if (str.indexOf('刷新') !== -1) {
      let cominfo = await ctx.service.component.find({
        screenId
      })
      let screenIds = []
      for (let c = 0; c < cominfo.length; c++) {
        const element = cominfo[c]
        if (element.config.screens && element.config.screens.length) {
          screenIds.push(...element.config.screens.map(item => item.id))
        }
      }
      let screensComs = await ctx.service.component.find({
        screenId: { $in: screenIds }
      })
      cominfo = cominfo.concat(screensComs)
      const eventCollection = []
      for (let v = 0; v < cominfo.length; v++) {
        const com = cominfo[v]
        let voicecontrolinfo = await ctx.service.voicecontrol.findOne({
          comId: com.id
        })
        let words = []
        if (voicecontrolinfo) {
          words = voicecontrolinfo.aliasList
          words.push(voicecontrolinfo.comName, voicecontrolinfo.alias)
          words = _.uniq(words)
          for (let w = 0; w < words.length; w++) {
            const element = words[w]
            if (str.indexOf(element) !== -1) {
              eventCollection.push({
                comId: com.id,
                events: 'refresh',
                alias: com.alias,
                params: {},
                eventWords: '刷新'
              })
            }
          }
        } else {
          words.push(com.orginName, voicecontrolinfo.alias)
          words = _.uniq(words)
          for (let w = 0; w < words.length; w++) {
            const element = words[w]
            if (str.indexOf(element) !== -1) {
              eventCollection.push({
                comId: com.id,
                events: 'refresh',
                alias: com.alias,
                params: {},
                eventWords: '刷新'
              })
            }
          }
        }
      }
      ctx.body = getResponseBody({ str, eventCollection })
      return
    }
    let eventCollection = [],
      comEventList = []
    for (let v = 0; v < voicecontrolData.length; v++) {
      const item = voicecontrolData[v]
      for (let e = 0; e < item.eventsConfig.length; e++) {
        let element = item.eventsConfig[e].eventWords
        for (let i = 0; i < element.length; i++) {
          const eventWords = element[i]
          let nameList = item.aliasList
          nameList.push(item.comName, item.alias)
          nameList = _.uniq(nameList)
          if (str.indexOf(eventWords) !== -1) {
            comEventList.push({
              eventWords,
              comId: item.comId,
              aliasList: nameList,
              eventsConfig: item.eventsConfig[e]
            })
          }
          let len = comEventList.filter(it => {
            return it.comId == item.comId
          })
          if (i === element.length - 1 && len.length == 0) {
            comEventList.push({
              eventWords: '查看',
              comId: item.comId,
              aliasList: nameList,
              eventsConfig: item.eventsConfig[e]
            })
          }
        }
      }
    }
    for (let index = 0; index < comEventList.length; index++) {
      const comEvent = comEventList[index]
      comEvent.eventsConfig.valueList.push(comEvent.eventsConfig.field)
      if (
        comEvent.eventsConfig.type == 'ai' &&
        comEvent.eventsConfig.isComponentWord &&
        comEvent.eventsConfig.aiValueList.length
      ) {
        const aiValueList = comEvent.eventsConfig.aiValueList
        for (let a = 0; a < aiValueList.length; a++) {
          const element = aiValueList[a]
          if (entity[element] && entity[element].length > 0) {
            eventCollection.push({
              comId: comEvent.comId,
              events: comEvent.eventsConfig.relatedEvents,
              params: { [comEvent.eventsConfig.params]: entity[element][0] },
              eventWords: comEvent.eventWords
            })
          }
        }
      }
      if (
        comEvent.eventsConfig.type == 'datasource' &&
        comEvent.eventsConfig.isComponentWord &&
        comEvent.eventsConfig.valueList.length
      ) {
        let min = -1
        const valueList = comEvent.eventsConfig.valueList
        if (comEvent.eventsConfig.valueList.length > 0) {
          min = findMostSimilar(comEvent.eventsConfig.valueList, str)
          console.log(comEvent.eventsConfig.valueList, 'dsadas')
        }
        for (let v = 0; v < valueList.length; v++) {
          const dataValue = valueList[v]
          if (min === v) {
            eventCollection.push({
              comId: comEvent.comId,
              events: comEvent.eventsConfig.relatedEvents,
              params: { [comEvent.eventsConfig.params]: dataValue },
              eventWords: comEvent.eventWords
            })
          }
        }
      }
      if (!comEvent.eventsConfig.isComponentWord) {
        let minIndex = -1
        if (comEvent.aliasList.length > 0) {
          minIndex = findMostSimilar(comEvent.aliasList, str)
        }
        for (let i = 0; i < comEvent.aliasList.length; i++) {
          const alias = comEvent.aliasList[i]
          if (minIndex === i) {
            if (
              comEvent.eventsConfig.type == 'ai' &&
              comEvent.eventsConfig.aiValueList.length
            ) {
              const aiValueList = comEvent.eventsConfig.aiValueList
              for (let a = 0; a < aiValueList.length; a++) {
                const element = aiValueList[a]
                if (entity[element] && entity[element].length > 0) {
                  eventCollection.push({
                    comId: comEvent.comId,
                    events: comEvent.eventsConfig.relatedEvents,
                    params: {
                      [comEvent.eventsConfig.params]: entity[element][0]
                    },
                    eventWords: comEvent.eventWords
                  })
                }
              }
            } else if (
              comEvent.eventsConfig.type == 'datasource' &&
              comEvent.eventsConfig.valueList.length
            ) {
              const valueList = comEvent.eventsConfig.valueList
              for (let v = 0; v < valueList.length; v++) {
                const dataValue = valueList[v]
                if (str.indexOf(dataValue) !== -1) {
                  eventCollection.push({
                    comId: comEvent.comId,
                    events: comEvent.eventsConfig.relatedEvents,
                    alias,
                    params: { [comEvent.eventsConfig.params]: dataValue },
                    eventWords: comEvent.eventWords
                  })
                }
              }
            } else {
              eventCollection.push({
                comId: comEvent.comId,
                events: comEvent.eventsConfig.relatedEvents,
                params: { [comEvent.eventsConfig.params]: '' },
                alias,
                eventWords: comEvent.eventWords
              })
            }
          }
        }
      }
    }
    if (eventCollection.length === 0) {
      ctx.body = getResponseBody(
        str,
        true,
        '请输入准确的事件名称或组件名称',
        202
      )
    } else {
      ctx.body = getResponseBody({ str, eventCollection })
    }
  }
  async create() {
    const { ctx } = this
    const body = ctx.request.body
    const data = await ctx.service.voicecontrol.create(body)
    ctx.body = getResponseBody(data)
  }
  async update() {
    const { ctx } = this
    const body = ctx.request.body
    const comId = ctx.query.comId
    const data = await ctx.service.voicecontrol.update({ comId }, body)
    ctx.body = getResponseBody(data)
  }
  async createInitControl() {
    const { ctx, config } = this
    let eventList = config.eventList
    const screenId = ctx.query.screenId
    let cominfo = await ctx.service.component.find({ screenId })
    let screensComsList = []
    for (let c = 0; c < cominfo.length; c++) {
      let screenIds = []
      const element = cominfo[c]
      if (element.config.screens && element.config.screens.length) {
        screenIds.push(...element.config.screens.map(item => item.id))
      }
      let screensComs = await ctx.service.component.find({
        screenId: { $in: screenIds }
      })
      for (let sc = 0; sc < screensComs.length; sc++) {
        const comobj = {
          actions: screensComs[sc].actions,
          alias: screensComs[sc].alias,
          comName: screensComs[sc].comName,
          dataConfig: screensComs[sc].dataConfig,
          events: screensComs[sc].events,
          id: screensComs[sc].id,
          screenId: screensComs[sc].screenId,
          type: screensComs[sc].type,
          parentComName: element.alias,
          orginName: element.orginName,
          interactionConfig: screensComs[sc].interactionConfig
        }
        screensComsList.push(comobj)
      }
    }
    cominfo = cominfo.concat(screensComsList)

    const voiceDataList = await ctx.service.voicecontrol.find({ screenId })
    for (let v = 0; v < voiceDataList.length; v++) {
      const voiceData = voiceDataList[v]
      const comdata = await ctx.service.component.findOne({
        id: voiceData.comId
      })
      if (!comdata) {
        await ctx.service.voicecontrol.deleteOne({ comId: voiceData.comId })
      }
      if (
        comdata.interactionConfig.events.length < 1 &&
        comdata.interactionConfig.linkAge.length < 1
      ) {
        await ctx.service.voicecontrol.deleteOne({ comId: voiceData.comId })
      }
    }
    const comlist = cominfo.filter(item => {
      return (
        item.interactionConfig.events.length > 0 ||
        item.interactionConfig.linkAge.length > 0
      )
    })
    for (let index = 0; index < comlist.length; index++) {
      let data = await ctx.service.voicecontrol.findOne({
        comId: comlist[index].id
      })
      const comdata = comlist[index]
      const screenId = comdata.screenId
      const alias = comdata.alias
      const comName = comdata.orginName
      let eventsList = comdata.interactionConfig.events
      const linkAgeList = comdata.interactionConfig.linkAge
      eventsList = eventsList.concat(linkAgeList)
      const events = _.uniqBy(eventsList, 'trigger')
      if (!data) {
        const eventsConfig = []
        for (let index = 0; index < events.length; index++) {
          const eventselement = events[index]
          eventsConfig.push({
            relatedEvents: eventselement.trigger, // 关联事件
            eventWords: getWords(eventselement.trigger, eventList) || [], // 事件词
            type: 'datasource', // 词源类型
            field: '', // 数据源字段
            valueList: [], // 数据源的值
            aiValueList: [],
            params: '', //事件参数
            isComponentWord: false //是否绑定为组件词
          })
        }
        data = await ctx.service.voicecontrol.create({
          comId: comdata.id,
          screenId,
          eventsConfig,
          comName,
          alias
        })
      } else {
        let eventsConfigs = data.eventsConfig
        let eventsConfigList = []
        for (let index = 0; index < events.length; index++) {
          const eventselement = events[index]
          const flag = _.findIndex(eventsConfigs, function (o) {
            return o.relatedEvents == eventselement.trigger
          })
          if (flag === -1) {
            eventsConfigList.push({
              relatedEvents: eventselement.trigger, // 关联事件
              eventWords: getWords(eventselement.trigger, eventList) || [], // 事件词
              type: 'datasource', // 词源类型
              field: '', // 数据源字段
              valueList: [], // 数据源的值
              aiValueList: [],
              params: '', //事件参数
              isComponentWord: false //是否绑定为组件词
            })
          } else {
            if (eventsConfigs[flag].type == 'datasource') {
              eventsConfigs[flag].aiValueList = []
            } else {
              eventsConfigs[flag].valueList = []
            }
            eventsConfigList.push(eventsConfigs[flag])
          }
        }
        await ctx.service.voicecontrol.update(
          {
            comId: comdata.id
          },
          { eventsConfig: eventsConfigList, comName, alias }
        )
      }
    }

    ctx.body = getResponseBody(comlist)
  }

  async find() {
    const { ctx, config } = this
    let eventList = config.eventList
    const comId = ctx.query.comId
    let data = await ctx.service.voicecontrol.findOne({ comId })
    if (!data) {
      const comdata = await ctx.service.component.findOne({ id: comId })
      const screenId = comdata.screenId
      const alias = comdata.alias
      const comName = comdata.comName
      let eventsList = comdata.interactionConfig.events
      const linkAgeList = comdata.interactionConfig.linkAge
      eventsList = eventsList.concat(linkAgeList)
      const events = _.uniqBy(eventsList, 'trigger')
      const eventsConfig = []
      for (let index = 0; index < events.length; index++) {
        const eventselement = events[index]
        eventsConfig.push({
          relatedEvents: eventselement.trigger, // 关联事件
          eventWords: getWords(eventselement.trigger, eventList) || [], // 事件词
          type: 'datasource', // 词源类型
          field: '', // 数据源字段
          valueList: [], // 数据源的值
          aiValueList: [],
          params: '', //事件参数
          isComponentWord: false //是否绑定为组件词
        })
      }

      data = await ctx.service.voicecontrol.create({
        comId,
        screenId,
        eventsConfig,
        alias,
        comName
      })
    }
    ctx.body = getResponseBody(data)
  }

  async comlist() {
    const { ctx } = this
    const screenId = ctx.query.screenId
    const data = await ctx.service.component.find({ screenId })
    const comlist = data.filter(item => {
      return item.interactionConfig.events.length > 0
    })
    ctx.body = getResponseBody(comlist)
  }

  async voiceconfig() {
    const { ctx, config } = this
    let eventList = config.eventList
    ctx.body = getResponseBody(eventList)
  }
  async speech() {
    const { ctx, config } = this
    const body = ctx.request.body
    const speech = body.speech
    const speech2ie = config.ai.speech2ie || 'http://127.0.0.1:18213/speech2ie'
    let hzvopData
    hzvopData = await ctx.curl(speech2ie, {
      method: 'POST',
      dataType: 'json',
      data: {
        audio_base64: speech,
        schema: JSON.stringify([])
      },
      timeout: 10000,
      rejectUnauthorized: false
    })
    const str = hzvopData.data.result.content || ''
    ctx.body = getResponseBody(str)
  }
}

module.exports = Voicecontrol
