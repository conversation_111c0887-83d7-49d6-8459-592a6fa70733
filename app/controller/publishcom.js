'use strict'
const path = require('path')
let fs = require('fs-extra')
const base64 = require('base-64')
const { spawnSync, exec, execSync } = require('child_process')
const compressing = require('compressing')
const _ = require('lodash')
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
class PublishcomController extends Controller {
  async publish() {
    const { ctx } = this
    const seatomTeamPath = path.join(
      '/data/application/service/jenkins_work/workspace/seatom-team'
      // '/Users/<USER>/Programs/submodules'
    )
    const requireFiles = ['node_modules', 'index.js']
    try {
      process.chdir(seatomTeamPath)
      // await new Promise(resolve => {
      //   exec(
      //     'git submodule foreach git pull origin master && seatom install-all',
      //     () => {
      //       resolve()
      //     }
      //   )
      // })
    } catch (error) {
      ctx.body = getResponseBody(
        `执行命令时出错: ${error}`,
        false,
        '发布失败',
        400
      )
      return false
    }
    // 递归遍历文件夹
    function traverseDirectory(currentPath) {
      return new Promise(async resolve => {
        const files = fs.readdirSync(currentPath)
        for (let i = 0; i < files.length; i++) {
          const file = files[i]
          const filePath = path.join(currentPath, file)
          if (fs.statSync(filePath).isDirectory()) {
            const flag = requireFiles.every(item => {
              const requirePath = path.join(filePath, item)
              return fs.existsSync(requirePath)
            })
            if (flag) {
              // 进入组件目录
              process.chdir(filePath)
              const comJson = fs.readFileSync(
                filePath + '/package.json',
                'utf-8'
              )
              const comName = JSON.parse(comJson).name
              if (!comName) {
                return false
              }
              const com = await ctx.service.package.findLatestVersion({
                name: comName
              })
              if (!com) return false
              const updatedAt = new Date(com.updatedAt).getTime()
              const stdout = await new Promise(resolve => {
                exec('git log -1 --format="%cd"', (error, stdout, stderr) => {
                  resolve(stdout)
                })
              })
              if (updatedAt < new Date(stdout).getTime()) {
                // 更新时间早于代码提交时间，需要重新发布
                console.log('=======>', comName)
                await new Promise(resolve => {
                  exec('seatom publish', (error, stdout) => {
                    console.log(stdout)
                    resolve()
                  })
                })
                ctx.body = getResponseBody(`${comName}发布成功`, false, '', 200)
              }
            } else {
              await traverseDirectory(filePath)
            }
          }
        }
        resolve()
      })
    }
    await traverseDirectory(seatomTeamPath)
    ctx.body = getResponseBody(
      {
        finish: true
      },
      false,
      '发布结束',
      200
    )
  }
}

module.exports = PublishcomController
