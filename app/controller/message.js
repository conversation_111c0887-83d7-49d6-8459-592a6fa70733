/*
 * @Description: 消息处理
 * @Date: 2022-11-07 10:28:13
 * @Author: chen<PERSON><PERSON>
 * @LastEditors: chenxingyu
 */
'use strict'
// 分享消息通知
const Controller = require('egg').Controller
const { getResponseSuccess, getResponseError } = require('../extend/utils')

/**
 * @Controller 消息
 */
class MessageController extends Controller {
  /**
   * @Summary 查询用户消息
   * @Description 查询用户消息
   * @Router POST /message
   * @Request body messageListBody *body 可以不传值，不传值则查找所有消息
   * @Response 200 messageListSuccessResponse ok
   * @Response 400 errorResponse error
   */
  async index() {
    const { ctx } = this
    const body = ctx.request.body
    console.log('body',body);
    // const data = await ctx.service.user.getDmcRoleUserList(
    //   'ro_2f7b685f8d92481babd6d3a736eb2f5c'
    // )
    // ctx.body = data
    // return
    if(body.userId){
      try {
        let data = await ctx.service.message.find(body)
  
        const userIds = []
        const screeenIds = []
        const screenCoeditMsgs = data.filter(item => {
          return item.type === 'screenCoedit'
        })
        screenCoeditMsgs.forEach(item => {
          if (!userIds.includes(item.content.screenCoedit.createUserId)) {
            userIds.push(item.content.screenCoedit.createUserId)
          }
          if (!userIds.includes(item.content.screenCoedit.coeditUserId)) {
            userIds.push(item.content.screenCoedit.coeditUserId)
          }
          if (!screeenIds.includes(item.content.screenCoedit.coeditScreenId)) {
            screeenIds.push(item.content.screenCoedit.coeditScreenId)
          }
        })
  
        // 找到用户列表
        let userList = []
        if (userIds.length) {
          userList = await ctx.service.user.find(
            { userId: { $in: userIds } },
            {
              token: 0,
              createdAt: 0,
              updatedAt: 0
            }
          )
        }
  
        // 找到大屏列表
        let screenList = []
        if (screeenIds.length) {
          screenList = await ctx.service.screen.find({
            id: { $in: screeenIds }
          })
        }
  
        // 获取用户名
        const getUserNameByUserId = userId => {
          const user =
            userList.find(user => {
              return user.userId === userId
            }) || {}
          return user.userName || ''
        }
  
        // 获取大屏名
        const getScreenNameByScreenId = screenId => {
          const screen =
            screenList.find(screen => {
              return screen.id === screenId
            }) || {}
          return screen.name || ''
        }
  
        data = data.map(item => {
          item = item.toObject()
          if (item.type === 'common') {
            return {
              ...item,
              content: {
                common: item.content.common
              }
            }
          }
          if (item.type === 'usershare') {
            return {
              ...item,
              content: {
                usershare: item.content.usershare
              }
            }
          }
          // 查找用户名称
          if (item.type === 'screenCoedit') {
            item.content.screenCoedit.createUserName = getUserNameByUserId(
              item.content.screenCoedit.createUserId
            )
            item.content.screenCoedit.coeditUserName = getUserNameByUserId(
              item.content.screenCoedit.coeditUserId
            )
            item.content.screenCoedit.coeditScreenName = getScreenNameByScreenId(
              item.content.screenCoedit.coeditScreenId
            )
            return {
              ...item,
              content: {
                screenCoedit: item.content.screenCoedit
              }
            }
          }
  
          return item
        })
  
        ctx.body = getResponseSuccess({ data })
      } catch (error) {
        this.ctx.logger.info(error)
        ctx.body = getResponseError({ data: error.toString() })
      }
    } else {
      ctx.body = getResponseError({ data: '未传入userId！' })
    }
    
  }
  /**
   * @Summary 创建信息
   * @Description 创建信息
   * @Router POST /message/create
   * @Request body array[MessageModel] *body
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async create() {
    const { ctx } = this
    const body = ctx.request.body
    try {
      body.map(async item => {
        await ctx.service.message.create(item)
      })
      ctx.body = getResponseSuccess({ data: null })
    } catch (error) {
      this.ctx.logger.info(error)
      ctx.body = getResponseError({ data: error.toString() })
    }
  }
  /**
   * @Summary 更新消息
   * @Description 更新消息
   * @Router POST /message/update
   * @Request query number id 消息ID
   * @Request body MessageModel *body
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async update() {
    const { ctx } = this
    const id = ctx.query.id
    const body = ctx.request.body
    try {
      const data = await ctx.service.message.update({ id }, body)
      ctx.body = getResponseSuccess({ data })
    } catch (error) {
      this.ctx.logger.info(error)
      ctx.body = getResponseError({ data: error.toString() })
    }
  }

  /**
   * @Summary 删除消息
   * @Description 删除消息
   * @Router POST /message/delete
   * @Request body idBody *body
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async delete() {
    const { ctx } = this
    const { id } = ctx.request.body

    try {
      const data = await ctx.service.message.delete({ id })
      ctx.body = getResponseSuccess({ data })
    } catch (error) {
      this.ctx.logger.info(error)
      ctx.body = getResponseError({ data: error.toString() })
    }
  }
  /**
   * @Summary 读取消息
   * @Description 读取消息
   * @Router POST /message/readMsg
   * @Request body idBody *body
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async readMsg() {
    const { ctx } = this
    const { id } = ctx.request.body

    try {
      const data = await ctx.service.message.update(
        { id },
        {
          isRead: true
        }
      )
      ctx.body = getResponseSuccess({ data })
    } catch (error) {
      this.ctx.logger.info(error)
      ctx.body = getResponseError({ data: error.toString() })
    }
  }
}

module.exports = MessageController
