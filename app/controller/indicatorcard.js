'use strict'

const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
const { encryptPath } = require('../extend/crypto')
const _ = require('lodash')
class IndicatorCardController extends Controller {
  async com() {
    const { ctx } = this
    const body = ctx.query ? ctx.query : {}
    const indicatorCard = await ctx.service.indicatorcard.findOne({
      componentId: body.id
    })
    const data = await ctx.service.component.findOne(body)
    if (indicatorCard) {
      data.alias = indicatorCard.name
    }
    ctx.body = getResponseBody(data)
  }
  async index() {
    const { ctx } = this
    const body = ctx.query ? ctx.query : {}
    const screenInfo = await ctx.service.screen.findOne({ id: 1 })
    if (!screenInfo) {
      const params = {
        id: 1,
        projectId: 0,
        workspaceId: 0,
        name: '指标卡模版大屏',
        level: 0,
        type: 'pc',
        templateId: 1,
        screenType: 'common',
        isScreentpl: true
      }
      await ctx.service.screen.create(params)
    }
    const indicatorCard = await ctx.service.indicatorcard.find(body)
    ctx.body = getResponseBody(indicatorCard)
  }

  async create() {
    const { ctx } = this
    const body = ctx.request.body
    const workspaceId = ctx.query.workspaceId
    const indicatorCard = await ctx.service.indicatorcard.findOne({
      name: body.name
    })
    if (indicatorCard) {
      ctx.body = getResponseBody(null, false, '名称已存在', 400)
      return
    }
    if (body.componentId) {
      try {
        const data = await ctx.service.indicatorcard.create(body)
        const projectData = await ctx.service.project.findOne({ type: 0 })
        const params = {
          name: '子组件编辑指标卡',
          projectId: projectData.id,
          workspaceId: workspaceId,
          level: 0,
          type: 'pc',
          templateId: 1,
          config: {
            height: 400,
            width: 500,
            backgroundImage: ''
          },
          isDynamicScreen: true,
          relationCompId: body.componentId,
          parentId: 1,
          screenType: 'common'
        }
        const screenInfo = await ctx.service.screen.create(params)
        await ctx.service.component.update(
          { id: body.componentId },
          {
            'config.screens': [{ id: screenInfo.id }],
            'dataConfig.dataResponse.source.api.data.func':
              '// 可在此处生成自定义的动态js参数\n// 必须返回对象形式数据，即 return {...}\n// 动态参数可在header/body/参数里通过 "${_var.xxx}" 引用\n\nreturn {}'
          }
        )
        ctx.body = getResponseBody(data)
      } catch (error) {
        ctx.body = getResponseBody(error, false, '参数错误', 400)
      }
    } else {
      ctx.body = getResponseBody(null, false, '参数错误', 400)
    }
  }

  async delete() {
    const { ctx } = this
    const ids = ctx.request.body.ids
    const componentIds = ctx.request.body.componentIds
    await ctx.service.indicatorcard.delete({ id: { $in: ids } })
    const res = await ctx.service.component.deleteMany({
      screenId: 1,
      id: { $in: componentIds }
    })
    ctx.body = getResponseBody(res)
  }

  async update() {
    const { ctx } = this
    const body = ctx.request.body
    if (body.name) {
      const indicatorCard = await ctx.service.indicatorcard.findOne({
        name: body.name
      })
      if (indicatorCard) {
        ctx.body = getResponseBody(null, false, '名称已存在', 400)
        return
      }
    }
    const data = await ctx.service.indicatorcard.update(
      { id: ctx.query.id },
      body
    )
    ctx.body = getResponseBody(data)
  }
}

module.exports = IndicatorCardController
