'use strict';

const Controller = require('egg').Controller;
const { getResponseBody } = require('../extend/utils');

class ApiLogController extends Controller {
  /**
   * 获取API调用日志列表
   */
  async list() {
    const { ctx } = this;
    const { page = 1, page_size = 10, s_time, e_time, userName, screenName } = ctx.query;
    
    const filter = {};
    // 添加时间范围过滤
    if (s_time || e_time) {
      filter.requestTime = {};
      if (s_time) {
        filter.requestTime.$gte = new Date(s_time);
      }
      if (e_time) {
        filter.requestTime.$lte = new Date(e_time);
      }
    }
    // 添加用户名过滤
    if (userName) {
      filter.userName = { $regex: userName, $options: 'i' };
    }
    // 添加大屏名称过滤
    if (screenName) {
      filter.screenName = { $regex: screenName, $options: 'i' };
    }
    // 查询总数
    const total = await ctx.service.apiLog.count(filter);
    // 查询列表
    const options = {
      skip: (page - 1) * page_size,
      limit: parseInt(page_size)
    };
    
    const list = await ctx.service.apiLog.find(filter, {}, options);
    
    ctx.body = getResponseBody({
      list,
      pagination: {
        total,
        page: parseInt(page),
        pageSize: parseInt(page_size)
      }
    });
  }
}

module.exports = ApiLogController; 