'use strict'

const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
class WorkSpaceController extends Controller {
  async index() {
    const { ctx } = this
    let condition = ctx.query || {}
    const data = await ctx.service.workspace.find(condition)
    ctx.body = getResponseBody(data)
  }

  async create() {
    const { ctx } = this
    const body = ctx.request.body
    if (body.type === 0) {
      const defaultData = await ctx.service.workspace.find({ type: 0 })
      if (defaultData && defaultData.length) {
        ctx.body = getResponseBody(null, false, '默认工作空间已存在')
        return
      }
    }
    const data = await ctx.service.workspace.create(body)
    ctx.body = getResponseBody(data)
  }

  async delete() {
    const { ctx } = this
    const id = ctx.query.id
    const data = await ctx.service.workspace.delete({ id })
    ctx.body = getResponseBody(data)
  }

  async update() {
    const { ctx } = this
    const data = await ctx.service.workspace.update(
      { id: ctx.query.id },
      ctx.request.body
    )
    ctx.body = getResponseBody(data)
  }
}

module.exports = WorkSpaceController
