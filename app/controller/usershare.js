/*
 * @Description: 复制大屏给用户
 * @Date: 2022-11-07 10:28:13
 * @Author: chen<PERSON><PERSON>
 * @LastEditors: chenxingyu
 */
'use strict'
// 用户分享
const _ = require('lodash')
const fs = require('fs-extra')
const path = require('path')
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')

class UsershareController extends Controller {
  // 查询用户分享信息
  async index() {
    const { ctx } = this
    const body = ctx.request.body
    const data = await ctx.service.usershare.find(body)
    ctx.body = getResponseBody(data)
  }

  /**
   * @Summary 通过shareIds获取用户同步列表
   * @Description 通过shareIds获取用户同步列表
   * @Router get /usershare/getListByShareIds
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async getListByShareIds() {
    const { ctx } = this
    const shareIds = ctx.request.body.shareIds || []

    const data = await ctx.service.usershare.find({
      id: { $in: shareIds }
    })
    ctx.body = getResponseBody(data)
  }

  /**
   * @Summary 用户同步列表
   * @Description 用户同步列表
   * @Router get /usershare/list
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async list() {
    const { ctx } = this
    const userId = ctx.header.sysuserid

    const data = await ctx.service.usershare.find({
      shareUserId: userId
    })
    ctx.body = getResponseBody(data)
  }

  // 获取用户列表
  async userlist() {
    const { ctx } = this
    // const data = await ctx.service.user.getDmcUserList()
    // ctx.body = getResponseBody(data.data)
    const data = await ctx.service.user.find()
    ctx.body = getResponseBody(data)
  }
  async rolelist() {
    const { ctx } = this
    const data = await ctx.service.user.getDmcRoleList()
    ctx.body = getResponseBody(data.data)
  }
  async grouplist() {
    const { ctx } = this
    const data = await ctx.service.user.getDmcGroupList()
    ctx.body = getResponseBody(data.data)
  }

  async getDmcGroupSublistList() {
    const { ctx } = this
    const group_id = ctx.query.group_id
    const data = await ctx.service.user.getDmcGroupSublistList(group_id)
    ctx.body = getResponseBody(data)
  }

  // 创建用户分享信息
  async create() {
    const { ctx } = this
    const body = ctx.request.body
    const messageList = []

    let userlist = body.userList || []
    const rolelist = body.roleList || []
    const grouplist = body.groupList || []
    console.log(body, '参数的情况')
    for (let g = 0; g < grouplist.length; g++) {
      const group = grouplist[g]
      const data = await ctx.service.user.getDmcGroupUserList(group.group_id)
      let groupUserList = data.map(item => {
        return {
          screenId: group.screenId,
          screenName: group.screenName,
          userId: group.userId,
          userName: group.userName,
          shareUserId: item.user_id
        }
      })
      console.log(data, '组的用户情况')
      userlist = userlist.concat(groupUserList)
    }

    for (let r = 0; r < rolelist.length; r++) {
      const role = rolelist[r]
      const data = await ctx.service.user.getDmcRoleUserList(role.role_id)
      let roleUserList = data.map(item => {
        return {
          screenId: role.screenId,
          screenName: role.screenName,
          userId: role.userId,
          userName: role.userName,
          shareUserId: item.user_id
        }
      })
      userlist = userlist.concat(roleUserList)
    }
    userlist = _.uniqBy(userlist, 'shareUserId')
    const usershareList = await ctx.service.usershare.insertMany(userlist)

    for (let index = 0; index < usershareList.length; index++) {
      const usershareData = usershareList[index]
      messageList.push({
        type: 'usershare',
        userId: usershareData.shareUserId,
        content: {
          usershare: {
            usershareId: usershareData.id
          }
        }
      })
    }

    await ctx.service.message.insertMany(messageList)

    const data = await ctx.service.usershare.find({
      userId: ctx.header.sysuserid
    })

    ctx.body = getResponseBody(userlist)
  }
  // 更新分享消息
  async update() {
    const { ctx } = this
    const id = ctx.query.id
    const body = ctx.request.body
    const data = await ctx.service.usershare.update({ id }, body)
    ctx.body = getResponseBody(data)
  }
  async delete() {
    const { ctx } = this
    const { id } = ctx.request.body
    const data = await ctx.service.usershare.delete({ id })
    ctx.body = getResponseBody(data)
  }
}

module.exports = UsershareController
