'use strict'
const Controller = require('egg').Controller
const _ = require('lodash')
const { getResponseBody, getResponseError } = require('../extend/utils')
const { getNewTipsConditionsModel } = require('../utils/component')

class AiChatController extends Controller {
  /**
   * 通过ai助手更新组件
   */
  async comsUpdate() {
    const { ctx } = this
    const { body } = ctx.request
    const { screenId, mode = 'edit' } = ctx.query
    const filter = { screenId }
    const projection = {
      _id: 0,
      __v: 0,
      staticData: 0,
      controlConfig: 0,
      events: 0,
      actions: 0
    }
    // mode 分为 edit-编辑模式； read-预览模式。
    if (mode === 'edit') {
      delete projection.controlConfig // projection 不能混合指定
      delete projection.events
      delete projection.actions
    }
    try {
      const res = await ctx.service.component
        .originFindToPromise(filter, projection)
        .lean()
        .exec()

        // 处理子组件
      let childIds = _.reduce(
        res,
        (arr, d) => {
          arr.push(...d.children)
          return arr
        },
        []
      )

      // 过滤已查询出来的组件
      childIds = childIds.filter(childId => {
        const index = res.findIndex(item => {
          return item.id === childId
        })
        return index === -1
      })

      if (childIds.length) {
        const childData = await ctx.service.component
          .originFindToPromise({ id: { $in: childIds } }, projection)
          .lean()
          .exec()

        res.push(...childData)
      }
      const comAry = []
      for (let index = 0; index < res.length; index++) {
        const comData = res[index]

        // 兼容数据
        if (comData && comData.dataConfig) {
          const tips = comData.dataConfig.dataResponse.tips
          if (!tips) {
            res[index].dataConfig.dataResponse.tips = getNewTipsModel()
          } else if (!tips.conditions) {
            res[index].dataConfig.dataResponse.tips.conditions = [
              getNewTipsConditionsModel(tips)
            ]
          } else if (tips.conditions.length === 0) {
            res[index].dataConfig.dataResponse.tips.conditions.push(
              getNewTipsConditionsModel(tips)
            )
          }
        }

        // 兼容数据
        if (comData.interactionConfig && comData.interactionConfig.linkAge) {
          comData.interactionConfig.linkAge.forEach(item => {
            if (!item.linkageConfig) {
              return
            }

            item.linkageConfig.forEach(one => {
              one.componentList.forEach(component => {
                if (!component.sourceType) {
                  component.sourceType = item.source
                }
              })
            })
          })
        }

        // 转换成对象
        comAry.push(comData)
      }
      ctx.body = getResponseBody(comAry)
      const aiUrl = 'http://*************:17774' + '/api/copilot/ai_assit/chat'
      const result = await ctx.curl(aiUrl, {
        method: 'POST',
        data: {
          content_type: 'text',
          sys_pro_name: 'vis',
          action_route: 'edit',
          content: JSON.stringify({
            question: body.keyword,
            components: comAry
          })
        },
        dataType: 'json',
      })
      console.log('11111', {
        content_type: 'text',
        sys_pro_name: 'vis',
        action_route: 'edit',
        content: JSON.stringify({
          question: body.keyword,
          components: comAry
        })
      })
      if(result.status === 200 && result.data.result) {
        ctx.body = getResponseBody(result.data.result.data)
      } else {
        ctx.body = getResponseError(result)
      }
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })
    }
    // this.ctx.logger.info('请求的ai助手组件', loginUrl, result)
  }
}

module.exports = AiChatController
