const {
  uuid,
  getResponseBody,
  getResponseError,
  compareVersion,
  addConfig,
  randomStr,
  getLoggerModel
} = require('../extend/utils')
const _ = require('lodash')

const Controller = require('egg').Controller
/**
 * @Controller 组件标签
 */
class LabelController extends Controller {
  /**
   * @Summary 创建标签/分类
   * @Description 创建标签/分类
   * @Router POST /component/tags/create
   * @Request body LabelModel
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async create() {
    const { ctx } = this
    // const { query } = ctx
    const body = ctx.request.body
    const res = await ctx.service.label.create(body)
    ctx.body = getResponseBody(res)
  }
  /**
   * @Summary 删除标签/分类
   * @Description 删除标签/分类
   * @Router POST /component/tags/delete
   * @Request query number categoryId 分类Id
   * @Request query number id 标签Id
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async delete() {
    const { ctx } = this
    const { query } = ctx
    const { categoryId, id } = query
    if (categoryId) {
      const docs = (await ctx.service.label.find({ categoryId })) || []
      const ids = docs.map(doc => doc.id)
      const res = await ctx.service.label.deleteMany({ id: { $in: ids } })
      ctx.body = getResponseBody(res)
    } else if (id) {
      const res = await ctx.service.label.deleteOne({ id })
      ctx.body = getResponseBody(res)
    }
  }
  /**
   * @Summary 查询所有标签/根据组件类型查询标签
   * @Description 查询所有标签/根据组件类型查询标签
   * @Router GET /component/tags
   * @Request query string compId 组件类型
   * @Response 200 LabelSuccessResponse ok
   * @Response 400 errorResponse error
   */
  async index() {
    const { ctx } = this
    const { compId } = ctx.query
    const res = await ctx.service.label.find()
    let temp = {}
    for (let i = 0; i < res.length; i++) {
      const data = res[i]
      if (!temp[data['categoryId']]) {
        temp[data['categoryId']] = {
          categoryId: data['categoryId'],
          categoryName: data['categoryName'],
          tags: [
            {
              id: data['id'],
              name: data['name']
            }
          ]
        }
        if (compId) {
          temp[data['categoryId']].tags[0].checked =
            data['compList'].findIndex(item => item.compId === compId) > -1
        }
      } else {
        const obj = {
          id: data['id'],
          name: data['name']
        }
        if (compId) {
          obj.checked =
            data['compList'].findIndex(item => item.compId === compId) > -1
        }
        temp[data['categoryId']].tags.push(obj)
      }
    }
    const result = Object.values(temp)
    temp = null
    ctx.body = getResponseBody(result)
  }
  /**
   * @Summary 更新组件标签
   * @Description 更新组件标签
   * @Router POST /component/tags/update
   * @Request query number id 标签Id
   * @Request query string compId 组件类型
   * @Request query string compName 组件名称
   * @Request query number originId 变更前的标签id
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async update() {
    const { ctx } = this
    const { body } = ctx.request
    const { id, compId, compName, originId } = body
    const originDoc = await ctx.service.label.find({ id: originId })
    const doc = await ctx.service.label.find({ id })
    if (originDoc.length) {
      const index = originDoc[0].compList.findIndex(
        item => item.compId === compId
      )
      if (index > -1) {
        originDoc[0].compList.splice(index, 1)
      }
      await ctx.service.label.update(
        { id: originId },
        { compList: originDoc[0].compList }
      )
    }
    if (doc.length) {
      doc[0].compList.push({
        compId,
        compName
      })
      const res = await ctx.service.label.update(
        { id },
        { compList: doc[0].compList }
      )
      ctx.body = getResponseBody(res)
      return
    }
    ctx.body = getResponseBody()
  }
  /**
   * @Summary 根据标签查询组件
   * @Description 根据标签查询组件
   * @Router POST /component/tags/list
   * @Response 200 ComponentLabelModel ok
   * @Response 400 errorResponse error
   */
  async indexByTag() {
    const { ctx } = this
    const { body } = ctx.request
    const { tags } = body
    const tagArr = tags
    const res = await ctx.service.label.find({ id: { $in: tagArr } })
    let comps = []
    for (let i = 0; i < res.length; i++) {
      const data = res[i]
      comps = comps.concat(data.compList)
    }
    comps = _.uniqBy(comps, 'compId')
    ctx.body = getResponseBody(comps)
  }
}

module.exports = LabelController
