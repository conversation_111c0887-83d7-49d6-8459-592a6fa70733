'use strict'
const fs = require('fs-extra')
const path = require('path')
const Controller = require('egg').Controller
const _ = require('lodash')
const {
  getResponseBody,
  getResponseError,
  copyResource,
  uuid,
  getFileName,
  getLoggerModel,
  randomId
} = require('../extend/utils')
const { screenDataTransfer } = require('../utils/screen')
const { EXPORT_COMTYPE, DASHBOARD_CODES } = require('../extend/constant')
const { cipher, decipher } = require('../extend/crypto')
const {
  componentListToObj,
  getCombinationScreenModel
} = require('../utils/preview')
const compressing = require('compressing')
const {
  getNewTipsConditionsModel,
  getNewTipsModel
} = require('../utils/component')

/**
 * @Controller 大屏
 */
class ScreenController extends Controller {
  /**
   * @Summary 预览大屏时获取大屏信息
   * @Description 一起返回[过滤器数据、组件数据、Screensocket数据、字体列表数据]
   * @Router get /screen
   * @Request query number id 大屏ID
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async index() {
    const { ctx, config } = this
    const id = ctx.query.id
    const isReferPanel = ctx.query.isReferPanel || false
    const sysuserid = ctx.header.sysuserid
    const voiceConfig = config.voiceConfig || {
      voiceWeak: false,
      weakWord: '你好伏羲'
    }
    const workspaceData = await ctx.service.workspace.findOne({
      userId: sysuserid
    })
    if (!id) {
      // const data = await ctx.service.screen.find({}, { layerTree: 0 })
      ctx.body = getResponseBody(null, false, 'ID参数错误', 400)
      return
    }
    const screenInfo = await ctx.service.screen.findOne({ id: Number(id) })
    if (!screenInfo) {
      return (ctx.body = getResponseBody(null, true, '没有这个大屏id', 400))
    }
    if (
      screenInfo.workspaceId &&
      screenInfo.workspaceId != 0 &&
      screenInfo.screenType != 'child'
    ) {
      // 是否是协同编辑用户
      let isCollaborators = false

      // 根据screenId、userId查找协同信息
      const currCoedit = await ctx.service.screencoedit.findOne({
        coeditScreenId: Number(
          screenInfo.isDynamicScreen ? screenInfo.parentId || 0 : screenInfo.id
        ),
        coeditUserId: sysuserid
      })

      isCollaborators = !!currCoedit

      // 不是协同编辑用户才判断 workspaceData.id !== screenInfo.workspaceId
      if (!isReferPanel) {
        if (
          !workspaceData ||
          (!isCollaborators
            ? workspaceData.id !== screenInfo.workspaceId
            : false)
        ) {
          return (ctx.body = getResponseBody(
            null,
            false,
            '非当前用户应用，无打开权限',
            402
          ))
        }
      }
    }

    // 是否是动态面板类型
    const isDynamicScreen = screenInfo.isDynamicScreen || false

    // screenSocket
    let screenSocket = {}
    // 字体列表
    let fonts = []

    /**
     * 获取Screensocket
     * @return Promise
     */
    const getScreensocket = () => {
      return ctx.service.screensocket
        .getScreensocketByScreenId({
          screenId: screenInfo.id
        })
        .then(res => {
          screenSocket = res || {}
          return res
        })
    }

    /**
     * 获取字体列表数据
     * @return Promise
     */
    const getFontList = () => {
      return ctx.service.font.find().then(res => {
        fonts = (res || []).map(item => {
          return {
            enable: item.enable,
            fontName: item.fontName,
            fontUrl: item.fontUrl,
            id: item.id,
            systemFont: item.systemFont
          }
        })
        return res
      })
    }
    let componentList = []
    // 过滤器数据
    let filterList = []

    // promiseList
    const promiseList = [
      ctx.service.preview.getComponentListByScreenIds([screenInfo.id]),
      ctx.service.preview.getFilterListByScreenIds([screenInfo.id])
    ]

    // 分享页数据
    if (!isDynamicScreen) {
      promiseList.push(getScreensocket(), getFontList())
    }

    try {
      await Promise.all(promiseList).then((resList = []) => {
        componentList = resList[0] || []
        filterList = resList[1] || []
      })

      // 组件列表转换为对象
      const components = componentListToObj(componentList)

      const res = getCombinationScreenModel(screenInfo, {
        components,
        filter: filterList,
        voiceConfig
      })

      if (!isDynamicScreen) {
        res.screenSocket = screenSocket
        res.fonts = fonts
      }

      ctx.body = getResponseBody(res)
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screen error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  /**
   * @Summary 编辑大屏时获取大屏信息
   * @Description 编辑大屏时获取大屏信息
   * @Router get /screen/info
   * @Request query number id 大屏ID
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async info() {
    const { ctx, config } = this
    const screenId = ctx.query.id
    const projection = {
      layerTree: 0,
      _id: 0,
      __v: 0
    }
    let data
    try {
      let userId, token
      data = await ctx.service.screen.findOne({ id: screenId }, projection)
      let screenType = data.screenType
      // if (data.screenType == "child") {
      //   data.id = data.parentId
      // }
      if (data) {
        data = data.toObject()
        const workspaceId = data.workspaceId
        const projectId = data.projectId
        userId = ctx.header.sysuserid
        token = ctx.header.systoken
        const project = await ctx.service.project.findOne({
          workspaceId,
          id: projectId
        })
        const workspace = await ctx.service.workspace.findOne({
          id: workspaceId
        })

        // 是否是协同编辑用户
        let isCollaborators = false
        // 如果是协同大屏
        if (data.coeditId) {
          // 根据screenId、userId查找协同信息列表
          const currCoedit = await ctx.service.screencoedit.findOne(
            {
              coeditScreenId: screenId,
              coeditUserId: userId
            },
            {
              createdAt: 0,
              updatedAt: 0
            }
          )

          if (currCoedit && currCoedit.coeditRole === 'viewers') {
            ctx.body = getResponseError({
              message: '暂无编辑权限，请联系大屏创建者',
              code: 402
            })
            return
          }

          if (currCoedit && currCoedit.coeditRole === 'collaborators') {
            isCollaborators = true
            // workspaceId改为我的
            data.workspaceId = currCoedit.workspaceId
            // 标记当前用户是协同者
            data.isCollaborators = true
          }
        }

        if (workspace) {
          if (
            // 不是协同编辑用户才判断 workspace.userId != userId
            (!isCollaborators ? workspace.userId != userId : false) &&
            !data.isScreentpl &&
            !data.isDynamicScreen &&
            workspaceId != 0 &&
            projectId != 0 &&
            screenType !== 'child'
          ) {
            ctx.body = getResponseError({
              message: '大屏不在此用户下',
              code: 402
            })
            return
          }
        }
        if (
          !project &&
          !data.isScreentpl &&
          !data.isDynamicScreen &&
          workspaceId != 0 &&
          projectId != 0 &&
          screenType !== 'child'
        ) {
          ctx.body = getResponseError({
            message: '没有这个组或者空间',
            code: 401
          })
          return
        }
      } else {
        return (ctx.body = getResponseBody(null, true, '没有这个大屏id', 402))
      }
      // -----------验证token过期-开始------------
      const userData = await ctx.service.user.findOne({ userId })
      if (!userData || !userData.isDmcLogin) {
        ctx.body = getResponseError({
          message: '登录失效，请重新登录',
          code: 401
        })
        return
      }
      // -----------验证token过期-开始------------
      const filterData = await ctx.service.filter.find({ screenId: data.id })
      data.filter = filterData
      ctx.body = getResponseBody(data)
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screen/info error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  // 获取某个大屏的所有组件实例
  /**
   * @Summary 获取某个大屏的所有组件实例
   * @Description 获取某个大屏的所有组件实例
   * @Router post /screen/coms
   * @Request query number screenId 大屏ID
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async screenComs() {
    const { ctx } = this
    const { screenId, mode = 'edit' } = ctx.query
    const filter = { screenId }
    const projection = {
      _id: 0,
      __v: 0,
      controlConfig: 0,
      events: 0,
      actions: 0
    }
    // mode 分为 edit-编辑模式； read-预览模式。
    if (mode === 'edit') {
      delete projection.controlConfig // projection 不能混合指定
      delete projection.events
      delete projection.actions
    }
    try {
      const res = await ctx.service.component
        .originFindToPromise(filter, projection)
        .lean()
        .exec()

      // 处理子组件
      let childIds = _.reduce(
        res,
        (arr, d) => {
          arr.push(...d.children)
          return arr
        },
        []
      )

      // 过滤已查询出来的组件
      childIds = childIds.filter(childId => {
        const index = res.findIndex(item => {
          return item.id === childId
        })
        return index === -1
      })

      if (childIds.length) {
        const childData = await ctx.service.component
          .originFindToPromise({ id: { $in: childIds } }, projection)
          .lean()
          .exec()

        res.push(...childData)
      }

      const comObjs = {}

      for (let index = 0; index < res.length; index++) {
        const comData = res[index]

        // 兼容数据
        if (comData && comData.dataConfig) {
          const tips = comData.dataConfig.dataResponse.tips
          if (!tips) {
            res[index].dataConfig.dataResponse.tips = getNewTipsModel()
          } else if (!tips.conditions) {
            res[index].dataConfig.dataResponse.tips.conditions = [
              getNewTipsConditionsModel(tips)
            ]
          } else if (tips.conditions.length === 0) {
            res[index].dataConfig.dataResponse.tips.conditions.push(
              getNewTipsConditionsModel(tips)
            )
          }
        }

        // 兼容数据
        if (comData.interactionConfig && comData.interactionConfig.linkAge) {
          comData.interactionConfig.linkAge.forEach(item => {
            if (!item.linkageConfig) {
              return
            }

            item.linkageConfig.forEach(one => {
              one.componentList.forEach(component => {
                if (!component.sourceType) {
                  component.sourceType = item.source
                }
              })
            })
          })
        }

        // 转换成对象
        comObjs[comData.id] = comData
      }

      ctx.body = getResponseBody(comObjs)
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screen/screenComs error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }
  async getScreensComs() {
    const { ctx } = this
    const body = ctx.request.body
    const screenIds = body.screenIds
    let screensComsList = []
    const projection = {
      _id: 0,
      __v: 0,
      staticData: 0
    }

    try {
      const promiseList = []
      if (screenIds && screenIds.length > 0) {
        const screensComs = await ctx.service.screen.find({
          id: { $in: screenIds }
        })
        // const comData = await ctx.service.component.find({ screenId: { "$in": screenIds } });
        for (let index = 0; index < screensComs.length; index++) {
          promiseList.push(
            new Promise(async (res, rej) => {
              try {
                const screensComselement = screensComs[index]
                const comData = await ctx.service.component.find(
                  { screenId: screensComselement.id },
                  projection
                )
                // screensComsList.push({
                //   id: screensComselement.id,
                //   screenType: screensComselement.screenType,
                //   comList: comData
                // })
                res({
                  id: screensComselement.id,
                  screenType: screensComselement.screenType,
                  comList: comData,
                  layerTree: screensComs[index].layerTree
                })
              } catch (error) {
                res(error)
              }
            })
          )
        }
        screensComsList = await Promise.all(promiseList)
        ctx.body = getResponseBody(screensComsList)
      } else {
        ctx.body = getResponseBody(null, false, '请传入伏羲大屏id', 400)
      }
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screen/getScreensComs error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }
  async findChild() {
    const { ctx } = this
    const query = ctx.query || {}
    const projection = {
      _id: 0,
      __v: 0,
      staticData: 0
    }
    const screens = (await ctx.service.screen.find(query, projection)) || []
    const screenIds = screens.map(item => {
      return item.id
    })
    let screensComsList = []
    try {
      const promiseList = []
      if (screenIds && screenIds.length > 0) {
        const screensComs = await ctx.service.screen.find({
          id: { $in: screenIds }
        })
        // const comData = await ctx.service.component.find({ screenId: { "$in": screenIds } });
        for (let index = 0; index < screensComs.length; index++) {
          promiseList.push(
            new Promise(async (res, rej) => {
              try {
                const screensComselement = screensComs[index]
                const comData = await ctx.service.component.find(
                  { screenId: screensComselement.id },
                  projection
                )
                // screensComsList.push({
                //   id: screensComselement.id,
                //   screenType: screensComselement.screenType,
                //   comList: comData
                // })
                res({
                  name: screensComselement.name,
                  id: screensComselement.id,
                  screenType: screensComselement.screenType,
                  comList: comData,
                  layerTree: screensComs[index].layerTree
                })
              } catch (error) {
                res(error)
              }
            })
          )
        }
        screensComsList = await Promise.all(promiseList)
        ctx.body = getResponseBody(screensComsList)
      } else {
        ctx.body = getResponseBody([])
      }
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })
      this.ctx.logger.info(
        getLoggerModel({
          message: '/screen/getScreensComs error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }
  async create() {
    const { ctx } = this
    const body = ctx.request.body
    try {
      let params, sceneConfig
      if (body.screenType === 'scene') {
        sceneConfig = [
          {
            sceneId: uuid(),
            sceneName: '场景1',
            sceneOrder: 1,
            sceneBackground: '',
            pageList: [
              {
                pageId: uuid(),
                pageName: '页面1',
                pageOrder: 1,
                pageBackground: ''
              }
            ]
          }
        ]
      } else {
        sceneConfig = []
      }
      params = { sceneConfig, ...body }
      const data = await ctx.service.screen.create(params)
      ctx.body = getResponseBody({ id: data.id })
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screen/create error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  /**
   * @Summary 删除大屏
   * @Description 删除大屏，会将关联的组件一起删除
   * @Router post /screen/delete
   * @Request header string sysuserid 用户ID
   * @Request body idBody *body
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async delete() {
    const { ctx, app } = this
    const body = ctx.request.body
    const { screens } = body
    const userId = ctx.header.sysuserid

    try {
      // 参数校验
      const errors = app.validator.validate(
        {
          // 大屏ID
          screens: {
            type: 'array',
            required: true
          }
        },
        ctx.request.body
      )
      // 参数校验
      if (errors && errors.length) {
        ctx.body = getResponseError({
          message: JSON.stringify(errors)
        })
        return
      }
      const ids = [],
        errIds = []
      for (let i = 0; i < screens.length; i++) {
        const id = screens[i]
        // 检查大屏删除权限
        const { success, res } =
          await ctx.service.screen.checkScreenDeletePermissionByScreenId(id)

        if (success) {
          ids.push(id)
        } else {
          errIds.push({
            id,
            res
          })
        }
      }
      if (!ids.length && errIds.length) {
        const res = errIds[errIds.length - 1].res
        ctx.body = getResponseError({
          ...res,
          data: {
            screenIds: errIds.map(item => item.id),
            userId
          }
        })
        return
      }
      const screenInfos = await ctx.service.screen.find({ id: { $in: ids } })
      for (let i = 0; i < screenInfos.length; i++) {
        const screenInfo = screenInfos[i]
        // 如果删除的是协作大屏，要删除协同关联表
        if (screenInfo && !!screenInfo.coeditId) {
          await ctx.service.screencoedit.deleteMany({
            coeditId: screenInfo.coeditId
          })
        }
      }
      // await ctx.service.filter.deleteMany({ screenId: id });
      const data = await ctx.service.screen.delete({ id: { $in: ids } })
      setImmediate(async () => {
        // 清除发布页缓存
        for (let i = 0; i < ids.length; i++) {
          const id = ids[i]
          await ctx.service.component.deleteMany({ screenId: id })
          ctx.service.preview.clearCache({ screenId: id })

          ctx.logger.info(
            getLoggerModel({
              message: '/screen/delete success',
              data: {
                screenId: id
              }
            })
          )
        }
      })
      ctx.body = getResponseBody(data)
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      ctx.logger.info(
        getLoggerModel({
          message: '/screen/delete error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  /**
   * @Summary 更新大屏
   * @Description 更新大屏
   * @Router post /screen/update
   * @Request header string sysuserid 用户ID
   * @Request query number id 大屏ID
   * @Request body idBody *body
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async update() {
    const { ctx } = this
    const body = ctx.request.body
    const id = ctx.query.id

    try {
      // 检查该用户的大屏编辑权限
      const { success, res } =
        await ctx.service.screen.checkScreenEditingPermissionByScreenId({
          screenId: id
        })

      if (!success) {
        ctx.body = getResponseError({
          ...res
        })
        return
      }

      const data = await ctx.service.screen.update({ id }, body)
      ctx.body = getResponseBody(data)
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      ctx.logger.info(
        getLoggerModel({
          message: '/screen/update error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  /**
   * @Summary 更新面板parentId，不用检查该用户是否是大屏所有者
   * @Description
   * @Router post /screen/updateParentId
   * @Request query number id 大屏ID
   * @Request body idBody *body
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async updateParentId() {
    const { ctx, app } = this
    const id = ctx.query.id
    const parentId = ctx.request.body.parentId

    try {
      // 参数校验
      const errors = app.validator.validate(
        {
          // 父级大屏ID
          parentId: {
            type: 'string',
            required: true
          }
        },
        ctx.request.body
      )
      // 参数校验
      if (errors && errors.length) {
        ctx.body = getResponseError({
          message: JSON.stringify(errors)
        })
        return
      }

      await ctx.service.screen.findOneAndUpdate(
        { id: id },
        {
          $set: {
            parentId: parentId
          }
        }
      )

      ctx.body = getResponseBody()
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      ctx.logger.info(
        getLoggerModel({
          message: '/screen/updateParentId error',
          data: {
            error: error
          }
        })
      )
    }
  }

  async exportData(ctx, config, screenId, mainId) {
    const projection = {
      _id: 0,
      __v: 0,
      createdAt: 0,
      updatedAt: 0,
      projectId: 0,
      workspaceId: 0,
      // 协同ID不要导出
      coeditId: 0
    }
    const convertToStatic = ctx.query.convertToStatic || false
    const mainScreenId = mainId
    const userId = ctx.query.userId || ''
    const screenDataPath = path.resolve(config.resourcePath, './screenData')
    if (!fs.existsSync(screenDataPath)) {
      fs.mkdirSync(screenDataPath)
    }
    // 获取大屏的信息，大屏组件信息， 大屏过滤器信息
    const screenInfo = await ctx.service.screen.findOne(
      { id: screenId },
      projection
    )
    // 获取主大屏是否是模版的信息
    const screenInfoTpl = await ctx.service.screen.findOne(
      { id: mainScreenId },
      projection
    )
    let comInfo
    // 如果是模版，导出升级后的的组件
    if (screenInfoTpl.isScreentpl) {
      comInfo = await ctx.service.component.exportUpgradeComs(screenId)
    } else {
      // todo导出的大屏组件是引用面板的话，如果该引用面板里面还有别的面板类组件，则这些组件不会被导出。引用的面板大屏也不会导出
      comInfo = await ctx.service.component.find({ screenId }, projection)
    }
    // 获取组件的基本信息
    const versionitem = comInfo.map(item => {
      return {
        version: item.version,
        comName: item.comName,
        alias: item.alias,
        id: item.id
      }
    })
    const filterInfo = await ctx.service.filter.find(
      { screenId },
      { screenId: 0 }
    )
    // 大屏或者组件 不存在则提示导出文件为空
    if (!screenInfo) {
      ctx.body = getResponseBody(null, false, '导出文件为空', 400)
      return
    }
    // 获取数据源csv_file，json的信息
    const datastorageList = [] // 所有组件数据源信息
    const sourceIdList = [] // 所有组件数据源id

    for (let c = 0; c < comInfo.length; c++) {
      const item = comInfo[c]
      if (item.dataConfig.dataResponse.sourceType === 'csv_file') {
        sourceIdList.push(
          item.dataConfig.dataResponse.source.csv_file.data.sourceId
        )
      } else if (item.dataConfig.dataResponse.sourceType === 'json') {
        sourceIdList.push(
          item.dataConfig.dataResponse.source.json.data.sourceId
        )
      }
      if (convertToStatic) {
        if (item.dataConfig.dataResponse.sourceType !== 'static') {
          const info = await ctx.service.screen.findOne({ id: screenId })
          try {
            let resData = await ctx.service.datastorage.getData({
              componentId: item.id,
              params: { _var: {} },
              workspaceId: info.workspaceId,
              userId
            })
            if (resData && resData.length) {
              resData = resData.slice(0, 5)
              comInfo[c].staticData = resData
            }
          } catch (error) {
            this.ctx.logger.info('导出组件原数据报错，组件id', item.id, error)
          }
        }
      }
    }

    comInfo.map(item => {
      if (item.dataConfig.dataResponse.sourceType === 'csv_file') {
        sourceIdList.push(
          item.dataConfig.dataResponse.source.csv_file.data.sourceId
        )
      } else if (item.dataConfig.dataResponse.sourceType === 'json') {
        sourceIdList.push(
          item.dataConfig.dataResponse.source.json.data.sourceId
        )
      }
    })

    for (let index = 0; index < sourceIdList.length; index++) {
      const element = sourceIdList[index]
      const datastorage = await ctx.service.datastorage.findOne({
        id: element
      })
      datastorageList.push(datastorage)
    }
    // 将数据源信息，大屏信息，组件信息，过滤器信息包装起来
    screenInfo.shareCollection = {}
    let data = {
      screenInfo,
      comInfo,
      filterInfo: filterInfo ? filterInfo : [],
      datastorage: datastorageList
    }
    // 将大屏资源放到指定文件夹
    const screen_id = `screen_${screenInfo.id}`
    data = copyResource(
      JSON.stringify(data),
      config.resourcePath,
      'screenData',
      screen_id,
      mainScreenId
    )
    // 生成大屏数据
    const cipherData = cipher(data)
    const filePath = path.resolve(
      config.resourcePath,
      `./screenData/${screen_id}`
    )
    if (!fs.existsSync(filePath)) {
      fs.mkdirSync(filePath)
    }
    await fs.writeFileSync(`${filePath}/info.txt`, cipherData, err => {
      if (err) throw err
    })
    await fs.renameSync(`${filePath}/info.txt`, `${filePath}/info.cj`, err => {
      if (err) throw err
    })
    // 生成大屏数据地址
    const compressingPath = path.resolve(
      config.resourcePath,
      `./screenData/${screen_id}`
    )
    return {
      compressingPath,
      comlist: versionitem
    }
  }

  async export(batchExport, batchExportId) {
    const { ctx, config } = this
    const compressList = []
    // 获取所有组件
    let comdatalist = []
    const id = ctx.query.id || String(batchExportId)
    const isTiming = ctx.query.isTiming || false
    // 查询大屏内的组件信息
    const screenInfo = await ctx.service.screen.findOne({ id })
    const comInfo = await ctx.service.component.find({
      screenId: id
    })
    for (let index = 0; index < comInfo.length; index++) {
      const element = comInfo[index]
      if (EXPORT_COMTYPE.COMTYPE.includes(element.comType)) {
        for (let i = 0; i < element.config.screens.length; i++) {
          const screenItem = element.config.screens[i]
          const exportData = await this.exportData(
            ctx,
            config,
            screenItem.id,
            id
          )
          compressList.push(exportData.compressingPath)
          comdatalist = comdatalist.concat(exportData.comlist)
        }
      }
    }
    const exportData = await this.exportData(ctx, config, id, id)
    compressList.push(exportData.compressingPath)
    comdatalist = comdatalist.concat(exportData.comlist)
    comdatalist = _.uniqBy(comdatalist, 'comName')
    // 将所有大屏放到一个统一的压缩包内并下载
    const fuxi = path.resolve(
      config.resourcePath,
      `./screenData/${screenInfo.name}`
    )
    const t = new Date().getTime()
    const fuxibak = path.resolve(
      config.resourcePath,
      `./commonfiles/${screenInfo.name}${t}`
    )
    if (fs.existsSync(fuxi)) {
      fs.moveSync(fuxi, fuxibak)
    }
    fs.mkdirSync(fuxi)

    // 创建组件包文件夹
    // fs.mkdirSync(`${fuxi}/compackages`);
    // 将组件导出
    // for (let index = 0; index < comdatalist.length; index++) {
    //   const element = comdatalist[index].comName;
    //   const compath = await ctx.service.packcom.pack('https://125.32.31.231', element);
    //   console.log(compath)
    //   await fs.moveSync(compath, `${fuxi}/compackages/${element}`)
    // }

    await fs.writeFileSync(`${fuxi}/screenMainId.txt`, id, err => {
      if (err) throw err
    })
    this.ctx.logger.info('get final export compressList: ', compressList)
    compressList.map(item => {
      const fieldname = getFileName(item)
      if (fs.existsSync(item)) {
        this.ctx.logger.info(
          '压缩包内各个大屏的文件名',
          item,
          `${fuxi}/${fieldname}`
        )
        fs.moveSync(item, `${fuxi}/${fieldname}`)
      }
    })

    await compressing.tgz.compressDir(fuxi, `${fuxi}.tgz`)
    if (batchExport === true) {
      return `${fuxi}.tgz`
    }
    // 针对定时任务修改返回值
    if (isTiming) {
      const screenTpl = path.resolve(
        config.resourcePath,
        `./screenTplData/${screenInfo.name}.tgz`
      )
      if (fs.existsSync(screenTpl)) {
        fs.removeSync(screenTpl)
      }
      fs.moveSync(`${fuxi}.tgz`, `${screenTpl}`)
      ctx.body = getResponseBody(screenTpl)
    } else {
      this.ctx.logger.info('压缩包名字', `${fuxi}.tgz`)
      ctx.attachment(`${fuxi}.tgz`)
      ctx.set('Content-Type', 'application/octet-stream')
      ctx.body = fs.createReadStream(`${fuxi}.tgz`)
    }
  }
  async batchExport() {
    const { ctx, config } = this
    const screenIds = JSON.parse(ctx.query.screenIds)
    let resData = []
    const t = new Date().getTime()
    const batchExportPkg = path.resolve(
      config.resourcePath,
      `./batchExport/${uuid()}${t}`
    )
    const batchPkg = path.resolve(config.resourcePath, `./batchExport`)
    if (!fs.existsSync(batchPkg)) {
      fs.mkdirSync(batchPkg)
    }
    if (!fs.existsSync(batchExportPkg)) {
      fs.mkdirSync(batchExportPkg)
    }
    screenIds.forEach(id => {
      resData.push(
        new Promise(async (resolve, reject) => {
          try {
            const batchExportPath = await this.export(true, id)
            const filepath = path.basename(batchExportPath)
            fs.moveSync(batchExportPath, `${batchExportPkg}/${filepath}`)
            resolve(batchExportPath)
          } catch (e) {
            resolve({
              code: e.code,
              message: e.message
            })
          }
        })
      )
    })
    try {
      const ret = await Promise.all(resData)
      this.ctx.logger.info('压缩包的各大屏路径', ret)
      this.ctx.logger.info('批量导出包的路径', batchExportPkg)
      await compressing.tgz.compressDir(batchExportPkg, `${batchExportPkg}.tgz`)
      ctx.attachment(`${batchExportPkg}.tgz`)
      ctx.set('Content-Type', 'application/octet-stream')
      ctx.body = fs.createReadStream(`${batchExportPkg}.tgz`)
    } catch (error) {
      ctx.body = getResponseBody(error, false, '批量导出大屏报错', 400)
    }
  }

  /**
   * 导入数据
   * @param {object} ctx ctx
   * @param {object} config 配置
   * @param {string} fileData 文件内容
   * @param {object} screenIdMapping
   * @param {string} screenMainName 大屏名称
   * @param {boolean} istpl 是否是模板
   * @param {number} newScreenId 新的大屏ID
   * @param {number} parentScreenId 面板类组件父级大屏ID
   * @return
   */
  async importData({
    ctx,
    config,
    fileData,
    timeStamp,
    screenIdMapping = {},
    screenMainName = '',
    istpl,
    newScreenId = null,
    parentScreenId = null
  }) {
    const t = timeStamp
    const body = ctx.request.body
    // 获取导入的工作空间和组
    const workspaceId = body.workspaceId
    const projectId = body.projectId
    const publicPath = config.publicPath
    const origin = body.origin || false // 标识是否恢复成组件原始数据、清空交互设置等
    let screenInfoStr = decipher(fileData)
    // 获取匹配的域名
    let res = decipher(fileData).match(/(\w+):\/\/([^/:"']+)(:\d*)?/g)
    const serverIp = config.serverIp
    const port = config.cluster.listen.port
    res = _.uniq(res)
    this.ctx.logger.info('get final export res替换的路径: ', res)
    // 全局替换
    res.map(item => {
      if (config.componentUrl === '') {
        screenInfoStr = screenInfoStr.replace(
          new RegExp(item, 'g'),
          `http://${serverIp}:${port}`
        )
      } else {
        screenInfoStr = screenInfoStr.replace(new RegExp(item, 'g'), ``)
      }
    })
    const { screenInfo, comInfo, filterInfo, datastorage } =
      JSON.parse(screenInfoStr)
    if (origin === 'true') {
      comInfo.forEach(com => {
        const dataItem = com.staticData[0]
        com.dataConfig.dataResponse.filters.list = []
        com.dataConfig.dataResponse.sourceType = 'static'
        if (dataItem) {
          const keys = Object.keys(dataItem)
          com.dataConfig.fieldMapping = com.dataConfig.fieldMapping.map(
            item => {
              // 映射初始化
              if (!keys.includes(item.target)) {
                let target = keys.find(
                  key => typeof dataItem[key] === item.type
                )
                if (keys.length === 1) {
                  target = keys[0]
                }
                if (target) keys.splice(keys.indexOf(target), 1)
                else target = item.source
                item.target = target
              } else {
                let targetIndex = keys.findIndex(key => key === item.target)
                keys.splice(targetIndex, 1)
              }
              return item
            }
          )
        }
        com.interactionConfig.callbackParams = []
        com.interactionConfig.drillDown = []
        com.interactionConfig.events = []
        com.interactionConfig.linkAge = []
      })
    }
    const oldScreenId = screenInfo.id
    delete screenInfo.id
    const { type, screenType, templateId, name, isDynamicScreen } = screenInfo
    const screenName = `${screenMainName ? screenMainName : name}`
    screenInfo.name = screenName

    // 创建新的大屏ID，具体看app/model/screen.js
    newScreenId = newScreenId || randomId()

    const newScreenData = await ctx.service.screen.create({
      id: newScreenId,
      projectId,
      workspaceId,
      type,
      screenType,
      templateId,
      isDynamicScreen,
      name: screenName
    })

    const comParams = {
      workspaceId,
      copyScreenId: newScreenData.id,
      comList: comInfo,
      screenIdMapping,
      screenInfo,
      filterInfo: origin === 'true' ? [] : filterInfo,
      datastorage,
      type: 'import',
      t
    }
    const layerTree = await this.ctx.service.component.bacthCopy(comParams)
    // 修改新创建大屏的某些属性
    screenInfo.layerTree = layerTree
    screenInfo.workspaceId = workspaceId
    screenInfo.isConnect = false
    screenInfo.isScreentpl = istpl ? istpl : false

    const newScreenInfo = await ctx.service.screen.findOneAndUpdate(
      { id: newScreenData.id },
      {
        ...screenInfo,
        parentId: parentScreenId
      },
      {
        new: true
      }
    )
    // const newScreenInfo = await ctx.service.screen.findOne({
    //   id: newScreenData.id
    // })

    setImmediate(() => {
      this.ctx.logger.info('get final improt screenInfo: ', newScreenInfo)
      // this.ctx.logger.info(
      //   'get final improt screenIdMapping: ',
      //   screenIdMapping
      // )
    })
    return {
      oldId: oldScreenId,
      newId: newScreenData.id,
      newScreenInfo
    }
  }

  /**
   * 导入大屏
   * @return
   */
  async import() {
    const { ctx, config } = this
    const body = ctx.request.body
    const autoUpgrade = body.autoUpgrade || false
    console.log(autoUpgrade, 'autoUpgrade')
    const t = new Date().getTime()
    // 大屏的映射数据
    const screenIdMapping = {}
    if (!ctx.request.files || !ctx.request.files.length) {
      ctx.body = getResponseBody(null, false, '无法创建，导入文件为空！', 400)
      return
    }
    const file = ctx.request.files[0]
    let fileDir = ''
    // if (file.filename.indexOf('(') !== -1) {
    //   fileDir = file.filename.split('(')[0]
    //   fileDir = fileDir.replace(/(^\s*)|(\s*$)/g, '')
    // } else {
    //   fileDir = file.filename.substring(0, file.filename.lastIndexOf('.'))
    //   fileDir = fileDir.replace(/(^\s*)|(\s*$)/g, '')
    // }
    // 获取解压的大屏id
    let oldScreenId = ''
    // 解压相关数据  todo获取解压后的名字
    const fileTimeName = uuid()
    const uncompressPath = path.resolve(config.resourcePath, './screenData')
    const uncompressPathTime = path.resolve(
      config.resourcePath,
      `./screenData/${fileTimeName}`
    )
    await compressing.tgz.uncompress(file.filepath, uncompressPathTime)
    const uncompressPathName = fs.readdirSync(uncompressPathTime)

    if (uncompressPathName && uncompressPathName.length == 1) {
      fileDir = uncompressPathName[0]
      if (fs.existsSync(`${uncompressPath}/${fileDir}`)) {
        fs.removeSync(`${uncompressPath}/${fileDir}`)
      }
      fs.moveSync(
        `${uncompressPathTime}/${fileDir}`,
        `${uncompressPath}/${fileDir}`
      )

      setImmediate(() => {
        // 删除空文件夹
        fs.removeSync(uncompressPathTime)
      })
    }

    const fuxiPathOld = path.resolve(uncompressPath, `./${fileDir}/`)
    let fuxiPath
    // 遍历压缩包内所有大屏文件，优先生成组件关联的大屏
    if (fs.existsSync(`${fuxiPathOld}/screenMainId.txt`)) {
      oldScreenId = await fs.readFileSync(
        `${fuxiPathOld}/screenMainId.txt`,
        'utf-8'
      )
      fuxiPath = path.resolve(uncompressPath, `./fuxi_${oldScreenId}`)
      const commonpath = path.resolve(
        config.resourcePath,
        `./commonfiles/fuxi_${oldScreenId}${t}`
      )
      if (fs.existsSync(fuxiPath)) {
        fs.moveSync(fuxiPath, commonpath)
      }
      fs.renameSync(fuxiPathOld, fuxiPath)
    } else {
      oldScreenId = fileDir.split('_').pop()
      fuxiPath = path.resolve(uncompressPath, `./fuxi_${oldScreenId}`)
    }
    const files = fs.readdirSync(fuxiPath)

    // 新的ID列表
    const newIdList = []

    // 新的主面板ID
    const newMainScreenId = randomId()

    // 导入
    const importAction = async ({
      filePath,
      screenIdMapping = {},
      screenMainName,
      newScreenId,
      parentScreenId
    }) => {
      const fileData = await fs.readFileSync(
        `${fuxiPath}/${filePath}/info.cj`,
        'utf-8'
      )

      const screenIdItem = await this.importData({
        ctx,
        config,
        fileData,
        timeStamp: t,
        screenIdMapping,
        screenMainName,
        newScreenId,
        parentScreenId
      })

      return screenIdItem
    }

    const promiseList = []

    // 遍历压缩包内所有大屏文件，优先生成组件关联的大屏
    for (let index = 0; index < files.length; index++) {
      const element = files[index]
      if (element === '.DS_Store') {
        continue
      }
      // 去掉screenMainId。txt
      if (element === 'screenMainId.txt') {
        continue
      }
      const otherScreenId = element.split('_').pop()
      if (otherScreenId !== oldScreenId) {
        const prom = importAction({
          filePath: element,
          parentScreenId: newMainScreenId
        }).then(screenIdItem => {
          // 生成新的面板大屏id
          newIdList.push(Number(screenIdItem.newId))
          // 生成新的大屏映射
          screenIdMapping[screenIdItem.oldId] = screenIdItem.newId
        })

        promiseList.push(prom)

        // const fileData = await fs.readFileSync(
        //   `${fuxiPath}/${element}/info.cj`,
        //   'utf-8'
        // )
        // const screenIdItem = await this.importData(ctx, config, fileData, t)
        // const comdataitem = await ctx.service.component.find(
        //   { screenId: screenIdItem.newId },
        //   { _id: 0 }
        // )
        // const versionitem = comdataitem.map(item => {
        //   return {
        //     comType: item.comType,
        //     version: item.version,
        //     alias: item.alias,
        //     currentVersion: comversion[item.comType] || null
        //   }
        // })
        // versionList = versionList.concat(versionitem)
        // // 生成新的面板大屏id
        // newIdList.push(Number(screenIdItem.newId))
        // // 生成新的大屏映射
        // screenIdMapping[screenIdItem.oldId] = screenIdItem.newId
      }
    }

    await Promise.all(promiseList)

    const res = await importAction({
      filePath: `screen_${oldScreenId}`,
      screenIdMapping,
      newScreenId: newMainScreenId,
      screenMainName: body.name
    })

    // 组件版本
    let packList = []
    // 组件列表
    let comList = []

    // 获取当前大屏的组件版本
    const packProm = ctx.service.package.find(
      {},
      {
        alias: 0,
        icon: 0,
        type: 0,
        presetChildren: 0, // 预设子组件列表
        children: 0, // 支持的子组件名称数组
        width: 0,
        height: 0,
        config: 0,
        platform: 0,
        path: 0,
        hidden: 0,
        createdAt: 0,
        updatedAt: 0
      }
    )

    // 获取当前大屏的组件
    const comProm = ctx.service.component
      .originFindToPromise(
        {
          screenId: {
            $in: [...newIdList, res.newId]
          }
        },
        {
          _id: 0,
          pageId: 0,
          sceneId: 0,
          type: 0,
          parent: 0,
          orginName: 0,
          children: 0,
          icon: 0,
          show: 0,
          needCommon: 0,
          config: 0,
          callbacks: 0,
          ordinary: 0,
          controlConfig: 0,
          dataConfig: 0,
          interactionConfig: 0,
          events: 0,
          actions: 0,
          animation: 0,
          staticData: 0,
          requirePath: 0,
          attr: 0,
          other: 0,
          packageCreateDate: 0,
          createdAt: 0,
          updatedAt: 0
        }
      )
      .lean()
      .exec()

    await Promise.all([packProm, comProm]).then((res = []) => {
      res = res || []
      packList = res[0] || []
      comList = res[1] || []
    })

    const comVersion = packList.reduce((map, v) => {
      const key = `${v.name}`
      map[key] = v.version
      return map
    }, {})

    let versionList = comList.map(item => {
      return {
        id: item.id,
        comType: item.comType,
        version: item.version,
        alias: item.alias,
        currentVersion: comVersion[item.comType] || null
      }
    })
    console.log(versionList, '=====')
    if (autoUpgrade) {
      for (let i = 0; i < versionList.length; i++) {
        const com = versionList[i]
        if (com.version !== com.currentVersion) {
          await ctx.service.component.upgrade({
            id: com.id,
            version: com.currentVersion
          })
        }
      }
    }
    versionList = _.uniqBy(versionList, 'comType')
    // 组件自动升级
    // const mainScreenData = await fs.readFileSync(
    //   `${fuxiPath}/screen_${oldScreenId}/info.cj`,
    //   'utf-8'
    // )
    // const res = await this.importData(
    //   ctx,
    //   config,
    //   mainScreenData,
    //   t,
    //   screenIdMapping,
    //   body.name
    // )

    // // 获取新的大屏的组件的信息
    // const comdataitem = await ctx.service.component.find(
    //   { screenId: res.newId },
    //   { _id: 0 }
    // )
    // const versionitem = comdataitem.map(item => {
    //   return {
    //     comType: item.comType,
    //     version: item.version,
    //     alias: item.alias,
    //     currentVersion: comversion[item.comType] || null
    //   }
    // })
    // versionList = versionList.concat(versionitem)

    // 面板大屏的parentId进行绑定
    // await ctx.service.screen.updateMany(
    //   { id: { $in: newIdList } },
    //   { parentId: res.newId }
    // )

    // const { name, type, level, id, description, size } = res.newScreenInfo
    ctx.body = getResponseBody({
      versionList
    })
  }

  async importTpl() {
    const { ctx, config } = this
    const body = ctx.request.body
    const t = new Date().getTime()
    // 大屏的映射数据
    const screenIdMapping = {}
    if (!ctx.request.files || !ctx.request.files.length) {
      ctx.body = getResponseBody(null, false, '无法创建，导入文件为空！', 400)
      return
    }
    const file = ctx.request.files[0]
    let fileDir = ''
    // if (file.filename.indexOf('(') !== -1) {
    //   fileDir = file.filename.split('(')[0]
    //   fileDir = fileDir.replace(/(^\s*)|(\s*$)/g, '')
    // } else {
    //   fileDir = file.filename.substring(0, file.filename.lastIndexOf('.'))
    //   fileDir = fileDir.replace(/(^\s*)|(\s*$)/g, '')
    // }
    // 获取解压的大屏id
    let oldScreenId = ''
    // 解压相关数据  todo获取解压后的名字
    const fileTimeName = uuid()
    const uncompressPath = path.resolve(config.resourcePath, './screenData')
    const uncompressPathTime = path.resolve(
      config.resourcePath,
      `./screenData/${fileTimeName}`
    )
    await compressing.tgz.uncompress(file.filepath, uncompressPathTime)
    const uncompressPathName = fs.readdirSync(uncompressPathTime)

    if (uncompressPathName && uncompressPathName.length == 1) {
      fileDir = uncompressPathName[0]
      if (fs.existsSync(`${uncompressPath}/${fileDir}`)) {
        fs.removeSync(`${uncompressPath}/${fileDir}`)
      }
      fs.moveSync(
        `${uncompressPathTime}/${fileDir}`,
        `${uncompressPath}/${fileDir}`
      )

      setImmediate(() => {
        // 删除空文件夹
        fs.removeSync(uncompressPathTime)
      })
    }

    const fuxiPathOld = path.resolve(uncompressPath, `./${fileDir}/`)

    // 遍历压缩包内所有大屏文件，优先生成组件关联的大屏
    if (fs.existsSync(`${fuxiPathOld}/screenMainId.txt`)) {
      oldScreenId = await fs.readFileSync(
        `${fuxiPathOld}/screenMainId.txt`,
        'utf-8'
      )
    } else {
      oldScreenId = fileDir.split('_').pop()
    }
    const fuxiPath = path.resolve(uncompressPath, `./fuxi_${oldScreenId}/`)
    if (fs.existsSync(fuxiPath)) {
      fs.removeSync(fuxiPath)
    }

    fs.renameSync(fuxiPathOld, fuxiPath)

    const files = fs.readdirSync(fuxiPath)

    // 新的主面板ID
    const newMainScreenId = randomId()

    // 导入
    const importAction = async ({
      filePath,
      screenIdMapping = {},
      screenMainName,
      newScreenId,
      parentScreenId
    }) => {
      const fileData = await fs.readFileSync(
        `${fuxiPath}/${filePath}/info.cj`,
        'utf-8'
      )

      const screenIdItem = await this.importData({
        ctx,
        config,
        fileData,
        timeStamp: t,
        istpl: true,
        screenIdMapping,
        screenMainName,
        newScreenId,
        parentScreenId
      })

      return screenIdItem
    }

    const promiseList = []

    // 遍历压缩包内所有大屏文件，优先生成组件关联的大屏
    for (let index = 0; index < files.length; index++) {
      const element = files[index]
      if (element === '.DS_Store') {
        continue
      }
      // 去掉screenMainId。txt
      if (element === 'screenMainId.txt') {
        continue
      }
      const otherScreenId = element.split('_').pop()
      if (otherScreenId !== oldScreenId) {
        const prom = importAction({
          filePath: element,
          parentScreenId: newMainScreenId
        }).then(screenIdItem => {
          // 生成新的大屏映射
          screenIdMapping[screenIdItem.oldId] = screenIdItem.newId
        })

        promiseList.push(prom)

        // const fileData = await fs.readFileSync(
        //   `${fuxiPath}/${element}/info.cj`,
        //   'utf-8'
        // )

        // const screenIdItem = await this.importData({
        //   ctx,
        //   config,
        //   fileData,
        //   timeStamp: t,
        //   istpl: true
        // })

        // 生成新的大屏映射
        // screenIdMapping[screenIdItem.oldId] = screenIdItem.newId
      }
    }

    await Promise.all(promiseList)

    const res = await importAction({
      filePath: `screen_${oldScreenId}`,
      screenIdMapping,
      newScreenId: newMainScreenId,
      screenMainName: body.name
    })

    // const mainScreenData = await fs.readFileSync(
    //   `${fuxiPath}/screen_${oldScreenId}/info.cj`,
    //   'utf-8'
    // )

    // const res = await this.importData({
    //   ctx,
    //   config,
    //   fileData: mainScreenData,
    //   timeStamp: t,
    //   screenIdMapping,
    //   screenMainName: body.name,
    //   istpl: true
    // })

    // const { name, type, level, id, description, size } = res.newScreenInfo
    const screentpldata = await ctx.service.screentpl.create({
      name: res.newScreenInfo.name,
      type: res.newScreenInfo.type,
      level: res.newScreenInfo.level,
      description: '导入的模版',
      size: res.newScreenInfo.size,
      screenId: res.newScreenInfo.id,
      tag: res.newScreenInfo.tag
    })
    ctx.body = getResponseBody(screentpldata)
  }

  async copy() {
    const { ctx, config } = this
    const body = ctx.request.body
    const { workspaceId, projectId, name } = body
    const screenId = body.id
    const t = new Date().getTime()
    // isScreentpl生成模版传true, 以模版创建项目传false
    let isScreentpl
    if (body.isCreateScreen) {
      isScreentpl = false
    } else {
      isScreentpl = !!body.isScreentpl
    }
    const tag = body.tag || []
    // 大屏原id与现id之间的映射
    const screenIdMapping = {}
    const newScreenIdList = []
    // 获取大屏原组件数据
    const comInfoOrigin = await ctx.service.component.find(
      { screenId },
      { _id: 0 }
    )
    for (let index = 0; index < comInfoOrigin.length; index++) {
      const comInfoOriginItem = comInfoOrigin[index]
      if (EXPORT_COMTYPE.COPYCOMTYPE.includes(comInfoOriginItem.comType)) {
        for (let i = 0; i < comInfoOriginItem.config.screens.length; i++) {
          const quoteScreenId = comInfoOriginItem.config.screens[i].id
          const screenInfoNow = await ctx.service.screen.copy({
            screenIdOrigin: quoteScreenId,
            workspaceId,
            projectId,
            name: `${name}${comInfoOriginItem.alias}`,
            isScreentpl,
            tag,
            t
          })
          screenIdMapping[quoteScreenId] = screenInfoNow.id
          if (screenInfoNow.id) {
            newScreenIdList.push(Number(screenInfoNow.id))
          }
        }
      }
    }
    const screenInfoNow = await ctx.service.screen.copy({
      screenIdOrigin: screenId,
      workspaceId,
      projectId,
      name,
      screenIdMapping,
      isScreentpl,
      tag,
      isCreateTpl: true,
      t
    })
    await ctx.service.screen.updateMany(
      { id: { $in: newScreenIdList } },
      { parentId: screenInfoNow.id }
    )
    this.ctx.logger.info('get final copy screenInfo: ', screenInfoNow)
    ctx.body = getResponseBody({
      id: screenInfoNow.id
    })
  }

  async move() {
    const { ctx, config } = this
    const body = ctx.request.body
    const { screenId, workspaceId, projectId, name } = body
    const tag = body.tag || []
    const t = new Date().getTime()
    // 大屏原id与现id之间的映射
    const screenIdMapping = {}
    const newScreenIdList = []
    // 获取大屏原组件数据
    const comInfoOrigin = await ctx.service.component.find(
      { screenId },
      { _id: 0 }
    )
    for (let index = 0; index < comInfoOrigin.length; index++) {
      const comInfoOriginItem = comInfoOrigin[index]
      if (EXPORT_COMTYPE.COPYCOMTYPE.includes(comInfoOriginItem.comType)) {
        for (let i = 0; i < comInfoOriginItem.config.screens.length; i++) {
          const quoteScreenId = comInfoOriginItem.config.screens[i].id
          const screenInfoNow = await ctx.service.screen.copy({
            screenIdOrigin: quoteScreenId,
            workspaceId,
            projectId,
            name: `${name}${comInfoOriginItem.alias}`,
            isScreentpl: false,
            tag,
            t
          })
          screenIdMapping[quoteScreenId] = screenInfoNow.id
          if (screenInfoNow.id) {
            newScreenIdList.push(Number(screenInfoNow.id))
          }
        }
      }
    }
    const screenInfoNow = await ctx.service.screen.copy({
      screenIdOrigin: screenId,
      workspaceId,
      projectId,
      name,
      screenIdMapping,
      t
    })
    await ctx.service.screen.updateMany(
      { id: { $in: newScreenIdList } },
      { parentId: screenInfoNow.id }
    )
    this.ctx.logger.info('get final move screenInfo: ', screenInfoNow)
    ctx.body = getResponseBody(screenInfoNow)
  }

  // 新增场景
  async addScene() {
    const { ctx } = this
    const body = ctx.request.body
    const id = ctx.query.id

    try {
      // 检查该用户的大屏编辑权限
      const { success, res } =
        await ctx.service.screen.checkScreenEditingPermissionByScreenId({
          screenId: id
        })

      if (!success) {
        ctx.body = getResponseError({
          ...res
        })
        return
      }

      const projection = {
        workspaceId: 0
      }
      let screenInfo = await ctx.service.screen.findOne({ id }, projection)

      if (!screenInfo) {
        ctx.body = getResponseError({ message: `找不到大屏id：${id}` })
        return
      }

      screenInfo = screenInfo.toObject()

      const sceneConfig = screenInfo.sceneConfig
      sceneConfig.push({
        sceneId: uuid(),
        sceneName: `场景${sceneConfig.length + 1}`,
        sceneOrder: sceneConfig.length,
        sceneBackground: body.sceneBackground ? body.sceneBackground : '',
        pageList: [
          {
            pageId: uuid(),
            pageName: '页面1',
            pageOrder: 1,
            pageBackground: ''
          }
        ]
      })
      await ctx.service.screen.update({ id }, { sceneConfig })
      // const data = await ctx.service.screen.findOne({ id })
      ctx.body = getResponseBody(screenInfo)
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screen/addScene error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }
  // 新增页面
  async addPage() {
    const { ctx } = this
    const body = ctx.request.body
    const id = ctx.query.id

    try {
      // 检查该用户的大屏编辑权限
      const { success, res } =
        await ctx.service.screen.checkScreenEditingPermissionByScreenId({
          screenId: id
        })

      if (!success) {
        ctx.body = getResponseError({
          ...res
        })
        return
      }

      const screenInfo = await ctx.service.screen.findOne({ id })
      const sceneConfig = screenInfo.sceneConfig
      for (let index = 0; index < sceneConfig.length; index++) {
        if (sceneConfig[index].sceneId === body.sceneId) {
          const num = sceneConfig[index].pageList.length + 1
          sceneConfig[index].pageList.push({
            pageId: uuid(),
            pageName: `页面${num}`,
            pageOrder: num,
            pageBackground: ''
          })
        }
      }
      await ctx.service.screen.update({ id }, { sceneConfig })
      const data = await ctx.service.screen.findOne({ id })
      ctx.body = getResponseBody(data)
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screen/addPage error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }
  // 删除页面
  async deletePage() {
    const { ctx } = this
    const body = ctx.request.body
    const id = ctx.query.id

    try {
      // 检查该用户的大屏编辑权限
      const { success, res } =
        await ctx.service.screen.checkScreenEditingPermissionByScreenId({
          screenId: id
        })

      if (!success) {
        ctx.body = getResponseError({
          ...res
        })
        return
      }

      const screenInfo = await ctx.service.screen.findOne({ id })
      const sceneConfig = screenInfo.sceneConfig
      const layerTree = screenInfo.layerTree
      for (let index = 0; index < sceneConfig.length; index++) {
        if (sceneConfig[index].sceneId === body.sceneId) {
          sceneConfig[index].pageList = sceneConfig[index].pageList.filter(
            item => {
              return item.pageId !== body.pageId
            }
          )
        }
      }
      const newlayerTree = layerTree.filter(item => {
        return item.pageId !== body.pageId
      })
      await ctx.service.screen.update(
        { id },
        { sceneConfig, layerTree: newlayerTree }
      )
      const data = await ctx.service.component.deleteMany({
        screenId: id,
        pageId: body.pageId
      })
      ctx.body = getResponseBody(data)
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screen/deletePage error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }
  // 删除场景
  async deleteScene() {
    const { ctx } = this
    const body = ctx.request.body
    const id = ctx.query.id

    try {
      // 检查该用户的大屏编辑权限
      const { success, res } =
        await ctx.service.screen.checkScreenEditingPermissionByScreenId({
          screenId: id
        })

      if (!success) {
        ctx.body = getResponseError({
          ...res
        })
        return
      }

      const screenInfo = await ctx.service.screen.findOne({ id })
      const layerTree = screenInfo.layerTree
      let sceneConfig = screenInfo.sceneConfig
      sceneConfig = sceneConfig.filter(item => {
        return item.sceneId !== body.sceneId
      })
      const newlayerTree = layerTree.filter(item => {
        return item.sceneId !== body.sceneId
      })
      await ctx.service.component.deleteMany({
        screenId: id,
        sceneId: body.sceneId
      })
      const data = await ctx.service.screen.update(
        { id },
        { sceneConfig, layerTree: newlayerTree }
      )
      ctx.body = getResponseBody(data)
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screen/deleteScene error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }
  // 更新场景页面名称
  async updateName() {
    const { ctx } = this
    const body = ctx.request.body
    const id = ctx.query.id

    try {
      // 检查该用户的大屏编辑权限
      const { success, res } =
        await ctx.service.screen.checkScreenEditingPermissionByScreenId({
          screenId: id
        })

      if (!success) {
        ctx.body = getResponseError({
          ...res
        })
        return
      }

      const screenInfo = await ctx.service.screen.findOne({ id })
      const sceneConfig = screenInfo.sceneConfig
      sceneConfig.map(item => {
        if (item.sceneId === body.sceneId) {
          item.sceneName = body.sceneName
          item.pageList.map(page => {
            if (page.pageId === body.pageId) {
              page.pageName = body.pageName
            }
          })
        }
      })
      await ctx.service.screen.update({ id }, { sceneConfig })
      ctx.body = getResponseBody({
        id,
        sceneConfig
      })
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screen/updateName error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }
  // 改变场景的顺序
  async orderScene() {
    const { ctx } = this
    const body = ctx.request.body
    // const originIndex = body.originIndex
    const targetIndex = body.targetIndex
    const id = body.id
    const sceneId = body.sceneId

    try {
      // 检查该用户的大屏编辑权限
      const { success, res } =
        await ctx.service.screen.checkScreenEditingPermissionByScreenId({
          screenId: id
        })

      if (!success) {
        ctx.body = getResponseError({
          ...res
        })
        return
      }

      const screenInfo = await ctx.service.screen.findOne({ id })
      const sceneConfig = screenInfo.sceneConfig.filter(item => {
        return item.sceneId === sceneId
      })[0]
      // 删除原位置数据
      await ctx.service.screen.update({ id }, { $pull: { sceneConfig } })

      // 向数组指定位置添加数据
      const screen = await ctx.service.screen.update(
        { id },
        {
          $push: {
            sceneConfig: { $each: [sceneConfig], $position: targetIndex }
          }
        }
      )
      ctx.body = getResponseBody(screen)
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screen/orderScene error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }
  // 改变页面的顺序
  async orderPage() {
    const { ctx } = this
    const body = ctx.request.body
    const targetIndex = body.targetIndex
    const id = body.id
    const pageId = body.pageId
    const sceneId = body.sceneId

    try {
      // 检查该用户的大屏编辑权限
      const { success, res } =
        await ctx.service.screen.checkScreenEditingPermissionByScreenId({
          screenId: id
        })

      if (!success) {
        ctx.body = getResponseError({
          ...res
        })
        return
      }

      const screenInfo = await ctx.service.screen.findOne({ id })
      let sceneIndex, sceneConfig
      for (let index = 0; index < screenInfo.sceneConfig.length; index++) {
        const element = screenInfo.sceneConfig[index]
        if (element.sceneId === sceneId) {
          sceneConfig = element
          sceneIndex = index
        }
      }
      const pageList = sceneConfig.pageList.filter(item => {
        return item.pageId === pageId
      })[0]
      const scenepageList = `sceneConfig.${sceneIndex}.pageList`
      // 删除原位置数据  变量当作key需要用[]括起来
      await ctx.service.screen.update(
        { id },
        { $pull: { [scenepageList]: pageList } }
      )

      // 向数组指定位置添加数据
      const componentData = await ctx.service.screen.update(
        { id },
        {
          $push: {
            [scenepageList]: { $each: [pageList], $position: targetIndex }
          }
        }
      )
      ctx.body = getResponseBody(componentData)
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screen/orderScene error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }
  // 清空大屏配置
  async clearConfig() {
    const { ctx } = this
    const id = ctx.query.screenId
    await ctx.service.screen.update({ id }, { layerTree: [] })
    const data = await ctx.service.component.deleteMany({ screenId: id })
    ctx.body = getResponseBody(data)
  }
  // 分页获取大屏列表
  async getScreenList() {
    const { ctx } = this
    const {
      pageSize,
      page,
      projectId,
      workspaceId,
      query,
      platform,
      resolution,
      sortField
    } = ctx.request.body
    const filter = {
      workspaceId,
      isScreentpl: false,
      isDynamicScreen: false,
      screenType: { $in: ['scene', 'common'] }
    }
    const screenObj = {}
    const screenIds = []
    if (projectId) filter.projectId = projectId
    if (query) {
      filter.name = new RegExp(query, 'i')
    }
    if (platform) {
      filter.type = platform
    }
    if (resolution) {
      const width = Number(resolution.split('*')[0])
      const height = Number(resolution.split('*')[1])
      if (!isNaN(width) && !isNaN(height)) {
        filter['config.width'] = width
        filter['config.height'] = height
      }
    }
    let screens = []
    if (isNaN(Number(page))) {
      screens = await ctx.service.screen.find(filter, {})
    } else {
      screens = await ctx.service.screen.paginfind(
        filter,
        {},
        page,
        pageSize,
        sortField
      )
    }
    const promises = []
    screens.forEach((screenItem, index) => {
      screenItem = screenDataTransfer(screenItem.toObject())
      if (!screenItem.coeditId) {
        screens[index] = screenItem
        return
      }
      promises.push(
        ctx.service.screencoedit
          .getCoeditInfoByCoeditId({
            coeditId: screenItem.coeditId
          })
          .then(coeditInfo => {
            screenItem.coeditInfo = coeditInfo
            screens[index] = screenItem
          })
      )
    })
    if (promises.length) await Promise.all(promises)
    const screenCoeditProjection = {
      _id: 0,
      __v: 0,
      createdAt: 0,
      updatedAt: 0
    }
    console.log(
      page === 1 || isNaN(Number(page)),
      'page === 1 || isNaN(Number(page))'
    )
    if (page === 1 || isNaN(Number(page))) {
      // 只在首页去加载协同编辑的大屏
      // 处理别人共享给我的大屏
      // 根据workspaceId、projectId找出协同列表
      const coeditFilter = { workspaceId }
      if (projectId) coeditFilter.projectId = projectId
      const screenCoeditList = await ctx.service.screencoedit.find(
        coeditFilter,
        screenCoeditProjection
      )

      if (screenCoeditList.length) {
        // 找到别人共享给我的大屏列表
        const screenCoeditFilter = _.pick(filter, [
          'name',
          'query',
          'type',
          'config.width',
          'config.height'
        ])
        const keys = Object.keys(screenCoeditFilter).filter(
          k => screenCoeditFilter[k] != ''
        )
        const coeditScreens =
          await ctx.service.screencoedit.getCoeditScreensScreenCoeditList({
            screenCoeditList,
            projectId,
            filter: _.pick(screenCoeditFilter, keys)
          })

        // console.log('coeditScreens', coeditScreens)

        if (coeditScreens.length) {
          // 简单做一下合并
          screens = coeditScreens
            .map(item => screenDataTransfer(item))
            .concat(screens)
        }
      }
    }
    screens = screens.map(screen => {
      const screenData = screen
      const screenId = screen.id
      screenObj[screenId] = screenData
      screenIds.push(screenId)
      return screenData
    })
    const shareDatas = await ctx.service.screenshare.find({
      screenId: { $in: screenIds }
    })
    shareDatas.forEach(shareData => {
      const screenData = screenObj[shareData.screenId]
      if (screenData) {
        screenData.isPublic = shareData.isPublic
        screenData.shareUrl = shareData.shareUrl
      }
    })
    ctx.body = getResponseBody(screens)
  }
  async addvariableList() {
    const { ctx } = this
    const id = ctx.query.screenId
    const body = ctx.request.body
    const data = await ctx.service.screen.update(
      { id },
      {
        $addToSet: { variableList: body }
      }
    )
    ctx.body = getResponseBody(data)
  }
  async variableList() {
    const { ctx } = this
    const id = ctx.query.screenId
    const data = await ctx.service.screen.findOne({ id })
    ctx.body = getResponseBody({ variableList: data.variableList || [] })
  }
  async deletevariableList() {
    const { ctx } = this
    const id = ctx.query.screenId
    const _id = ctx.query.variableId
    const data = await ctx.service.screen.update(
      { id },
      { $pull: { variableList: { _id } } }
    )
    ctx.body = getResponseBody(data)
  }
  async updatevariableList() {
    const { ctx } = this
    const id = ctx.query.screenId
    const _id = ctx.query.variableId
    const body = ctx.request.body
    const data = await ctx.service.screen.update(
      {
        id,
        'variableList._id': _id
      },
      {
        $set: {
          'variableList.$.name': body.name,
          'variableList.$.content': body.content,
          'variableList.$.type': body.type,
          'variableList.$.description': body.description
        }
      }
    )
    ctx.body = getResponseBody(data)
  }
  // 添加全局变量
  // 获取大屏内的组件联动数据
  async getscreenlinkAge() {
    const { ctx } = this
    const screenId = ctx.query.screenId
    const data = await ctx.service.screen.getscreenlinkAge(screenId)
    ctx.body = getResponseBody(data)
  }
}

module.exports = ScreenController
