'use strict'
const Controller = require('egg').Controller
const {
  getResponseSuccess,
  getResponseError,
  getR<PERSON>ponseList,
  getLoggerModel,
  getResponseBody
} = require('../extend/utils')
const { randomId } = require('../extend/utils')
const { screenDataTransfer } = require('../utils/screen')
const {
  getScreenCoeditRoomsRedisKey,
  getScreenCoeditRootRoom
} = require('../utils/coedit')
const _ = require('lodash')

/**
 * @Controller 协同共享编辑
 */
class screenCoeditController extends Controller {
  /**
   * @Summary 分页查询协同列表
   * @Description 分页查询协同列表
   * @Router get /screencoedit/list
   * @Request query number page 页码 默认 1
   * @Request query number pageSize 单页数量 默认 20
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async list() {
    const { ctx } = this
    const query = ctx.request.query

    const page = Number(query.page)
    const pageSize = Number(query.pageSize)

    try {
      const list = await ctx.service.screencoedit.find(
        {},
        {},
        {
          skip: (page - 1) * pageSize,
          limit: pageSize
        }
      )
      const total = await ctx.service.screencoedit.count()

      ctx.body = getResponseSuccess({
        data: getResponseList({
          list,
          page,
          pageSize,
          total
        })
      })
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screencoedit/list error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  /**
   * @Summary 创建、保存协同编辑共享信息
   * @Description 创建、保存协同编辑共享信息
   * @Router POST /screencoedit/save
   * @Request body postScreenCoeditSaveBody *body
   * @Request header string sysuserid 用户ID
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async save() {
    const { ctx, app } = this
    let body = ctx.request.body
    // const screenId = body.coeditScreenId
    // let res = []
    // const data = await ctx.service.screencoedit.save(body)
    // res.push(data)
    // const screenlist = await ctx.service.screen.find({
    //   parentId: Number(screenId),
    //   screenType: 'child'
    // })
    // if (screenlist && screenlist.length > 0) {
    //   for (let index = 0; index < screenlist.length; index++) {
    //     const screen = screenlist[index]
    //     body.coeditScreenId = screen.id
    //     if (body.coeditId) {
    //       body.coeditId = screen.coeditId
    //     }
    //     const data = await ctx.service.screencoedit.save(body)
    //     res.push(data)
    //   }
    // }
    // ctx.body = getResponseBody(res)
    // return
    // 参数校验
    const errors = app.validator.validate(
      {
        // 协同ID
        coeditId: {
          type: 'number',
          required: false
        },
        // 协同大屏ID
        coeditScreenId: {
          type: 'number',
          required: true
        }
      },
      body
    )
    // 参数校验
    if (errors && errors.length) {
      ctx.body = getResponseError({
        message: JSON.stringify(errors)
      })
      return
    }

    const coeditId = body.coeditId
    const coeditScreenId = body.coeditScreenId
    const createUserId = ctx.header.sysuserid
    // 是否是更新
    const isUpdate = !!coeditId

    //添加用户分组功能start
    let userlist = body.userList || []
    const rolelist = body.roleList || []
    const grouplist = body.groupList || []
    for (let g = 0; g < grouplist.length; g++) {
      const group = grouplist[g]
      const data = await ctx.service.user.getDmcGroupUserList(group.group_id)
      let groupUserList = data.map(item => {
        return {
          role: group.role,
          userName: item.name,
          userId: item.user_id
        }
      })
      userlist = userlist.concat(groupUserList)
    }

    for (let r = 0; r < rolelist.length; r++) {
      const role = rolelist[r]
      const data = await ctx.service.user.getDmcRoleUserList(role.role_id)
      let roleUserList = data.map(item => {
        return {
          role: role.role,
          userName: item.name,
          userId: item.user_id
        }
      })
      userlist = userlist.concat(roleUserList)
    }
    userlist = _.uniqBy(userlist, 'userId')

    //添加用户分组功能start

    const coeditUsers = userlist || []
    console.log(!coeditUsers || !coeditUsers.length, 'dsadsasd------')
    const shareCollection = {
      userList: body.userList || [],
      roleList: body.roleList || [],
      groupList: body.groupList || []
    }
    await ctx.service.screen.update(
      {
        id: coeditScreenId
      },
      {
        shareCollection
      }
    )
    // 通过用户ID获取未分组
    const getDefaultProjectByUserId = async userId => {
      const key = `defaultProject-${userId}`

      let defaultProject = await ctx.service.cacheservice.get(key)

      if (defaultProject) {
        return defaultProject
      }

      // 通过userId查找工作目录ID
      let currWorkspace = await ctx.service.workspace.findOne({
        userId: userId
      })

      if (!currWorkspace) {
        currWorkspace = await ctx.service.workspace.createOrigin({
          name: userId + '的工作空间',
          userId: userId,
          type: 0
        })
      }

      // 查找出未分组
      defaultProject = await ctx.service.project.findOne({
        type: 0,
        workspaceId: currWorkspace.id
      })

      // 兼容找不到的情况，创建
      if (!defaultProject) {
        defaultProject = await ctx.service.project.create({
          type: 0,
          name: '未分组',
          workspaceId: currWorkspace.id
        })
      }
      ctx.service.cacheservice.set(key, defaultProject, 86400)

      return defaultProject
    }

    // 通过用户信息，获取协同编辑每一项
    const getScreenCoeditItemByUser = async ({ user, coeditId }) => {
      const defaultProject = await getDefaultProjectByUserId(user.userId)
      return {
        coeditId,
        coeditScreenId,
        createUserId,
        coeditUserId: user.userId,
        coeditRole: user.role,
        projectId: defaultProject.id,
        workspaceId: defaultProject.workspaceId
      }
    }

    // 通过 coeditUsers 获取新的协同编辑列表
    const getNewScreenCoeditListByCoeditUsers = async ({
      coeditUsers,
      coeditId
    }) => {
      const addList = []
      const promises = []

      for (let index = 0; index < coeditUsers.length; index++) {
        const user = coeditUsers[index]
        promises.push(
          getScreenCoeditItemByUser({
            user,
            coeditId
          }).then(screenCoeditItem => {
            addList.push(screenCoeditItem)
          })
        )
      }

      await Promise.all(promises)

      return addList
    }

    // 更新大屏coeditId
    const updateScreenCoeditId = async (screeenId, coeditId) => {
      // 更新screen表的coeditId字段
      return await ctx.service.screen.findOneAndUpdate(
        {
          id: screeenId
        },
        {
          coeditId: coeditId
        }
      )
    }

    try {
      // 新建
      if (!isUpdate) {
        if (!coeditUsers || coeditUsers.length === 0) {
          // ctx.body = getResponseBody(
          //   null,
          //   false,
          //   '该角色或组织下没有添加用户',
          //   402
          // )
          ctx.body = getResponseError({ message: '该角色或组织下没有添加用户' })
          return
        }
        const coeditId = randomId()

        const addList = await getNewScreenCoeditListByCoeditUsers({
          coeditUsers,
          coeditId
        })

        // 批量创建协同编辑
        await ctx.service.screencoedit.insertMany(addList)

        // 更新screen表的coeditId字段
        await updateScreenCoeditId(coeditScreenId, coeditId)

        // 添加协同编辑消息
        ctx.service.screencoedit.insertScreenCoeditMsgList({
          coeditId,
          coeditScreenId,
          createUserId,
          screenCoeditList: addList,
          action: 'add'
        })
      } else {
        // 更新
        // 根据 coeditId 找出协同编辑列表
        const list = await ctx.service.screencoedit.find({
          coeditId: coeditId
        })

        // 找出新增的用户
        const newCoeditUsers = []

        // 找出需要更新的列表
        const updateList = []

        for (let index = 0; index < coeditUsers.length; index++) {
          const user = coeditUsers[index]
          const i = list.findIndex(item => {
            return item.coeditUserId === user.userId
          })

          if (i === -1) {
            // 新增的用户
            newCoeditUsers.push(user)
          } else if (list[i].coeditRole !== user.role) {
            // 如果角色改变，则要更新
            updateList.push({
              ...list[i].toObject(),
              coeditRole: user.role
            })
          }
        }

        // promises
        const promises = []

        // 新增动作
        const addAction = async () => {
          // 执行数据库新增
          const addList = await getNewScreenCoeditListByCoeditUsers({
            coeditUsers: newCoeditUsers,
            coeditId: coeditId
          })
          // 批量创建协同编辑
          promises.push(ctx.service.screencoedit.insertMany(addList))

          // 添加协同编辑消息
          ctx.service.screencoedit.insertScreenCoeditMsgList({
            coeditId,
            coeditScreenId,
            createUserId,
            screenCoeditList: addList,
            action: 'add'
          })
        }

        // 更新动作
        const updateAction = async () => {
          for (let index = 0; index < updateList.length; index++) {
            const item = updateList[index]
            promises.push(
              // 更新协同编辑的数据
              ctx.service.screencoedit.updateMany(
                {
                  coeditId: coeditId,
                  coeditUserId: item.coeditUserId
                },
                {
                  $set: {
                    coeditRole: item.coeditRole
                  }
                }
              )
            )
          }

          // 添加协同编辑消息
          ctx.service.screencoedit.insertScreenCoeditMsgList({
            coeditId,
            coeditScreenId,
            createUserId,
            screenCoeditList: updateList,
            action: 'update'
          })
        }

        // 删除动作
        const removeAction = () => {
          // 找到要删除的列表
          const removeList = []
          for (let index = 0; index < list.length; index++) {
            const item = list[index]
            const i = coeditUsers.findIndex(user => {
              return item.coeditUserId === user.userId
            })
            if (i === -1) {
              removeList.push(item)
            }
          }

          // 执行数据库删除
          if (removeList.length) {
            const removeCoeditUserIds = removeList.map(item => {
              return item.coeditUserId
            })

            const remove = async () => {
              return new Promise((resolve, reject) => {
                // 删除协同编辑
                ctx.service.screencoedit
                  .deleteMany({
                    coeditId: coeditId,
                    coeditUserId: { $in: removeCoeditUserIds }
                  })
                  .then(async () => {
                    // 删除之后判断是否还有协同的用户
                    const count = await ctx.service.screencoedit.count({
                      coeditId
                    })

                    // 没有协同用户了
                    if (count === 0) {
                      // 更新screen表的coeditId字段为null
                      await updateScreenCoeditId(coeditScreenId, null)
                      resolve()
                      return
                    }

                    resolve()
                  })
                  .catch(() => {
                    reject()
                  })
              })
            }

            // 添加到promise
            promises.push(remove())

            // 添加协同编辑消息
            ctx.service.screencoedit.insertScreenCoeditMsgList({
              coeditId,
              coeditScreenId,
              createUserId,
              screenCoeditList: removeList,
              action: 'remove'
            })
          }
        }

        // 协同编辑新增
        if (newCoeditUsers.length) {
          await addAction()
        }

        // 协同编辑更新
        if (updateList.length) {
          updateAction()
        }

        // 协同编辑删除
        removeAction()

        await Promise.all(promises)
      }

      ctx.body = getResponseSuccess({ data: null })
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screencoedit/save error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  /**
   * @Summary 移动到分组
   * @Description 移动到分组
   * @Router post /screencoedit/move
   * @Request body postScreencoeditMoveBody *body
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async move() {
    const { ctx, app } = this
    const body = ctx.request.body
    const coeditId = body.coeditId
    const userId = ctx.header.sysuserid

    // 参数校验
    const errors = app.validator.validate(
      {
        // 协同ID
        coeditId: {
          type: 'number',
          required: true
        },
        // 项目分组ID
        projectId: {
          type: 'number',
          required: true
        }
      },
      body
    )
    // 参数校验
    if (errors && errors.length) {
      ctx.body = getResponseError({
        message: JSON.stringify(errors)
      })
      return
    }

    try {
      const data = await ctx.service.screencoedit.update(
        {
          coeditId,
          coeditUserId: userId
        },
        {
          projectId: body.projectId
        }
      )
      ctx.body = getResponseSuccess({ data })
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screencoedit/move error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  // 删除协同
  async delete() {
    const { ctx, app } = this
    const query = ctx.request.query
    const coeditScreenId = query.coeditScreenId

    // 参数校验
    const errors = app.validator.validate(
      {
        // 协同大屏ID
        coeditScreenId: {
          type: 'string',
          required: true
        }
      },
      query
    )
    // 参数校验
    if (errors && errors.length) {
      ctx.body = getResponseError({
        message: JSON.stringify(errors)
      })
      return
    }

    try {
      const data = await ctx.service.screencoedit.deleteMany({ coeditScreenId })

      await ctx.service.screen.findOneAndUpdate(
        {
          id: coeditScreenId
        },
        {
          coeditId: null
        }
      )
      ctx.body = getResponseSuccess({ data })
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screencoedit/delete error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  /**
   * @Summary 分享给我的协同编辑
   * @Description 分享给我的协同编辑
   * @Router get /screencoedit/sharedToMe
   * @Response 200 successResponse ok
   * @Response 400 errorResponse error
   */
  async sharedToMe() {
    const { ctx } = this
    const userId = ctx.request.header.sysuserid

    const screencoeditProjection = {
      _id: 0,
      __v: 0,
      createdAt: 0,
      updatedAt: 0
    }

    try {
      const screenCoeditList = await ctx.service.screencoedit.find(
        {
          coeditUserId: userId
        },
        screencoeditProjection
      )

      // 找到别人共享给我的大屏列表
      let coeditScreens =
        await ctx.service.screencoedit.getCoeditScreensScreenCoeditList({
          screenCoeditList
          // projectId: project.id
        })

      coeditScreens = coeditScreens.map(screen => {
        return screenDataTransfer(screen)
      })

      ctx.body = getResponseSuccess({ data: coeditScreens })
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screencoedit/sharedToMe error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  // 更新
  // async update() {
  //   const { ctx } = this
  //   const id = ctx.query.id
  //   const body = ctx.request.body
  //   const data = await ctx.service.screencoedit.update({ id }, body)
  //   ctx.body = getResponseSuccess({ data })
  // }

  /**
   * @Summary 检查加入大屏
   * @Description 检查加入大屏
   * @Router get /screencoedit/checkJoinScreen
   * @Request query string screenId 大屏ID
   * @Request query string screenType 大屏类型
   * @Response 200 checkJoinScreenSuccessResponse ok
   * @Response 400 errorResponse error
   */
  async checkJoinScreen() {
    const { ctx, app } = this
    const { screenId, screenType } = ctx.request.query

    // 参数校验
    const errors = app.validator.validate(
      {
        // 大屏ID
        screenId: {
          type: 'string',
          required: true
        },
        // 大屏类型
        screenType: {
          type: 'string',
          required: true
        }
      },
      ctx.request.query
    )
    // 参数校验
    if (errors && errors.length) {
      ctx.body = getResponseError({
        message: JSON.stringify(errors)
      })
      return
    }

    // 是否是场景大屏
    const isSceneScreen = screenType === 'scene'

    // 房间名
    const room = `screen-editor-${screenId}`

    try {
      const rootRoom = await getScreenCoeditRootRoom(
        this.service.cacheservicedb1,
        getScreenCoeditRoomsRedisKey(this.config.env)
      )

      if (!rootRoom.screenRooms[room]) {
        rootRoom.screenRooms[room] = []
      }

      const editorNsp = ctx.app.io.of('/editor')

      const coeditUsers = []

      // 如果这个房间已存在
      if (editorNsp.adapter.rooms[room]) {
        const sockets = editorNsp.adapter.rooms[room].sockets

        for (const id in sockets) {
          const data = rootRoom.screenRooms[room].find(item => {
            return item.socketId === id
          })

          if (!data) {
            ctx.logger.info(
              getLoggerModel({
                message: '找不到大屏房间数据',
                data: {
                  room: room,
                  screenRooms: rootRoom.screenRooms,
                  socketId: id
                }
              })
            )
            continue
          }

          const item = {
            userId: data.userId,
            userName: data.userName,
            socketId: data.socketId
          }

          if (isSceneScreen) {
            item.pageId = ''
            item.pageName = ''
          }
          coeditUsers.push(item)
        }

        // 场景大屏，加入pageId返回
        if (isSceneScreen) {
          // 房间分组
          const scenePageRoomGroupName = `screen-editor-${screenId}`

          for (const key in rootRoom.scenePageRoomGroups[
            scenePageRoomGroupName
          ]) {
            const list =
              rootRoom.scenePageRoomGroups[scenePageRoomGroupName][key]

            if (!list || !list.length) {
              ctx.logger.info(
                getLoggerModel({
                  message: '找不到场景大屏页面房间数据',
                  data: {
                    key: key,
                    scenePageRooms: rootRoom.scenePageRooms
                  }
                })
              )
              continue
            }
            const item = list[0]

            const index = coeditUsers.findIndex(user => {
              return user.userId === item.userId
            })

            if (index !== -1) {
              coeditUsers[index].pageId = item.pageId
              coeditUsers[index].pageName = item.pageName
            }
          }
        }
      }

      const res = {
        screenId,
        screenType,
        coeditUsers
      }

      ctx.body = getResponseSuccess({
        data: res
      })
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/screencoedit/checkJoinScreen error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  // 获取 screenCoeditRooms，测试使用
  async getScreenCoeditRooms() {
    const { ctx, app } = this
    const rootRoom = await getScreenCoeditRootRoom(
      this.service.cacheservicedb1,
      getScreenCoeditRoomsRedisKey(this.config.env)
    )

    ctx.body = getResponseSuccess({
      data: rootRoom
    })
  }
}

module.exports = screenCoeditController
