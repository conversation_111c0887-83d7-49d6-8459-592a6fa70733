'use strict'
// 自定义组件
const fs = require('fs-extra')
const path = require('path')
const Controller = require('egg').Controller
const { getResponseBody, getScreenshot, getDate } = require('../extend/utils')
const { spawnSync, exec } = require('child_process')
const base64 = require('base-64')
const compressing = require('compressing')

class CustomComponentController extends Controller {
  async index() {
    const { ctx } = this
    const data = await ctx.service.customcomponent.find()
    ctx.body = getResponseBody(data)
  }
  async info() {
    const { ctx } = this
    const id = ctx.query.id
    const data = await ctx.service.customcomponent.findOne({ id })
    ctx.body = getResponseBody(data)
  }
  async create() {
    const { ctx } = this
    const body = ctx.request.body
    const data = await ctx.service.customcomponent.create(body)
    ctx.body = getResponseBody(data)
  }
  async update() {
    const { ctx } = this
    const id = ctx.query.id
    const body = ctx.request.body
    const data = await ctx.service.customcomponent.update({ id: id }, body)
    ctx.body = getResponseBody(data)

    // 记录自定义组件更新日志，出问题要找这个人
    ctx.logger.info(
      JSON.stringify({
        message: 'customcomponent update success',
        data: {
          userId: ctx.request.header.sysuserid,
          customcomponentId: id,
          customcomponentName:
            body.compConfig && body.compConfig.chartConfig
              ? body.compConfig.chartConfig.cn_name
              : '',
          date: getDate()
        }
      })
    )
  }
  async download() {
    const { ctx } = this
    const id = ctx.query.id
    const data = await ctx.service.customcomponent.findOne({ id: id })

    ctx.body = getResponseBody(data)
  }
  async delete() {
    const { ctx } = this
    const ids = ctx.request.body.ids
    if (ids) {
      for (let index = 0; index < ids.length; index++) {
        const element = ids[index]
        await ctx.service.customcomponent.delete({ id: element })
      }
    }
    ctx.body = getResponseBody(ids)
  }

  async createComScreenshot() {
    const { ctx, config } = this
    const body = ctx.request.body
    const comId = body.comId
    let comUrl =
      body.comUrl ||
      '/public/personal/system/common/G7RI95ETwATiMownAvBfUw==.png'
    const iconPath = path.resolve(
      config.resourcePath,
      './templates/test/static/icon.png'
    )
    let newcomUrl = comUrl.split('/public')[1]
    const comIcon = path.resolve(config.resourcePath, `.${newcomUrl}`)
    fs.copySync(comIcon, iconPath)
    await ctx.service.customcomponent.update(
      { id: comId },
      { customIcon: comUrl }
    )
    ctx.body = getResponseBody({ comIcon })
  }
  async packCom() {
    const { ctx, config } = this
    const id = ctx.request.body.id
    const serverIp = ctx.request.body.serverIp
    const data = await ctx.service.customcomponent.findOne({ id })
    const templatePath = path.resolve(config.resourcePath, './templates/test')
    const date = new Date()
    const datestr = `${date.getFullYear()}-${date.getMonth() + 1}-${
      date.getDate() + 1
    }`
    const compackagesName = path.resolve(
      config.resourcePath,
      `./commonfiles/${data.compConfig.chartConfig.cn_name}_${base64.encode(
        serverIp
      )}_${datestr}`
    )
    if (fs.existsSync(`${compackagesName}`)) {
      fs.removeSync(`${compackagesName}`)
    }
    fs.copySync(templatePath, compackagesName)
    await ctx.service.customcomponent.packCom(data, compackagesName)
    process.chdir(compackagesName)
    const execstr = 'seatom package ' + serverIp
    // spawnSync('seatom', ['package', serverIp], { stdio: 'inherit', timeout: 3000 });
    const res = new Promise(function (resolve, reject) {
      exec(execstr, (error, stdout, stderr) => {
        if (error) {
          console.error(`exec error: ${error}`)
          reject({
            error: `exec error: ${error}`,
            stdout,
            stderr,
            message: '打包失败'
          })
          return
        }
        resolve({
          stdout,
          stderr,
          message: '打包成功'
        })
      })
    })
    try {
      const success = await res
      const comtgz = `${compackagesName}/${data.compConfig.chartConfig.cn_name}_${datestr}`
      fs.moveSync(`${compackagesName}/build`, comtgz)
      await compressing.tgz.compressDir(comtgz, `${comtgz}.tgz`)
      ctx.attachment(`${comtgz}.tgz`)
      ctx.set('Content-Type', 'application/octet-stream')
      ctx.body = fs.createReadStream(`${comtgz}.tgz`)
      // ctx.body = getResponseBody({ fileData: success });
    } catch (error) {
      // const err = error
      ctx.body = getResponseBody(
        { fileData: error },
        false,
        '打包下载失败',
        444
      )
    }
  }
  async publish() {
    const { ctx, config } = this
    const id = ctx.request.body.id
    const data = await ctx.service.customcomponent.findOne({ id })
    const templatePath = path.resolve(config.resourcePath, './templates/test')
    const publicPath = config.publicPath
    await ctx.service.customcomponent.packCom(data, templatePath)
    let buildip, publiship
    process.chdir(templatePath)
    buildip = ''
    if (fs.existsSync(`${templatePath}/build`)) {
      fs.removeSync(`${templatePath}/build`)
    }
    publiship = `${config.protocol}://${config.intranetIp}:${config.cluster.listen.port}${publicPath}`
    spawnSync('seatom', ['config', 'set', 'server', publiship], {
      stdio: 'inherit',
      timeout: 3000
    })
    const buildResult = new Promise(function (resolve, reject) {
      exec(
        `seatom package ${buildip} && seatom publish-prod`,
        (error, stdout, stderr) => {
          if (error) {
            console.error(`exec error: ${error}`)
            reject({
              error: `exec error: ${error}`,
              stdout,
              stderr,
              message: '打包发布失败'
            })
            return
          }
          resolve({
            stdout,
            stderr,
            message: '打包发布成功'
          })
        }
      )
    })
    try {
      const buildRes = await buildResult
      ctx.body = getResponseBody({ fileData: buildRes })

      // 记录自定义组件发布日志，出问题要找这个人
      ctx.logger.info(
        JSON.stringify({
          message: 'customcomponent publish success',
          data: {
            userId: ctx.request.header.sysuserid,
            customcomponentId: id,
            customcomponentName:
              data.compConfig && data.compConfig.chartConfig
                ? data.compConfig.chartConfig.cn_name
                : '',
            date: getDate()
          }
        })
      )
    } catch (error) {
      // const err = error
      ctx.body = getResponseBody({ fileData: error }, false, '发布失败', 400)
    }
  }
}

module.exports = CustomComponentController
