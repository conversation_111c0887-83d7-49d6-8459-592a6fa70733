'use strict'

const Controller = require('egg').Controller
const { getResponseBody, compareVersion } = require('../extend/utils')
const _ = require('lodash')
const { ERROR_CODES, DASHBOARD_CODES } = require('../extend/constant')
class DataDashboardController extends Controller {
  async getProjectTree() {
    const { ctx } = this
    const queryParam = ctx.query
    const headerParam = ctx.header
    const config = Object.assign(queryParam, headerParam)
    const res = await ctx.service.datadashboard.getProjectTree(config)
    ctx.body = getResponseBody(res)
  }
  async getDashboardInfo() {
    const { ctx } = this
    const body = ctx.request.body
    const queryParam = ctx.query
    const headerParam = ctx.header
    const config = Object.assign(queryParam, headerParam, body)
    const res = await ctx.service.datadashboard.getDashboardInfo(config)
    ctx.body = getResponseBody(res)
  }
  async getChartData() {
    const { ctx } = this
    const body = ctx.request.body
    const queryParam = ctx.query
    const headerParam = ctx.header
    const config = Object.assign(queryParam, headerParam, body)
    const res = await ctx.service.datadashboard.getChartData(config)
    if (!res) {
      ctx.body = getResponseBody(null, false, '该仪表盘数据不可以导入', 400)
      return
    }
    ctx.body = getResponseBody(res)
  }
  async checkChartData() {
    const { ctx } = this
    const body = ctx.request.body
    const queryParam = ctx.query
    const headerParam = ctx.header
    const config = Object.assign(queryParam, headerParam, body)
    const res = await ctx.service.datadashboard.checkChartData(config)
    let comType = ''
    let comList = []
    // if (res.info && res.info.x && res.info.x.length === 0 && res.info.y && res.info.y.length === 1) {
    // }
    if (!res.info || !res.info.x || !res.info.y) {
      ctx.body = getResponseBody(null, false, '该仪表盘数据不可以导入', 400)
      return
    }
    // if (res.info.x.length > 1 || res.info.y.length < 1) {
    //   ctx.body = getResponseBody(null, false, "该仪表盘数据不可以导入", 400);
    //   return
    // }
    // const projection = {
    //   createdAt: 0
    // }
    // const packdata = (await ctx.service.package.find({}, projection)).map(d => d.toJSON());
    // packdata.sort((a, b) => {
    //   const res = compareVersion(a.version, b.version);
    //   return -res;
    // });
    // const list = _.uniqBy(packdata, 'name');
    // // const list = _.uniqBy(data, 'name');
    // const pkgMap = _.keyBy(list, 'name');

    // // 处理子组件
    // const children = list.reduce((arr, item) => {
    //   if (Array.isArray(item.children) && item.children.length) {
    //     arr.push(...item.children);
    //   }
    //   return arr;
    // }, []);
    // const uniqueChildren = _.uniq(children).map(d => ({ name: d }));  // 去重
    // const newList = _.pullAllBy(list, uniqueChildren, 'name');
    // for (let pkg of newList) {
    //   pkg.children = pkg.children && pkg.children.length ? pkg.children.map(childName => pkgMap[childName]).filter(d => d != null) : [];
    // }
    // let newList1 = newList.map(item => {
    //   const config = JSON.parse(item.config)
    //   // 获取两个数值的
    //   if (config && config.data.source.length >= 1) {
    //     if (Object.keys(config.data.source[0]).length >= 4) {
    //       return {
    //         name: item.name,
    //         source: config.data.source[0],
    //         alias: item.alias
    //       }
    //     }
    //   }
    // })
    // newList1 = newList1.filter(item => {
    //   return item !== undefined
    // })
    // 多个维度多个数值
    const data4 = [
      {
        name: 'data-set-listSwiper',
        source: {
          第一列: '行1列1',
          第二列: '行1列2',
          '第三列(万人)': 123,
          name: 'name1'
        },
        alias: '轮播列表'
      },
      {
        name: 'data-set-table',
        source: {
          name: '姓名',
          phoneNumber: '手机号',
          type: '类别',
          adress: '地址'
        },
        alias: '表格'
      }
    ]
    // 一个维度多个数值的组件
    const data3 = [
      {
        name: 'regular-bar-doubleWayBar',
        source: { x: '健康权', y: 19.7, s: '去年' },
        alias: '双向柱状图'
      },
      {
        name: 'regular-bar-group',
        source: { s: '1', x: '2016', y: 826 },
        alias: '分组柱状图'
      },
      {
        name: 'regular-bar-groupHor',
        source: { s: '1', x: '2016', y: 826 },
        alias: '水平分组柱状图'
      },
      {
        name: 'regular-bar-stack',
        source: { s: '1', x: '2016', y: 826 },
        alias: '堆叠柱状图'
      },
      {
        name: 'regular-bar-stackHor',
        source: { s: '1', x: '2016', y: 826 },
        alias: '堆叠水平柱状图'
      },
      {
        name: 'regular-bar-zebra-normal',
        source: { s: '1', x: '2016', y: 826 },
        alias: '斑马柱状图'
      },
      {
        name: 'regular-line-mult',
        source: { s: '1', x: '科信', y: 5 },
        alias: '多折线图'
      },
      {
        name: 'regular-line-stackArea',
        source: { s: '1', x: '科信', y: 5 },
        alias: '堆叠面积图'
      },
      {
        name: 'regular-line-zebraArea',
        source: { s: '1', x: '科信', y: 5 },
        alias: '斑马面积图'
      },
      {
        name: 'regular-mult-lineBarChart',
        source: { s: '1', x: '2016', y: 826 },
        alias: '折线柱状图'
      },
      {
        name: 'regular-other-manyFunnel',
        source: { s: '系列1', value: 60, name: '访问' },
        alias: '多维度漏斗图'
      },
      {
        name: 'regular-other-radar',
        source: { s: '系列一', text: '治安警察', mount: 28 },
        alias: '基本雷达图'
      },
      {
        name: 'regular-scatter-normal',
        source: { s: '1', x: '20', y: 826 },
        alias: '基本散点图'
      },
      {
        name: 'regular-scatter-singleValueScatter',
        source: { s: '1', x: '2016', y: 526 },
        alias: '单值气泡图'
      }
    ]
    // 一个维度1个数值的组件
    const data2 = [
      { name: 'bubble3D', source: { x: '普货', y: 5 }, alias: '3D气泡图' },
      {
        name: 'data-text-cloud',
        source: { name: '花鸟市场', value: 1446 },
        alias: '词云'
      },
      {
        name: 'data-text-cloud-number',
        source: { name: '酒店', value: '560' },
        alias: '数字气泡词云组件'
      },
      {
        name: 'regular-bar-falls',
        source: { x: '周一', y: 120 },
        alias: '瀑布柱状图'
      },
      {
        name: 'regular-bar-normal',
        source: { x: '2016', y: 826 },
        alias: '普通柱状图'
      },
      {
        name: 'regular-bar-normal25D',
        source: { x: '武汉市', y: 2012 },
        alias: '2.5D柱状图'
      },
      {
        name: 'regular-bar-normalHor',
        source: { x: '治安', y: 22 },
        alias: '水平柱状图'
      },
      {
        name: 'regular-bar-pictor',
        source: { x: '广东', y: 11.07 },
        alias: '象形柱状图'
      },
      {
        name: 'regular-line-area',
        source: { x: '普货', y: 5 },
        alias: '基本面积图'
      },
      {
        name: 'regular-line-normal',
        source: { x: '2016', y: 826 },
        alias: '基本折线图'
      },
      {
        name: 'regular-pie-donut',
        source: { name: '直接访问', value: 10 },
        alias: '环形饼图'
      },
      {
        name: 'regular-pie-normal',
        source: { name: '广东省', value: 100 },
        alias: '基本饼图'
      },
      {
        name: 'regular-pie-rotate',
        source: { name: '广东省', value: 100 },
        alias: '轮播饼图'
      },
      {
        name: 'regular-pie-zebra-donut',
        source: { name: '直接访问', value: 10 },
        alias: '斑马环图'
      }
    ]
    const x = res.info.x
    const y = res.info.y.concat(res.info.compare_axis)
    if (res.info && x && x.length === 0 && y && y.length === 1) {
      switch (res.info.chart_type) {
        case 'C310':
          comType = 'data-indicator-shortSingleHor' // 短横幅
          break
        case 'C240':
          comType = 'regular-bar-normalHor' // 水平柱状图
          break
        case 'C210':
          comType = 'regular-bar-normal' // 柱状图
          break
        case 'C230':
          comType = 'regular-pie-normal' // 普通饼图
          break
        case 'C261':
          comType = 'data-indicator-shortSingleHor' // 短横幅
          break
        default:
          comType = ''
          break
      }
    } else if (res.info && x && x.length === 1 && y && y.length >= 2) {
      comList = data3
      switch (res.info.chart_type) {
        case 'C200':
          comType = 'data-set-table' // 多折线图
          break
        case 'C220':
          comType = 'regular-line-mult' // 多折线图
          break
        case 'C210':
          comType = 'regular-bar-group' // 分组柱状图
          break
        case 'C240':
          comType = 'regular-bar-groupHor' // 水平分组柱状图
          break
        case 'C280':
          comType = 'regular-scatter-normal' // 基本散点图
          break
        case 'C350':
          comType = 'regular-line-stackArea' // 堆叠面积图
          break
        case 'C351':
          comType = 'regular-line-stackArea' // 基本散点图
          break
        case 'C243':
          comType = 'regular-bar-doubleWayBar' // 基本散点图
          break
        case 'C290':
          comType = 'regular-other-radar' // 基本散点图
          break
        default:
          comType = ''
          break
      }
    } else if (res.info && x && x.length >= 2 && y && y.length >= 1) {
      comList = data4
      comType = 'data-set-table'
    } else if (res.info && x && x.length >= 1 && y && y.length === 0) {
      comList = data4
      comType = 'data-set-table'
    } else if (res.info && x && x.length === 0 && y && y.length > 1) {
      comList = data4
      comType = 'data-set-table'
    } else if (res.info && x && x.length === 1 && y && y.length === 1) {
      comList = data2
      comType = DASHBOARD_CODES[res.info.chart_type]
        ? DASHBOARD_CODES[res.info.chart_type]
        : ''
    } else {
      comType = ''
    }
    const title = res.info.title || ''
    if (comType !== '') {
      ctx.body = getResponseBody({ title, comType, comList })
    } else {
      ctx.body = getResponseBody(null, false, '该仪表盘数据不可以导入', 400)
    }
  }
}

module.exports = DataDashboardController
