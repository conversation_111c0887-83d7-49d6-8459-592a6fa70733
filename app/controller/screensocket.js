/*
 * @Description: ScreenShareController
 * @Date: 2022-09-13 16:22:52
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
const _ = require('lodash')
const md5 = require('md5')

class ScreenShareController extends Controller {
  async index() {
    const { ctx, config } = this
    const screenId = ctx.query.screenId
    const screensocketData =
      await ctx.service.screensocket.getScreensocketByScreenId({ screenId })
    ctx.body = getResponseBody(screensocketData)
  }

  async verify() {
    const { ctx } = this
    let { screenId, password } = ctx.request.body
    const res = await ctx.service.screensocket.findOne({ screenId, password })
    if (res) {
      ctx.body = getResponseBody(true)
    } else {
      ctx.body = getResponseBody(false)
    }
  }

  async connect() {
    const { ctx } = this
    let { screenId } = ctx.request.body
    const res = await ctx.service.screensocket.findOne({ screenId })
    if (res.socketidList.length >= 2) {
      ctx.body = getResponseBody(true)
    } else {
      ctx.body = getResponseBody(false)
    }
  }

  async update() {
    const { ctx } = this
    const screenId = Number(ctx.query.screenId)
    const body = ctx.request.body
    const screensocketData = await ctx.service.screensocket.findOne({
      screenId
    })
    if (!screensocketData) {
      const password = parseInt(Math.random() * 1000000)
      body.password = password
      await ctx.service.screensocket.create({
        acceptSocketid: {},
        sendSocketid: {},
        waitSocketid: {},
        screenId,
        ...body
      })
    } else {
      await ctx.service.screensocket.update({ screenId, ...body })
    }
    const data = await ctx.service.screensocket.findOne({ screenId })
    await ctx.service.screen.update(
      { id: screenId },
      { isConnect: data.isConnect }
    )
    ctx.body = getResponseBody(data)
  }
}

module.exports = ScreenShareController
