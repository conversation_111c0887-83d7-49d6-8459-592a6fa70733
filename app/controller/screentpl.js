'use strict'
// 大屏模版
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
const { encryptPath } = require('../extend/crypto')
const _ = require('lodash')
class ScreentplController extends Controller {
  async index() {
    const { ctx } = this
    const body = ctx.query ? ctx.query : {}
    const screentpl = await ctx.service.screentpl.find({
      level: 0,
      type: 'mobile'
    })
    if (!screentpl.length) {
      await this.ctx.service.screentpl.create({
        name: 'mobile空白模版',
        type: 'mobile',
        level: 0
      })
    }
    let screentplDataArray = []
    if (body.type) {
      screentplDataArray = await ctx.service.screentpl.find({
        type: body.type,
        level: 1
      })
      const blank = await ctx.service.screentpl.find({
        type: body.type,
        level: 0
      })
      screentplDataArray.unshift(blank)
    } else {
      screentplDataArray = await ctx.service.screentpl.find(body)
    }

    const screentplData = JSON.parse(JSON.stringify(screentplDataArray))
    for (let i = 0, len = screentplData.length; i < len; i++) {
      if (screentplData[i].level) {
        const screenDataArray = await ctx.service.screen.findOne({
          id: screentplData[i].screenId
        })
        const screenData = JSON.parse(JSON.stringify(screenDataArray))
        if (screenData) {
          if (!screenData.config.encryptThumbnail) {
            const start =
              screenData.config['thumbnail'] &&
              screenData.config['thumbnail'].indexOf('/public/')
            const thumbnail =
              screenData.config['thumbnail'] &&
              screenData.config['thumbnail'].substring(start + 7).split('?')[0]
            const encryptThumbnail = thumbnail && encryptPath(thumbnail)
            screenData.config = Object.assign(screenData.config, {
              encryptThumbnail: encryptThumbnail || ''
            })
          } else {
            // const start = screenData.config['thumbnail'] && screenData.config['thumbnail'].indexOf('/public/');
            // const thumbnail = (screenData.config['thumbnail'] && screenData.config['thumbnail'].substring(start + 7).split('?')[0])
            // const encryptThumbnail = thumbnail && encryptPath(thumbnail);
            screenData.config.encryptThumbnail = null
          }
          screentplData[i].config = screenData.config
          screentplData[i].name = screenData.name
          await ctx.service.screentpl.update(
            { id: screentplData[i].id },
            { name: screenData.name }
          )
          screentplData[i].screenType = screenData.screenType
        }
      }
    }
    ctx.body = getResponseBody(screentplData)
  }

  async create() {
    const { ctx } = this
    const body = ctx.request.body
    const tag = body.tag || []
    body.isScreentpl = true
    delete body.tag
    if (body.level) {
      const screenData = await ctx.service.screen.create(body)
      const { name, type, level, id, description, size } = screenData
      const data = await ctx.service.screentpl.create({
        name,
        type,
        level,
        description,
        size,
        screenId: id,
        tag: tag
      })
      ctx.body = getResponseBody(data)
    } else {
      const data = await ctx.service.screentpl.create({
        name: body.name,
        type: body.type,
        level: body.level
      })
      ctx.body = getResponseBody(data)
    }
  }

  async delete() {
    const { ctx } = this
    const id = ctx.request.body.id
    const screenId = ctx.request.body.screenId
    await ctx.service.component.deleteMany({ screenId })
    await ctx.service.filter.deleteMany({ screenId })
    await ctx.service.screen.delete({ id: screenId })
    const data = await ctx.service.screentpl.delete({ id })
    ctx.body = getResponseBody(data)
  }

  async update() {
    const { ctx } = this
    const body = ctx.request.body
    const data = await ctx.service.screentpl.update({ id: ctx.query.id }, body)
    ctx.body = getResponseBody(data)
  }
}

module.exports = ScreentplController
