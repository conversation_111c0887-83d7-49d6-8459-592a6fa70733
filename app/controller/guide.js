'use strict'
const _ = require('lodash')
const fs = require('fs-extra')
const path = require('path')
const moment = require('moment')
const Controller = require('egg').Controller
const { getResponseBody, uuid } = require('../extend/utils')
const SeatomException = require('../exception/seatomException')
const { ERROR_CODES } = require('../extend/constant')
const html2md = require('html-to-md')

class GuideController extends Controller {
  // 上传文件
  async upload() {
    const { ctx, config } = this
    // todo: userId必须是管理员才能上传
    const userId = ctx.request.body && ctx.request.body.userId
    if (!ctx.request.files || !ctx.request.files.length) {
      ctx.body = getResponseBody(null, false, '无法上传，上传的文件为空！', 400)
      return
    }
    const relativeFolder = 'system/guide'
    const filefolder = path.resolve(config.resourcePath, relativeFolder)
    if (!fs.existsSync(filefolder)) {
      fs.mkdirSync(filefolder)
    }
    const file = ctx.request.files[0]
    const fileData = {
      file,
      filefolder: relativeFolder
    }
    const data = await ctx.service.guide.upload(fileData)
    ctx.body = getResponseBody(data, true, '上传文件成功！', 200)
  }
  // 删除文件
  async deleteFile() {
    const { ctx, config } = this
    const body = ctx.request.body
    // todo: userId必须是管理员才能上传
    const userId = body.userId
    if (!body || !body.name) {
      ctx.body = getResponseBody(null, false, '参数不能为空！', 401)
      return
    }
    const filePath = path.resolve(
      config.resourcePath,
      'system/guide',
      body.name
    )
    if (!fs.existsSync(filePath)) {
      ctx.body = getResponseBody(null, false, '路径错误或者文件名错误！', 402)
      return
    }

    try {
      fs.unlinkSync(filePath)
      ctx.body = getResponseBody(null, true, '删除文件成功！', 200)
    } catch (e) {
      ctx.body = getResponseBody(null, false, '删除文件失败！', 500)
    }
  }
  // 新建目录
  async createContent() {
    const { ctx } = this
    const body = ctx.request.body
    const name = body.name
    const parentID = body.parentID
    const contents = {
      name: name,
      parentID
    }
    if (!parentID || !name) {
      ctx.body = getResponseBody(null, false, '参数不能为空', 1001)
      return
    }
    const rootData = await ctx.service.guide.findOne({ id: 1 })
    if (!rootData) {
      await ctx.service.guide.create({ id: 1, name: '根目录', parentID: 0 })
    }
    const contentData = await ctx.service.guide.findOne({
      parentID: parentID,
      name: name
    })
    if (contentData) {
      ctx.body = getResponseBody(null, false, '目录不能重复', 1002)
      return
    }
    const res = await ctx.service.guide.create(contents)
    ctx.body = getResponseBody(res)
  }
  // 删除目录
  async deleteContent() {
    const { ctx } = this
    const body = ctx.request.body
    const id = body.id
    const defaultData = await ctx.service.guide.findOne({ id: id })
    if (!defaultData) {
      ctx.body = getResponseBody(null, false, '无效目录', 1000)
      return
    }
    try {
      // if (defaultData.doc && Object.keys(defaultData.doc).length > 0) {
      //   ctx.body = getResponseBody(null, false, "目录下有文档不能删除！", 1001);
      //   return;
      // }
      const parentData = await ctx.service.guide.findOne({ parentID: id })
      if (parentData) {
        ctx.body = getResponseBody(
          null,
          false,
          '目录下有子目录不能删除！',
          1001
        )
        return
      }
      await ctx.service.guide.delete({ id })
      for (const doc of defaultData.doc) {
        const videoPath = doc.videoPath
        if (videoPath) {
          fs.unlinkSync(videoPath)
        }
      }
      ctx.body = getResponseBody(null, true, '删除成功', 200)
    } catch (e) {
      ctx.body = getResponseBody(null, false, '删除失败', 1002)
    }
  }
  // 重命名
  async rename() {
    const { ctx } = this
    const body = ctx.request.body
    const id = body.id
    const folderName = body.folderName
    if (!id || !folderName) {
      ctx.body = getResponseBody(null, true, '参数无效', 1001)
      return
    }
    const defaultData = await ctx.service.guide.findOne({ id: id })
    if (!defaultData) {
      ctx.body = getResponseBody(null, true, '无效目录', 1000)
      return
    }
    const data = await ctx.service.guide.update(
      { id },
      { $set: { name: folderName } }
    )
    ctx.body = getResponseBody(data, true, '重命名成功!')
  }
  // 首页目录
  async index() {
    const { ctx, config } = this
    const projection = {
      doc: 0
    }
    let data = await ctx.service.guide.find({}, projection)
    const dataCopy = JSON.parse(JSON.stringify(data))
    var res = generateOptions(dataCopy)
    ctx.body = getResponseBody(res)
    return
    const guidePath = path.resolve(config.resourcePath, `./guideData`)
    const picPath = path.resolve(config.resourcePath)
    const guideList = res[0].children
    for (let index = 0; index < guideList.length; index++) {
      const element = guideList[index]
      if (!fs.existsSync(`${guidePath}/${element.name}`)) {
        fs.mkdirSync(`${guidePath}/${element.name}`)
      }
      if (element.doc) {
        for (let j = 0; j < element.doc.length; j++) {
          const docItem = element.doc[j]
          const title = element.doc[j].title
          // console.log(html2md(docItem.content, {}, false), '文档转化')
          let picPathList =
            docItem.content.match(
              /system.*?(\.png|\.jpg|\.svg|\.gif|\.jpeg|\.mp4)/g
            ) || []
          // console.log(picPathList, '图片链接集合')
          if (!fs.existsSync(`${guidePath}/${element.name}/img`)) {
            fs.mkdirSync(`${guidePath}/${element.name}/img`)
          }
          for (let p = 0; p < picPathList.length; p++) {
            const pic = picPathList[p]
            let fieldname = pic.split('/').pop()
            console.log(`${picPath}/${pic}`, 'tupianjihe')
            if (
              !fs.existsSync(`${guidePath}/${element.name}/img/${fieldname}`)
            ) {
              console.log('dsadsa')
              fs.copyFileSync(
                `${picPath}/${pic}`,
                `${guidePath}/${element.name}/img/${fieldname}`
              )
            }
          }
          let regExp = new RegExp(_.escapeRegExp('../public/system/guide'), 'g')
          const mdStr = docItem.content.replace(regExp, 'img')
          const content = html2md(mdStr, {}, false)
          await fs.writeFileSync(
            `${guidePath}/${element.name}/${title}.md`,
            content,
            err => {
              if (err) throw err
            }
          )
        }
      } else {
        for (let c = 0; c < element.children.length; c++) {
          const childData = element.children[c]
          if (
            !fs.existsSync(`${guidePath}/${element.name}/${childData.name}`)
          ) {
            fs.mkdirSync(`${guidePath}/${element.name}/${childData.name}`)
          }
          if (childData.doc) {
            for (let j = 0; j < childData.doc.length; j++) {
              const docItem = childData.doc[j]
              const title = childData.doc[j].title

              let picPathList =
                docItem.content.match(
                  /system.*?(\.png|\.jpg|\.svg|\.gif|\.jpeg|\.mp4)/g
                ) || []
              // console.log(picPathList, '图片链接集合')
              if (
                !fs.existsSync(
                  `${guidePath}/${element.name}/${childData.name}/img`
                )
              ) {
                fs.mkdirSync(
                  `${guidePath}/${element.name}/${childData.name}/img`
                )
              }
              for (let p = 0; p < picPathList.length; p++) {
                const pic = picPathList[p]
                let fieldname = pic.split('/').pop()
                console.log(`${picPath}/${pic}`, 'tupianjihe')
                if (
                  !fs.existsSync(
                    `${guidePath}/${element.name}/${childData.name}/img/${fieldname}`
                  )
                ) {
                  console.log('dsadsa')
                  fs.copyFileSync(
                    `${picPath}/${pic}`,
                    `${guidePath}/${element.name}/${childData.name}/img/${fieldname}`
                  )
                }
              }
              let regExp = new RegExp(
                _.escapeRegExp('../public/system/guide'),
                'g'
              )
              const mdStr = docItem.content.replace(regExp, 'img')
              const content = html2md(mdStr, {}, false)
              await fs.writeFileSync(
                `${guidePath}/${element.name}/${childData.name}/${title}.md`,
                content,
                err => {
                  if (err) throw err
                }
              )
            }
          }
        }
      }
    }
  }
  // 获取列表
  // async getDocList() {
  //   const { ctx } = this;
  //   if (ctx.request.body.id) {
  //     const data = await ctx.service.guide.find({});
  //     const newData = JSON.parse(JSON.stringify(data));
  //     const res = generateMarkTree(newData, ctx.request.body.id || 1);
  //     ctx.body = getResponseBody(_.uniq(res));
  //   } else {
  //     const data = await ctx.service.guide.find({ doc: { $gt: [] } });
  //     let res = [];
  //     data.map(item => {
  //       res = [...res, ...item.doc]
  //     });
  //     const returnValue = [];
  //     res.map(item => {
  //       let one = {};
  //       one.title = item.title;
  //       one.id = item.id;
  //       one.type = item.type;
  //       one.videoPath = item.videoPath;
  //       one.coverStyle = item.coverStyle;
  //       one.mark = item.mark;
  //       returnValue.push(one)
  //     });
  //     ctx.body = getResponseBody(returnValue);
  //   }
  // }
  // 创建文档
  async createDoc() {
    const { ctx } = this
    const body = ctx.request.body
    const userId = body.userId
    const title = body.title
    const content = body.content
    const videoPath = body.videoPath || ''
    const mark = body.mark
    const coverStyle = body.coverStyle
    const folderId = body.folderId
    const keyword = body.keyword

    if (!title || !folderId) {
      ctx.body = getResponseBody(null, false, '名称、路径参数不能为空！', 1000)
      return
    }
    const contentData = await ctx.service.guide.findOne({ id: folderId })
    if (!contentData) {
      ctx.body = getResponseBody(null, false, '路径不正确!', 1001)
      return
    }
    const now = moment().format('YYYY-MM-DD HH:mm:ss')
    const oneContent = {
      type: content && videoPath ? 3 : content ? 0 : 1,
      mark,
      coverStyle: coverStyle,
      id: uuid(),
      content,
      videoPath,
      title,
      userId,
      keyword: keyword || '',
      createdAt: now,
      updatedAt: now
    }
    if (contentData.doc instanceof Array) {
      contentData.doc.push(oneContent)
    }
    try {
      await ctx.service.guide.update(
        { id: folderId },
        { $set: { doc: contentData.doc } }
      )
      ctx.body = getResponseBody(null, true, '文档创建成功')
    } catch (e) {
      ctx.body = getResponseBody(null, false, '文档创建失败！', 1002)
    }
  }

  // 获取文件夹下封面
  async getFrontCover() {
    const { ctx } = this
    const coverData = await ctx.service.cover.find({})
    ctx.body = getResponseBody(coverData)
  }

  async getDocById() {
    const { ctx } = this
    const query = ctx.request.query
    const id = query.id
    const returnValue = await ctx.service.guide.find({ 'doc.id': id })
    const resValue = returnValue[0].doc.filter((item, index) => {
      return item.id == id
    })
    ctx.body = getResponseBody(resValue[0])
  }
  // 搜索
  async getDocList() {
    const { ctx } = this
    const body = ctx.request.body
    const folderId = body.folderId
    const data = await ctx.service.guide.find({})
    const newData = JSON.parse(JSON.stringify(data))
    const res = generateMarkTree(newData, folderId || 0)
    let returnValue = []
    if (body.title && body.marks) {
      returnValue = res.filter(item => {
        return (
          item.mark.includes(body.marks) && item.title.search(body.title) != -1
        )
      })
    } else if (body.title) {
      returnValue = res.filter(item => {
        return item.title && item.title.search(body.title) != -1
      })
    } else if (body.marks) {
      returnValue = res.filter(item => {
        return item.mark.includes(body.marks)
      })
    } else if (body.id) {
      returnValue = res.filter(item => {
        return item.id == body.id
      })
    } else {
      returnValue = res
    }
    ctx.body = getResponseBody(returnValue)
  }

  // 删除文档
  async deleteDoc() {
    const { ctx } = this
    const body = ctx.request.body
    const ids = body.ids
    if (!ids || Object.keys(ids).length == 0) {
      ctx.body = getResponseBody(null, false, 'ids不能为空！', 1001)
      return
    }
    for (const id of ids) {
      const newId = id
      const delDoc = await ctx.service.guide.findOne({ 'doc.id': id })
      if (!delDoc) {
        ctx.body = getResponseBody(null, false, '无效文档', 1002)
        return
      }

      for (let i = 0; i < delDoc.doc.length; i++) {
        if (delDoc.doc[i] && delDoc.doc[i].id == newId) {
          delete delDoc.doc[i]
        }
      }
      let res = []
      for (let i = 0; i < delDoc.doc.length; i++) {
        if (delDoc.doc[i]) {
          res.push(delDoc.doc[i])
        }
      }
      const data = await ctx.service.guide.update(
        { id: delDoc.id },
        { doc: res }
      )
    }

    ctx.body = getResponseBody(null, true, '删除文档成功！', 200)
  }
  // 编辑文档
  async updateDoc() {
    const { ctx } = this
    const body = ctx.request.body
    const id = body.id
    const title = body.title
    const mark = body.mark
    const videoPath = body.videoPath
    const coverStyle = body.coverStyle
    const content = body.content
    const folderId = body.folderId
    const keyword = body.keyword
    const type = content && videoPath ? 3 : content ? 0 : 1
    if (!title || !id) {
      ctx.body = getResponseBody(null, false, 'id、标题不能为空！', 1001)
      return
    }

    let updateData = {
      id,
      type,
      title,
      mark,
      videoPath,
      coverStyle,
      content,
      keyword: keyword || '',
      updatedAt: moment().format('YYYY-MM-DD HH:mm:ss')
    }
    const orgrinData = await ctx.service.guide.findOne({ 'doc.id': id })
    let data = []
    if (orgrinData && orgrinData.id == folderId) {
      data = await ctx.service.guide.update(
        { 'doc.id': id },
        { $set: { 'doc.$': updateData } }
      )
    } else {
      // 改变原来数据
      orgrinData.doc = orgrinData.doc.filter(item => {
        return item.id !== id
      })
      await ctx.service.guide.update(
        { id: orgrinData.id },
        { doc: orgrinData.doc }
      )
      // 添加新对数据
      let newData = await ctx.service.guide.findOne({ id: folderId })
      newData.doc.push(updateData)
      await ctx.service.guide.update({ id: folderId }, { doc: newData.doc })
    }
    ctx.body = getResponseBody(null, true, '修改成功')
  }
  // 获取所以标签
  async getMarks() {
    const { ctx } = this
    const id = ctx.request.body.id
    const data = await ctx.service.guide.find({})
    const newData = JSON.parse(JSON.stringify(data))
    const res = generateMarkTree(newData, id || 0)
    const returnValue = []
    res.map(item => {
      returnValue.push(...item.mark)
    })
    ctx.body = getResponseBody(_.uniq(returnValue))
  }

  // 拖拽文档
  async changeContent() {
    const { ctx } = this
    const body = ctx.request.body
    const parentID = body.parentID // 拖拽到父文件夹的位置
    const folderId = body.folderId // 当前文件夹id
    const treeList = body.treeList
    //console.log("<<<<<<<<<<",treeList);
    if (!parentID || !folderId) {
      throw new SeatomException(ERROR_CODES.PARAMETER_IS_REQUIRED, '无效参数！')
    }
    const data = await ctx.service.guide.findOne({ id: folderId })
    if (!data) {
      ctx.body = getResponseBody(null, false, '无效的文件夹！', 400)
      return
    }

    // 改变所属文件夹
    const parentContent = await ctx.service.guide.findOne({
      parentID: parentID
    })
    if (!parentContent) {
      ctx.body = getResponseBody(null, false, '无效的父文件夹！', 400)
      return
    }
    await ctx.service.guide.update({ id: folderId }, { parentID: parentID })
    // 改变移动后对位置
    let children = getChildren(treeList, parentID)
    children =
      children &&
      children.map((value, index) => {
        return { sort: value.sort, parentID: value.parentID, id: value.id }
      })
    for (const child of children) {
      await ctx.service.guide.update({ id: child.id }, { sort: child.sort })
    }
    // 不是同一个文件夹内移动
    if (data.parentID != parentID) {
      // 改变移动前对位置
      let oldChildren = getChildren(treeList, data.parentID)
      oldChildren =
        oldChildren &&
        oldChildren.map((value, index) => {
          return { sort: value.sort, parentID: value.parentID, id: value.id }
        })
      for (const child of oldChildren) {
        await ctx.service.guide.update({ id: child.id }, { sort: child.sort })
      }
    }
    ctx.body = getResponseBody(null, true, '修改成功')
  }
  // 保存草稿
  async saveDraft() {
    const { ctx } = this
    const body = ctx.request.body
    const userId = body.userId
    const content = body.content
    const docId = body.docId
    if (docId == 'NEW_TUTORIAL_DRAFT') {
      await ctx.service.draft.create({ userId, docId, content: content })
    } else {
      const draft = await ctx.service.draft.findOne({ userId, docId })
      if (draft) {
        await ctx.service.draft.update({ userId }, { content, docId })
      }
    }
    ctx.body = getResponseBody(null, true, '保存成功。')
  }
  //根据userId  获取草稿内容
  async getDraft() {
    const { ctx } = this
    const body = ctx.request.body
    const userId = body.userId
    const docId = body.docId
    const draft = await ctx.service.draft.findOne(
      { userId, docId },
      { id: 0, createdAt: 0, updatedAt: 0 }
    )
    ctx.body = getResponseBody(draft, true)
  }
}

// 根据parentID获取children级别
function getChildren(tree, parentID) {
  for (const data of tree) {
    if (data.id == parentID) {
      return data.children.map((value, index) => {
        return Object.assign(value, { sort: index })
      })
    }
  }
  for (const data of tree) {
    if (data.children) {
      return getChildren(data.children, parentID)
    }
  }
}

function generateOptions(params) {
  var result = []
  for (let param of params) {
    if (param.parentID == 0) {
      param.children = getchilds(param.id, params).sort((a, b) => {
        return a.sort - b.sort
      })
      delete params.doc
      result.push(param)
    }
  }
  return result
}

function getchilds(id, array) {
  let childs = new Array()
  for (let arr of array) {
    if (arr.parentID == id) {
      childs.push(arr)
    }
  }
  childs.sort((a, b) => {
    return a.sort - b.sort
  })
  for (let child of childs) {
    let childscopy = getchilds(child.id, array)
    if (childscopy.length > 0) {
      delete child.doc
      child.children = childscopy
    }
  }
  return childs
}

function generateMarkTree(params, id) {
  var marks = []
  for (let param of params) {
    if (id == param.id) {
      param.doc &&
        param.doc.map(item => {
          item.folderId = param.id
        })
      marks.push(...param.doc)
    }
    if (param.parentID == id) {
      param.doc &&
        param.doc.map(item => {
          item.folderId = param.id
        })
      marks.push(...param.doc)
      markChild(param.id, params, marks)
    }
  }
  return marks
}
function markChild(id, array, marks) {
  let childs = new Array()
  for (let arr of array) {
    if (arr.parentID == id) {
      childs.push(arr)
    }
  }
  for (let child of childs) {
    child.doc &&
      child.doc.map(item => {
        item.folderId = child.id
      })
    markChild(child.id, array, marks)
    marks.push(...child.doc)
  }
}

module.exports = GuideController
