'use strict'
// 地图相关
const _ = require('lodash')
const fs = require('fs-extra')
const path = require('path')
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
var crypto = require('crypto')

class MapdataController extends Controller {
  async upload() {
    const { ctx, config } = this
    if (!ctx.request.files || !ctx.request.files.length) {
      ctx.body = getResponseBody(
        null,
        false,
        '无法创建，上传地图文件为空！',
        400
      )
      return
    }
    const file = ctx.request.files[0]
    const mapData = fs.readFileSync(file.filepath, 'utf-8')
    const filefolder = 'screenData/mapjson' // 上传的地图文件路径
    const fileData = {
      file,
      filefolder
    }
    const data = await ctx.service.common.upload(fileData)
    const res = {
      mapData: mapData,
      ...data
    }
    ctx.body = getResponseBody(res)
  }
}

module.exports = MapdataController
