'use strict'
const path = require('path')
let fs = require('fs-extra')
const base64 = require('base-64')
const { spawnSync, exec } = require('child_process')
const _ = require('lodash')
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
const SeatomException = require('../exception/seatomException')
const { cipher, decipher, encrypt, decrypt } = require('../extend/crypto')
const { ERROR_CODES } = require('../extend/constant')
const { formatString } = require('../extend/utils')
// const JSSM4 = require("jssm4");
class CommonController extends Controller {
  async uploadfileList() {
    const { ctx } = this
    if (!ctx.request.files || !ctx.request.files.length) {
      ctx.body = getResponseBody(null, false, '无法创建，上传文件为空！', 400)
      return
    }
    const filefolder = ctx.request.body.filefolder
      ? `personal/${ctx.request.body.filefolder}`
      : 'personal'
    const fileList = ctx.request.files
    let fileUrlList = []
    for (let i = 0, len = fileList.length; i < len; i++) {
      const data = await ctx.service.common.upload({
        file: fileList[i],
        filefolder
      })
      fileUrlList.push(data)
    }
    ctx.body = getResponseBody(fileUrlList)
  }
  async uploadLicence() {
    const { ctx, config } = this
    if (!ctx.request.files || !ctx.request.files.length) {
      ctx.body = getResponseBody(null, false, '无法创建，授权文件为空！', 400)
      return
    }
    const file = ctx.request.files[0]
    if (file.filename.indexOf('licence') === -1) {
      ctx.body = getResponseBody(null, false, '请选择正确的授权文件', 400)
      return
    }
    const data = await fs.readFileSync(file.filepath, 'utf-8')
    const days = decipher(data).split('&&')[0]
    const ip = decipher(data).split('&&')[1]
    if (ip == config.serverIp && days) {
      const licencePath = path.join(__dirname, '..', '..', 'licence.txt')
      await fs.writeFileSync(licencePath, data, err => {
        if (err) throw err
      })
      ctx.body = getResponseBody('授权天数' + days + '天')
      return
    } else {
      ctx.body = getResponseBody(null, false, '请选择正确的授权文件', 400)
      return
    }
  }
  async getLicenceCode() {
    const { ctx, config } = this
    const code = cipher(config.serverIp)
    ctx.body = getResponseBody({ code })
  }
  async licence() {
    const { ctx, config } = this
    const { day } = ctx.query
    let encryptstr
    if (ctx.query.ip) {
      const { ip } = ctx.query
      const str = `${day}&&${ip}`
      encryptstr = cipher(str)
    } else {
      const str = `${day}`
      encryptstr = cipher(str)
    }
    const fuxi = path.resolve(config.resourcePath, `./personal`)
    await fs.writeFileSync(`${fuxi}/licence.txt`, encryptstr, err => {
      if (err) throw err
    })
    ctx.attachment(`${fuxi}/licence.txt`)
    ctx.set('Content-Type', 'application/octet-stream')
    ctx.body = fs.createReadStream(`${fuxi}/licence.txt`)
  }
  async upload() {
    const { ctx } = this
    if (!ctx.request.files || !ctx.request.files.length) {
      ctx.body = getResponseBody(null, false, '无法创建，上传文件为空！', 400)
      return
    }
    const filefolder = ctx.request.body.filefolder
      ? `personal/${ctx.request.body.filefolder}`
      : 'personal' // 上传的文件夹名
    const isModelFile = ctx.request.body.isModelFile || false
    const file = ctx.request.files[0]
    const fileData = {
      file,
      filefolder,
      isModelFile
    }
    const data = await ctx.service.common.upload(fileData)
    ctx.body = getResponseBody(data)
  }
  async customApiRequest() {
    // baseConfig数据源基础配置,componentConfig组件配置, paramsData前端传参
    const { ctx } = this
    const requestBody = ctx.request.body

    const {
      method,
      headers = null,
      baseUrl,
      path,
      params = null,
      body = null
    } = requestBody
    if (!ctx) {
      throw new Error('服务端错误，获取数据失败')
    }
    let url = baseUrl + path
    const options = {
      method: method,
      contentType: 'json',
      dataType: 'json',
      timeout: 3000
    }
    if (method !== 'GET' && method !== 'DELETE' && body) {
      let data = formatString(body, {})
      try {
        options.data = JSON.parse(data)
      } catch (error) {
        throw new SeatomException(
          ERROR_CODES.PARAMS_FORMAT_ERROR,
          'body格式错误，' + error.message
        )
      }
    }
    if (headers) {
      let headersStr = formatString(headers, {})
      try {
        options.headers = JSON.parse(headersStr)
      } catch (error) {
        throw new SeatomException(
          ERROR_CODES.PARAMS_FORMAT_ERROR,
          'header格式错误，' + error.message
        )
      }
    }
    // params格式：形如a = a & b = ${b} & c= ${c}的字符串
    if (params) {
      let paramString = formatString(params, {})
      // 去空格
      paramString = encodeURI(paramString.replace(/\s+/g, ''))
      url = url + '?' + paramString
    }
    const result = await ctx.curl(_.trim(url), options)
    if (result) {
      ctx.body = getResponseBody(result.data)
      return
    } else {
      throw new Error('获取API数据失败')
    }
  }
  async customRequest() {
    // baseConfig数据源基础配置,componentConfig组件配置, paramsData前端传参
    const { ctx } = this
    const requestBody = ctx.request.body

    const {
      method,
      headers = null,
      urlPath,
      params = null,
      body = null
    } = requestBody
    if (!ctx) {
      throw new Error('服务端错误，获取数据失败')
    }
    let url = urlPath
    const options = {
      method: method,
      contentType: 'json',
      dataType: 'json',
      timeout: 3000
    }
    if (method !== 'GET' && method !== 'DELETE' && body) {
      let data = formatString(body, {})
      try {
        options.data = JSON.parse(data)
      } catch (error) {
        throw new SeatomException(
          ERROR_CODES.PARAMS_FORMAT_ERROR,
          'body格式错误，' + error.message
        )
      }
    }
    if (headers) {
      let headersStr = formatString(headers, {})
      // console.log(headersStr, 'dadasdasdas======')

      try {
        options.headers = JSON.parse(headersStr)
      } catch (error) {
        throw new SeatomException(
          ERROR_CODES.PARAMS_FORMAT_ERROR,
          'header格式错误，' + error.message
        )
      }
    }
    // params格式：形如a = a & b = ${b} & c= ${c}的字符串
    if (params) {
      let paramString = formatString(params, {})
      // 去空格
      paramString = encodeURI(paramString.replace(/\s+/g, ''))
      url = url + '?' + paramString
    }
    const result = await ctx.curl(_.trim(url), options)
    if (result) {
      // console.log(result.data, '自定义请求返回的数据返回的数据')
      ctx.body = getResponseBody(result.data)
      return
    } else {
      throw new Error('获取API数据失败')
    }
  }
  // 武汉卫建委获取单点登录的用户数据
  async getSsoUserInfo() {
    const { ctx } = this
    const { tgt } = ctx.query
    const sKey = '2MAjwL3U'
    const keyWord = 'WH_YQFKGLPT'
    const serviceResult = await ctx.curl(
      'http://10.88.42.73:11729/api/sm4/encrypt',
      {
        method: 'POST',
        dataType: 'json',
        data: {
          key: sKey,
          value: 'https://10.88.42.134'
        },
        rejectUnauthorized: false
      }
    )
    const result = await ctx.curl(
      `http://10.88.44.181:9091/ssows/getLoginNameByTgt?tgt=${tgt}&service=${serviceResult.data.result}&keyWord=${keyWord}`,
      {
        method: 'POST',
        headers: {
          appCode: 'iuLECd',
          Sign: '21677601F753D7A98D745FFAA3D23B18'
        },
        dataType: 'json',
        data: {},
        rejectUnauthorized: false
      }
    )
    ctx.body = getResponseBody(result.data)
  }
  async getValue() {
    const { ctx } = this
    const result = await ctx.curl('http://10.88.42.73:11729/api/sm4/decrypt', {
      method: 'POST',
      dataType: 'json',
      data: {
        key: '2MAjwL3U',
        value: 'g00jDj96Dbj3ggUOWkkO+w=='
      },
      rejectUnauthorized: false
    })
    ctx.body = getResponseBody(result.data)
  }
  async apitest() {
    const { ctx } = this
    const params = ctx.query
    const res = await ctx.service.resource.find(params)
    this.ctx.body = getResponseBody(res)
  }
  async permission() {
    const { ctx } = this
    this.ctx.body = getResponseBody([
      {
        id: '1557615522771185666',
        parentId: '1556486743898779649',
        code: 'xp_zwfw_yss',
        name: '粤省事',
        alias: 'yss',
        path: './',
        source: 'iconfont iconicon_shakehands',
        category: 1,
        result: true
      },
      {
        id: '1557615696432148481',
        parentId: '1556486743898779649',
        code: 'xp_zwfw_yst',
        name: '粤商通',
        alias: 'yst',
        path: './',
        source: 'iconfont iconicon_shakehands',
        category: 1,
        result: false
      },
      {
        id: '1557615827428651009',
        parentId: '1556486743898779649',
        code: 'xp_zwfw_ygp',
        name: '粤公平',
        alias: 'ygp',
        path: './',
        source: 'iconfont iconicon_task_done',
        category: 1,
        result: true
      },
      {
        id: '1557616580356550657',
        parentId: '1557615522771185666',
        code: 'xp_zwfw_ygp_jyje',
        name: '交易金额',
        alias: 'jyje',
        path: '',
        source: 'iconfont iconicon_shakehands',
        category: 4,
        result: true
      },
      {
        id: '1557616767762247682',
        parentId: '1557615827428651009',
        code: 'xp_zwfw_ygp_jyxms',
        name: '交易项目数',
        alias: 'jyxms',
        path: '',
        source: 'iconfont iconicon_shakehands',
        category: 4,
        result: true
      },
      {
        id: '1557616580356550652',
        parentId: '1557616580356550657',
        code: 'xp_zwfw_ygp_jyje',
        name: '交易下级',
        alias: 'jyje',
        path: '',
        source: 'iconfont iconicon_shakehands',
        category: 4,
        result: true
      }
    ])
  }
  // 数据权限
  async dataPermission() {
    const { ctx, config } = this
    const { exchangeToken, domain } = ctx.request.body
    const openInfo = config.openInfo || {
      domain: 'haizhi',
      ip: '',
      api: ''
    }
    if (!exchangeToken || domain !== openInfo.domain) {
      ctx.body = getResponseBody(null, false, '请求参数错误', 400)
    }
    const url = openInfo.ip + openInfo.api
    const options = {
      data: { exchangeToken, domain },
      method: 'POST',
      headers: {
        exchangeToken: exchangeToken
      },
      contentType: 'json',
      dataType: 'json',
      timeout: 60000
    }
    // ctx.body = getResponseBody({
    //   code: 200, // 校验成功的状态码，200代表成功，其他都是失败
    //   message: '', // 校验失败时的详细错误信息
    //   data: {
    //     access: true, // 必选，是否对当前屏有访问权限，boolean类型：true表示有访问权限，正常打开此页面；false表示没有权限，会打开一个显示无权限页面的空白页面
    //     access_token: 'e114cd4c5ce132c1299dd210a3fa2c78b2cfbab4dcd44f8d', // 可选，权限访问token，第三方平台通过此次校验访问生成的访问token，校验成功后打开发布页后，组件每次请求数据源平台都会把这个参数带到请求header里，第三平台可根据此参数继续进行数据权限的校验和数据过滤
    //     username: 'admin', // 可选，第三方平台的用户名，用于在配置屏时将用户名设置为组件参数，在配置界面过滤器、筛选配置、联动等处通过${user.username}获取到
    //     userid: 'jui7dsadf', // 可选，第三方平台的用户id，用于在配置屏时将用户id设置为组件参数，在配置界面过滤器、筛选配置、联动等处通过${user.userid}获取到
    //     name: '群体管控',
    //     resourceType: 'picture'
    //   }
    // })
    // return
    this.ctx.logger.info('数据权限接口参数', url, options)
    try {
      const result = await ctx.curl(_.trim(url), options)
      this.ctx.logger.info('数据权限返回数据', result)
      if (result) {
        ctx.body = getResponseBody(result.data)
      } else {
        ctx.body = getResponseBody(result, false, '请求失败', 400)
      }
    } catch (error) {
      ctx.body = getResponseBody(error, false, '请求失败', 400)
    }
  }
}

module.exports = CommonController
