'use strict'
// 大屏截图
const _ = require('lodash')
const fs = require('fs-extra')
const path = require('path')
const Controller = require('egg').Controller
const { getResponseBody, getScreenshot } = require('../extend/utils')
class ScreenshotController extends Controller {
  async create() {
    const { ctx, config } = this
    const body = ctx.request.body
    const screenId = body.screenId
    const screenData = await ctx.service.screen.findOne({ id: screenId })
    let width, height
    if (screenData && screenData.config) {
      width = screenData.config.width || 1920
      height = screenData.config.height || 1080
    }
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    const name = 'screenshot' + screenId
    const fileType = 'jpg'
    const filefolder = path.resolve(config.resourcePath, './screenData')
    if (!fs.existsSync(filefolder)) {
      fs.mkdirSync(path.resolve(config.resourcePath, './screenData'))
    }
    const filePath = path.resolve(
      config.resourcePath,
      './screenData/' + name + '.' + fileType
    )
    if (fs.existsSync(filePath)) {
      fs.removeSync(filePath)
    }
    const screenshotData = {
      token,
      userId,
      fileType,
      name,
      width,
      height,
      url: `${config.webServerIp}/preview/index.html#/${screenId}`,
      path: path.resolve(config.resourcePath, './screenData')
    }
    const result = await getScreenshot(screenshotData)
    if (result && result.code === 0) {
      let screenshotdata, screenshotUrl
      screenshotUrl = '/public/screenData/' + name + '.' + fileType
      screenshotdata = {
        relationId: screenId,
        screenshotUrl
      }
      const defaultData = await ctx.service.screenshot.findOne(screenshotdata)
      if (!defaultData) {
        const data = await ctx.service.screenshot.create(screenshotdata)
        ctx.body = getResponseBody(data)
      } else {
        ctx.body = getResponseBody(defaultData)
      }
    }
  }
}

module.exports = ScreenshotController
