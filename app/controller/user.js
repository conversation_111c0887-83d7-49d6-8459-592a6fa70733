'use strict'
// 用户相关
const Controller = require('egg').Controller
const {
  uuid,
  getResponseBody,
  getExpdate,
  compareVersion
} = require('../extend/utils')
const base64 = require('base-64')
const SeatomException = require('../exception/seatomException')
const { ERROR_CODES } = require('../extend/constant')
const { getPassword } = require('../extend/password-utils')
const {
  domainCity,
  userObj,
  userNameList
} = require('../extend/seatom-account')
const _ = require('lodash')
const { cipher, decipher } = require('../extend/crypto')
const fs = require('fs-extra')
const path = require('path')
class userController extends Controller {
  async getAuthorizationDays() {
    const { ctx, config } = this
    const filePath = path.join(__dirname, '..', '..', 'licence.txt')
    // 文件不存在的话
    if (!fs.existsSync(filePath)) {
      ctx.body = getResponseBody(
        { authorizationDays: 0 },
        false,
        `产品授权已过期`,
        455
      )
      return
    }
    const data = await fs.readFileSync(filePath, 'utf-8')
    let days
    const decipherData = decipher(data).split('&&')[0]
    const ip = decipher(data).split('&&')[1]
    if (ip == config.serverIp) {
      days = parseInt(decipherData)
      console.log(days, 'days=============')
    } else {
      days = 0
    }
    // 如果有expireDate，通过日期计算
    const expireDate = decipher(data).split('&&')[2]
    this.ctx.logger.info('expireDate==', expireDate)
    if (expireDate) {
      // 说明是新逻辑
      days = Math.floor((expireDate - new Date().getTime()) / (24 * 3600 * 1000))
    }
    this.ctx.logger.info('剩余权限天数', days)
    const expdate = getExpdate(days)
    if (days <= 0) {
      let go = -days
      ctx.body = getResponseBody(
        { authorizationDays: days, expdate },
        false,
        `产品授权已经过期${go}天`,
        455
      )
      return
    } else {
      ctx.body = getResponseBody(
        { authorizationDays: days, expdate },
        true,
        `产品授权还有${days}天过期`
      )
      return
    }
  }

  async dmcUcenter() {
    const { ctx, config } = this
    let loginUrl
    if (config.ucenter) {
      if (config.dmcAddress.externalAddress) {
        loginUrl =
          config.dmcAddress.externalAddress +
          `/api/ucenter/check/gen_redirect_url?to=${config.pageURL}/login&redirect=1`
        ctx.unsafeRedirect(loginUrl)
      } else {
        loginUrl =
          config.dmcAddress.address +
          `/api/ucenter/check/gen_redirect_url?to=${config.pageURL}/login&redirect=1`
        ctx.unsafeRedirect(loginUrl)
      }
      return
    } else {
      loginUrl = ''
    }
    ctx.body = getResponseBody(loginUrl)
  }

  async screenConfig() {
    const { ctx, config } = this
    const newConfig = {
      ucenter:'',
      dmcAddress:{
        address:''
      },
      agentEnable: false
    }
    if (config) {
      // 添加dmcAddress
      newConfig['dmcAddress']['address'] =
        config.dmcAddress.externalAddress || config.dmcAddress.address

      // 添加ucenter
      newConfig.ucenter = config.ucenter
      // 添加大模型是否开启的功能
      newConfig.agentEnable = config.agent.enable
    }
    ctx.body = getResponseBody(newConfig)
  }

  async create() {
    const { ctx, config } = this
    const body = ctx.request.body
    const userName = body.username
    const domain = body.domain
    const passWord = body.password
    const userData = await ctx.service.user.find({ userName })
    if (userData && userData.length) {
      ctx.body = getResponseBody(null, false, '账号已存在', 400)
      return
    }
    const userId = uuid(userName)
    const data = await ctx.service.user.create({
      userName,
      passWord,
      domain,
      userId
    })
    ctx.body = getResponseBody(data)
  }

  async dmcLogin() {
    const { ctx, config } = this
    const body = ctx.request.body
    const username = body.username
    const domain = body.domain
    const password = body.password
    // const password = "T0RnNE9EZzQ=";
    let data
    const loginUrl =
      this.config.dmcAddress.address + '/api/ucenter/account/login'
    const result = await ctx.curl(loginUrl, {
      method: 'POST',
      data: {
        domain,
        username,
        pro_name: 'bdp',
        // 前端传的时候就是两次base加密，所以要先解密
        password: getPassword(
          base64.decode(base64.decode(password)),
          this.config
        )
      },
      timeout: 10000,
      rejectUnauthorized: false,
      dataType: 'json'
    })
    this.ctx.logger.info('请求的登录地址: ', loginUrl, result)
    if (result.status === 200 && result.data.status === '0') {
      const userId = result.data.result.user_info.user_id
      const entId = result.data.result.user_info.ent_id || ''
      const defaultData = await ctx.service.workspace.findOne({
        userId,
        type: 0
      })
      const userData = await ctx.service.user.findOne({ userId })
      const token = result.data.result.access_token
      const userName_creat = result.data.result.user_info.name
      if (!userData) {
        await ctx.service.user.create({
          userId,
          token: [token],
          userName: userName_creat,
          entId,
          isDmcLogin: 1
        })
      } else {
        if (userData.token.length > 100) {
          const tokenList = userData.token.slice(-50)
          await ctx.service.user.update({ userId }, { token: tokenList })
        }
        await ctx.service.user.update(
          { userId },
          {
            isDmcLogin: 1,
            userName: userName_creat,
            entId,
            $addToSet: { token: token }
          }
        )
        // await ctx.service.user.update({ userId }, { isDmcLogin: 1, token: [token] })
      }
      if (defaultData) {
        data = {
          workspaceData: defaultData,
          userInfo: result
        }
        ctx.body = getResponseBody(data)
        return
      }
      const workspaceBody = {
        name: username + '的工作空间',
        userId,
        type: 0
      }
      const workspaceData = await ctx.service.workspace.create(workspaceBody)
      data = {
        workspaceData: workspaceData,
        userInfo: result
      }
      ctx.body = getResponseBody(data)
      ctx.cookies.set('access_token', token)
      ctx.cookies.set('my_user_id', userId)
      return
    }
    data = {
      userInfo: result
    }
    // ctx.body = getResponseBody(data, false, result.data.errstr, 400);
    if (
      result.data.status == '1010' &&
      result.data.result.remaing_lock_seconds
    ) {
      ctx.body = getResponseBody(
        data,
        false,
        '登录错误次数过多，账号被临时锁定，请在' +
          result.data.result.remaing_lock_seconds +
          '秒后尝试登录',
        400
      )
    } else {
      ctx.body = getResponseBody(data, false, result.data.errstr, 400)
      // throw new SeatomException(ERROR_CODES.LOGIN_FAILURE, result.data.errstr);
    }
  }
  // 针对无法链接dmc的数据，写的登录接口
  async localLogin() {
    const { ctx, config } = this
    const body = ctx.request.body
    const username = body.username
    const domain = body.domain
    const password = body.password
    let data
    if (domain === domainCity[username] && userObj[username] === password) {
      const userId = cipher(username)
      const defaultData = await ctx.service.workspace.findOne({
        userId,
        type: 0
      })
      const userData = await ctx.service.user.find({ userId })
      // const token = base64.encode(base64.encode(password))
      const token = ctx.app.jwt.sign(
        { username, password },
        config.jwt.secret,
        { expiresIn: '7 days' }
      )
      const userName_creat = username
      const result = {
        data: {
          result: {
            access_token: token,
            user_info: {
              name: userNameList[username] || username
            }
          }
        }
      }
      if (!userData || !userData.length) {
        await ctx.service.user.create({
          userId,
          token: [token],
          userName: userName_creat,
          isDmcLogin: 2
        })
      } else {
        // await ctx.service.user.update({ userId }, { isDmcLogin: 2, $addToSet: { token: token } })
        await ctx.service.user.update(
          { userId },
          { isDmcLogin: 2, token: [token] }
        )
      }
      if (defaultData) {
        data = {
          workspaceData: defaultData,
          userInfo: result
        }
        ctx.body = getResponseBody(data)
        return
      }
      const workspaceBody = {
        name: (userNameList[username] || username) + '的工作空间',
        userId,
        type: 0
      }
      const workspaceData = await ctx.service.workspace.create(workspaceBody)
      data = {
        workspaceData: workspaceData,
        userInfo: result
      }
      ctx.body = getResponseBody(data)
    } else {
      ctx.body = getResponseBody(null, false, '企业域或账号密码不对', 400)
      return
    }
  }

  async index() {
    const { ctx } = this
    const data = await ctx.service.user.find()
    ctx.body = getResponseBody(data)
  }

  async ucenterLogin() {
    const { ctx, config } = this
    ctx.unsafeRedirect(
      `${config.dmcAddress.address}/api/ucenter/check/gen_redirect_url?to=${config.pageURL}/login&redirect=1`
    )
  }

  async logout() {
    const { ctx } = this
    const body = ctx.request.body
    const userId = body.userId
    const token = body.token
    const data = await ctx.service.user.update(
      { userId: userId },
      { $pull: { token: token } }
    )
    ctx.body = getResponseBody(data)
  }

  async getDmcUserInfo() {
    const { ctx } = this
    const query = ctx.query
    const data = await ctx.service.user.getDmcUserInfo(query)
    if (data.data && data.data.result && data.data.result.data) {
      ctx.body = getResponseBody(data.data.result.data)
    } else {
      ctx.body = getResponseBody(data)
    }
  }
  async getDmcGroupCategory() {
    const { ctx } = this
    const data = await ctx.service.user.getDmcGroupCategory()
    if (data.data && data.data.result && data.data.result.data) {
      ctx.body = getResponseBody(data.data.result.data)
    } else {
      ctx.body = getResponseBody(data)
    }
  }
  async getDmcGroupUserList() {
    const { ctx } = this
    const query = ctx.query
    const data = await ctx.service.user.getDmcGroupUserList(query.group_id)
    ctx.body = getResponseBody(data)
  }
  async genRedirecUrl() {
    const { ctx } = this
    const query = ctx.query
    const data = await ctx.service.user.genredirecturl(query)
    if (data.data && data.data.result && data.data.result.data) {
      ctx.body = getResponseBody(data.data.result.data)
    } else {
      ctx.body = getResponseBody(data)
    }
  }
  async getUserId() {
    const { ctx, config } = this
    const body = ctx.request.body
    const ec_token = body.ec_token
    const timestap = body.timestap
    const loginUrl =
      this.config.dmcAddress.address + '/api/ucenter/check/valid_ec_params'
    const userinfoUrl =
      this.config.dmcAddress.address + '/api/ucenter/user/info'
    const checkResult = await ctx.curl(loginUrl, {
      method: 'GET',
      data: {
        ec_token,
        timestap
      },
      dataType: 'json',
      rejectUnauthorized: false
    })
    let userName_creat, userResult
    let data
    if (checkResult.status === 200 && checkResult.data.status === '0') {
      const userId = checkResult.data.result.user_id
      const token = checkResult.data.result.token
      const defaultData = await ctx.service.workspace.findOne({
        userId,
        type: 0
      }) // 工作空间的数据
      const userData = await ctx.service.user.find({ userId }) // 用户的数据

      const cookie = `user_id= ${userId};token=${token}`
      let entId
      try {
        userResult = await ctx.curl(userinfoUrl, {
          method: 'GET',
          data: {},
          headers: {
            Cookie: cookie
          },
          dataType: 'json',
          rejectUnauthorized: false
        })
        if (userResult.status === 200 && userResult.data.status === '0') {
          userName_creat = userResult.data.result.name
          // this.ctx.logger.info('userResult is:', userResult)
          entId = userResult.data.result.enterprise_id || ''
        }
      } catch (error) {
        this.ctx.logger.info('get user info error:', error)
        ctx.body = getResponseBody(null, false, error, 400)
      }

      // userName_creat = "admin"
      const result = {
        data: {
          result: {
            access_token: token,
            user_info: {
              name: userName_creat || 'admin'
            }
          }
        }
      }
      if (!userData || !userData.length) {
        await ctx.service.user.create({
          userId,
          entId,
          token: [token],
          userName: userName_creat,
          isDmcLogin: 1
        })
      } else {
        await ctx.service.user.update(
          { userId },
          { $addToSet: { token: token }, entId }
        )
      }
      if (defaultData) {
        data = {
          workspaceData: defaultData,
          userInfo: result
        }
        ctx.body = getResponseBody(data)
        return
      }
      const workspaceBody = {
        name: userName_creat + '的工作空间',
        userId,
        type: 0
      }
      const workspaceData = await ctx.service.workspace.create(workspaceBody)
      data = {
        workspaceData: workspaceData,
        userInfo: result
      }
      ctx.body = getResponseBody(data)
    } else {
      ctx.body = getResponseBody(null, false, checkResult, 400)
      return
    }
  }

  async register() {
    const { ctx, config } = this
    const userName = ctx.request.body.userName
    const passWord = ctx.request.body.passWord
    const token = ctx.app.jwt.sign({ userName, passWord }, config.jwt.secret, {
      expiresIn: '18000s'
    })
    let a
    const decode = ctx.app.jwt.verify(token, config.jwt.secret)
    try {
      a = ctx.app.jwt.verify('eweqeweeqewewqe22222', config.jwt.secret)
    } catch (e) {}
    if (passWord === decode.passWord) {
    }
    ctx.body = getResponseBody(token)
  }
  async checkToken() {
    const { ctx, config } = this
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    if (!config.ucenter) {
      ctx.body = getResponseBody(token)
      return
    }
    try {
      const userData = await ctx.service.user.findOne({ userId })
      if (!userData.isDmcLogin) {
        ctx.body = getResponseBody(null, false, '登录已过期，请重新登录', 401)
        return
      } else if (userData.isDmcLogin === 1) {
        // dmc登录
        // const checkTokenUrl =
        //   this.ctx.app.config.dmcAddress.address +
        //   '/bdp/api/folder/get_dmc_tree_with_tblist'
        const checkTokenUrl =
          this.ctx.app.config.dmcAddress.address +
          '/api/ucenter/check/valid_token'
        const checkResult = await ctx.curl(checkTokenUrl, {
          method: 'GET',
          data: {
            valid_token: token,
            pro_name: 'seatom'
          },
          dataType: 'json',
          timeout: 100000,
          rejectUnauthorized: false
        })
        if (
          checkResult &&
          checkResult.status == 200 &&
          checkResult.data.status != '0'
        ) {
          // console.log(checkResult)
          this.ctx.logger.info('dmc数据表校验失败', checkResult.data)
          ctx.body = getResponseBody(
            checkResult.data,
            false,
            '登录已过期，请重新登录',
            401
          )
          return
        }
        ctx.body = getResponseBody('token校验成功')
      } else {
        ctx.body = getResponseBody('token校验成功')
      }
    } catch (e) {
      this.ctx.logger.info('组内-请求dmc验证token 过期 error:', e)
      ctx.body = getResponseBody(e, false, '登录已过期，请重新登录', 401)
    }
  }
  async getProductList() {
    const { ctx, config } = this
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    const isNewVersion = compareVersion(config.dmcAddress.version, '2.1.6') > 0
    let userlistUrl
    if (isNewVersion) {
      userlistUrl = config.dmcAddress.address + '/api/ucenter/product/list/v2'
    } else {
      userlistUrl = config.dmcAddress.address + '/api/ucenter/product/list'
    }

    const newProduct = [
      {
        product_category_name: 'data_manage',
        product_category_name_ch: '大数据治理与服务',
        product_category_id: 'cat_6a56a5377b_data_manage',
        product_list: []
      },
      {
        product_category_name: 'data_apply',
        product_category_name_ch: '大数据应用与可视化',
        product_category_id: 'cat_6a56a5377b_data_apply',
        product_list: []
      },
      {
        product_category_name: 'actual_app',
        product_category_name_ch: '业务实战应用',
        product_category_id: 'cat_6a56a5377b_actual_app',
        product_list: []
      },
      {
        product_category_name: '',
        product_category_name_ch: '默认分类',
        product_category_desc: '默认分类',
        product_list: []
      }
    ]
    const result = await ctx.curl(userlistUrl, {
      method: 'GET',
      data: {},
      headers: {
        Cookie: 'token=' + token + ';' + 'user_id=' + userId + ';'
      },
      dataType: 'json',
      timeout: 10000,
      rejectUnauthorized: false
    })
    if (result.status === 200 && result.data.status === '0') {
      if (!isNewVersion) {
        for (let index = 0; index < result.data.result.length; index++) {
          const element = result.data.result[index]
          switch (element.product_category) {
            case 'data_manage':
              newProduct[0].product_list.push(element)
              break
            case 'data_apply':
              newProduct[1].product_list.push(element)
              break
            case 'actual_app':
              newProduct[2].product_list.push(element)
              break
            default:
              newProduct[3].product_list.push(element)
              break
          }
        }
        ctx.body = getResponseBody(newProduct)
        return
      }
      result.data.result =
        result.data.result &&
        result.data.result.map(item => {
          delete item.attribute
          delete item.fix_attributes
          item.type = 1 // TODO:临时处理的 list 接口没有type自定义
          return item
        })

      ctx.body = getResponseBody(result.data.result)
    } else {
      ctx.body = getResponseBody('', false, 'cookie error', 201)
    }
  }

  /**
   * 同步dmc用户列表
   */
  async synchronizeDmcUsers() {
    const { ctx, config } = this

    await this.ctx.service.user.checkDmcUsersAndCreate()

    ctx.body = getResponseBody(null)
  }

  async getUserRoleList() {
    const { ctx, config } = this
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    const url =
      this.ctx.app.config.dmcAddress.address + '/api/ucenter/user/role/list'
    let res = {}
    try {
      res = await ctx.curl(url, {
        method: 'GET',
        data: { user_id: userId },
        headers: {
          Cookie: 'token=' + token + ';' + 'user_id=' + userId + ';'
        },
        dataType: 'json',
        timeout: 100000,
        rejectUnauthorized: false
      })
    } catch (error) {
      ctx.body = getResponseBody([
        {
          role_name: '伏羲大屏管理员',
          role_type: 1
        }
      ])
      return
    }

    let a = [],
      b = [],
      c = []
    if (res && res.data && res.data.status == '0') {
      for (let index = 0; index < res.data.result.role_infos.length; index++) {
        const role_info = res.data.result.role_infos[index]
        if (
          role_info.role_name == '伏羲大屏管理员' ||
          role_info.role_name == '超级管理员'
        ) {
          a.push({
            role_name: role_info.role_name,
            role_type: 1
          })
        }
        if (role_info.role_name == '伏羲大屏编辑者') {
          b.push({
            role_name: role_info.role_name,
            role_type: 2
          })
        }
        if (role_info.role_name == '伏羲大屏使用者') {
          c.push({
            role_name: role_info.role_name,
            role_type: 3
          })
        }
      }
      if (a.length) {
        ctx.body = getResponseBody(a)
        return
      } else if (b.length) {
        ctx.body = getResponseBody(b)
        return
      } else if (c.length) {
        ctx.body = getResponseBody(c)
        return
      } else {
        ctx.body = getResponseBody([
          {
            role_name: '伏羲大屏管理员',
            role_type: 1
          }
        ])
      }
    } else {
      ctx.body = getResponseBody(res, false, '用户登录失效，请重新登录', 400)
      return
    }
  }

  async getUserConfigInfo() {
    const { ctx, config } = this
    let userResult;
    const userId = ctx.request.header.sysuserid
    const token = ctx.request.header.systoken
    const cookie = `user_id= ${userId};token=${token}`
    ctx.logger.info('拼好的cookie为：', cookie)
    const userinfoUrl = config.dmcAddress.address + '/api/ucenter/user/info'
    ctx.logger.info('拼好的userinfoUrl为：', userinfoUrl)
    try {
      userResult = await ctx.curl(userinfoUrl, {
        method: 'GET',
        data: {
          sys_pro_name: 'dmc',
          dmc_request: 1
        },
        headers: {
          Cookie: cookie
        },
        dataType: 'json',
        rejectUnauthorized: false
      })
      // ctx.logger.info('userResult is:', userResult.data.result)
      ctx.logger.info('status is:', userResult.data.status)
      // ctx.body = getResponseBody(userResult)
      if (userResult.status === 200 && userResult.data.status === '0') {
        const preciseIp = ctx.request.header['x-forwarded-for']
        if(preciseIp){
          ctx.logger.info('获取到的精确 ip 为', preciseIp)
          const ip = preciseIp.split(',')[0].trim()
          userResult.data.result.personalization_setting.watermark_config.ip = ip
        }
        // userName_creat = userResult.data.result.name
        // ctx.logger.info('userResult is:', userResult.data.result)
        // entId = userResult.data.result.enterprise_id || ''
        ctx.body = getResponseBody(userResult.data.result)
        return
      }
      else {
        ctx.logger.info('result is', userResult.res.data.errstr)
        throw new SeatomException(400, userResult.res.data.errstr)
      }
    } catch (error) {
      ctx.logger.info('get user info error:', error)
      // throw new SeatomException(400, error)
      ctx.body = getResponseBody(null, false, error, 400)
    }
  }
}

module.exports = userController
