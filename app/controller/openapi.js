'use strict'
// openapi开放给第三方接口
const _ = require('lodash')
const fs = require('fs-extra')
const path = require('path')
const Controller = require('egg').Controller
const {
  getResponseBody,
  checkSign,
  getLoggerModel,
  getResponseError,
  getCompsByLayer
} = require('../extend/utils')
const { encryptPath } = require('../extend/crypto')
const { cipher, decipher } = require('../extend/crypto')
const base64 = require('base-64')
const { EXPORT_COMTYPE } = require('../extend/constant')
const md5 = require('md5')

class OpenApiController extends Controller {
  // 查询用户消息
  async index() {
    const { ctx } = this
    const body = ctx.request.body
    const data = await ctx.service.message.find(body)
    ctx.body = getResponseBody(data)
  }
  // 创建用户分享信息
  async getToken() {
    const { ctx, config } = this
    const body = ctx.request.body
    const { sign, userId } = body
    const sign1 = base64.encode(config.serverIp)
    if (!sign || !userId) {
      return (ctx.body = getResponseBody(null, false, '请传入参数', 400))
    }
    if (sign1 !== sign) {
      return (ctx.body = getResponseBody(
        null,
        false,
        '请传入正确令牌参数',
        400
      ))
    }
    const token = cipher(`${sign}&&${userId}`)
    ctx.body = getResponseBody(token)
  }
  // 自动更新时间接口
  async autoUpdate() {
    const { ctx, config } = this
    const sign = base64.encode(config.serverIp)
    const { screenId, status, time } = ctx.request.body
    if (!screenId) {
      return (ctx.body = getResponseBody(
        null,
        false,
        '请传入大屏screenId',
        400
      ))
    }
    const fuxiToken = ctx.request.header['fuxi-token']
    const fuxiPlatform = ctx.request.header['fuxi-platform']
    const userId = decipher(fuxiToken).split('&&')[1] || ''
    if (!checkSign(sign, fuxiToken, fuxiPlatform)) {
      return (ctx.body = getResponseBody(null, false, 'token参数校验失败', 400))
    }
    const updateDate = {
      'dataConfig.dataResponse.autoUpdate.time': time,
      'dataConfig.dataResponse.autoUpdate.enable': status || false
    }
    const comdata = await ctx.service.component.find({ screenId })
    const updateArr = comdata.map(d => {
      return {
        updateOne: {
          filter: { screenId, id: d.id },
          update: updateDate
        }
      }
    })
    const res = await ctx.service.component.bulkWrite(updateArr)
    ctx.body = getResponseBody(res)
  }
  // 获取大屏列表接口
  async project() {
    const { ctx, config } = this
    const { workspaceId } = ctx.request.body
    const sign = base64.encode(config.serverIp)
    const fuxiToken = ctx.request.header['fuxi-token']
    const fuxiPlatform = ctx.request.header['fuxi-platform']
    const userId = decipher(fuxiToken).split('&&')[1]
    if (!checkSign(sign, fuxiToken, fuxiPlatform)) {
      return (ctx.body = getResponseBody(null, false, 'token参数校验失败', 400))
    }
    const workspace = await ctx.service.workspace.findOne({
      id: workspaceId,
      userId
    })
    let projects
    if (workspaceId == 0) {
      projects = await ctx.service.project.find({})
    } else {
      if (!workspace) {
        return (ctx.body = getResponseBody(null, false, '没有这个空间', 400))
      }
      projects = await ctx.service.project.find({ workspaceId })
    }
    const data = []
    const screenObj = {}
    const screenIds = []
    projects.forEach(value => {
      const project = value.toJSON()
      data.push(
        new Promise(async (resolve, reject) => {
          try {
            let screens
            screens = await ctx.service.screen.find({
              projectId: project.id,
              workspaceId: project.workspaceId
            })
            project.screens = screens.map(screen => {
              let screenData = _.pick(screen, [
                'screenType',
                'type',
                'name',
                'createdAt',
                'updatedAt',
                'id',
                'templateId',
                'isScreentpl',
                'projectId',
                'isDynamicScreen',
                'isHistoryScreen'
              ])
              let { config: screenConfig = {} } = screen
              const start =
                screenConfig['thumbnail'] &&
                screenConfig['thumbnail'].indexOf('/public/')
              const thumbnail =
                screenConfig['thumbnail'] &&
                screenConfig['thumbnail'].substring(start + 7).split('?')[0]
              const encryptThumbnail = thumbnail && encryptPath(thumbnail)
              _.extend(screenData, {
                thumbnail: screenConfig['thumbnail'],
                encryptThumbnail: encryptThumbnail && encryptThumbnail,
                isPublic: false,
                shareUrl: '',
                width: screenConfig['width'],
                heiht: screenConfig['height'],
                isConnect: screen.isConnect ? screen.isConnect : false // 添加是否开启多端控制
              })
              let screenId = screen.id
              screenObj[screenId] = screenData
              screenIds.push(screenId)
              return screenData
            })
            project.screens = project.screens.filter(item => {
              if (item.isScreentpl || item.isDynamicScreen || isHistoryScreen) {
                return false
              } else {
                return true
              }
            })
            let shareDatas = await ctx.service.screenshare.find({
              screenId: { $in: screenIds }
            })
            shareDatas.forEach(shareData => {
              let screenData = screenObj[shareData['screenId']]
              if (screenData) {
                screenData['isPublic'] = shareData['isPublic']
                screenData['shareUrl'] = shareData['shareUrl']
              }
            })
            resolve(project)
          } catch (e) {
            resolve(e)
          }
        })
      )
    })
    const ret = await Promise.all(data)
    ctx.body = getResponseBody(ret)
  }
  // 第三方获取大屏里面的组件列表
  async screenComs() {
    const { ctx, config } = this
    const { screenId, mode = 'edit' } = ctx.request.body
    const sign = base64.encode(config.serverIp)
    const fuxiToken = ctx.request.header['fuxi-token']
    const fuxiPlatform = ctx.request.header['fuxi-platform']
    const userId = decipher(fuxiToken).split('&&')[1]
    if (!checkSign(sign, fuxiToken, fuxiPlatform)) {
      return (ctx.body = getResponseBody(null, false, 'token参数校验失败', 400))
    }
    const filter = { screenId }
    const projection = {
      _id: 0,
      __v: 0,
      staticData: 0,
      controlConfig: 0,
      events: 0,
      actions: 0
    }
    // mode 分为 edit-编辑模式； read-预览模式。
    if (mode === 'edit') {
      delete projection.controlConfig // projection 不能混合指定
      delete projection.events
      delete projection.actions
    }
    let res = await ctx.service.component.find(filter, projection)
    // 处理子组件
    let childIds = _.reduce(
      res,
      (arr, d) => {
        arr.push(...d.children)
        return arr
      },
      []
    )
    const childData = await ctx.service.component.find(
      { id: { $in: childIds } },
      projection
    )
    res.push(...childData)
    // 转换成对象
    res = _.reduce(
      res,
      (obj, v) => {
        v.interactionConfig.linkAge =
          v.interactionConfig &&
          v.interactionConfig.linkAge &&
          v.interactionConfig.linkAge.map(item => {
            const source = item.source
            item.linkageConfig =
              item.linkageConfig &&
              item.linkageConfig.map(one => {
                one.componentList = one.componentList.map(componentList => {
                  if (!componentList.sourceType) {
                    componentList.sourceType = source
                    return componentList
                  } else {
                    return componentList
                  }
                })
                return one
              })
            return item
          })
        obj.push(v)
        return obj
      },
      []
    )
    ctx.body = getResponseBody(res)
  }
  // 根据组件id获取组件实例
  async comInfo() {
    const { ctx, config } = this
    const body = ctx.request.body
    const sign = base64.encode(config.serverIp)
    const fuxiToken = ctx.request.header['fuxi-token']
    const fuxiPlatform = ctx.request.header['fuxi-platform']
    const userId = decipher(fuxiToken).split('&&')[1]
    if (!checkSign(sign, fuxiToken, fuxiPlatform)) {
      return (ctx.body = getResponseBody(null, false, 'token参数校验失败', 400))
    }
    if (!body || !body.componentId) {
      ctx.body = getResponseBody(null, false, '请求参数错误', 400)
      return
    }
    const data = await ctx.service.component.findOne({
      id: body.componentId
    })
    let filters = []
    if (
      data &&
      data.dataConfig.dataResponse.filters.list &&
      data.dataConfig.dataResponse.filters.list.length
    ) {
      for (
        let index = 0;
        index < data.dataConfig.dataResponse.filters.list.length;
        index++
      ) {
        const element = data.dataConfig.dataResponse.filters.list[index]
        const filterData = await ctx.service.filter.findOne({ id: element.id })
        filters.push(filterData)
      }
    }
    ctx.body = getResponseBody({ comData: data, filters })
  }
  // 获取组件的数据
  async getComData() {
    const { ctx, config } = this
    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const access_token = ctx.header.access_token || null
    const sign = base64.encode(config.serverIp)
    const fuxiToken = ctx.request.header['fuxi-token']
    const fuxiPlatform = ctx.request.header['fuxi-platform']
    const userId = decipher(fuxiToken).split('&&')[1]
    if (!checkSign(sign, fuxiToken, fuxiPlatform)) {
      return (ctx.body = getResponseBody(null, false, 'token参数校验失败', 400))
    }

    const headerParam = {
      systoken: ctx.header.systoken || '',
      sysuserid: userId,
      access_token
    }
    const key = md5(JSON.stringify(Object.assign(bodyParam, queryParam)))
    const enableCache = ctx.request.header['enable-cache'] === 'true' || false
    let value = await ctx.service.cacheservice.get(key)
    // 合并接口对特殊处理
    if (Object.keys(bodyParam)[0] == 0) {
      if (value && enableCache) {
        this.ctx.logger.info('getData数据缓存', key, bodyParam, queryParam)
        ctx.body = value
      }
      let resData = []
      bodyParam.forEach(value => {
        resData.push(
          new Promise(async (resolve, reject) => {
            try {
              const config = Object.assign(value, queryParam, headerParam)
              const res = await ctx.service.datastorage.getData(config)
              resolve(res)
            } catch (e) {
              resolve(e)
            }
          })
        )
      })
      const ret = await Promise.all(resData)
      const returnValue = ret.map(item => {
        return getResponseBody(item)
      })
      if (!value || !enableCache) {
        ctx.body = returnValue
      }
      await ctx.service.cacheservice.set(key, returnValue, 60000)
    } else {
      if (value && enableCache) {
        this.ctx.logger.info('getData数据缓存', key, bodyParam, queryParam)
        ctx.body = getResponseBody(value)
      }
      const config = Object.assign(queryParam, bodyParam, headerParam)
      const res = await ctx.service.datastorage.getData(config)
      if (!value || !enableCache) {
        ctx.body = getResponseBody(res)
      }
      await ctx.service.cacheservice.set(key, res, 60000)
    }
  }

  async comInfotest() {
    const { ctx, config } = this
    const body = ctx.request.body
    if (!body || !body.componentId) {
      ctx.body = getResponseBody(null, false, '请求参数错误', 400)
      return
    }

    const style = await ctx.service.component.findOne({
      id: body.componentId
    })
    let filters = []
    if (
      style &&
      style.dataConfig.dataResponse.filters.list &&
      style.dataConfig.dataResponse.filters.list.length
    ) {
      for (
        let index = 0;
        index < style.dataConfig.dataResponse.filters.list.length;
        index++
      ) {
        const element = style.dataConfig.dataResponse.filters.list[index]
        const filterData = await ctx.service.filter.findOne({ id: element.id })
        filters.push(filterData)
      }
    }

    const queryParam = ctx.query
    const bodyParam = ctx.request.body
    const headerParam = {
      systoken: ctx.header.systoken || '',
      sysuserid: '5f8a77b4a2c2896576e98ebc9fd217b5'
    }
    const key = md5(JSON.stringify(Object.assign(bodyParam, queryParam)))
    const enableCache = ctx.request.header['enable-cache'] === 'true' || false
    let value = await ctx.service.cacheservice.get(key)
    // 合并接口对特殊处理
    if (Object.keys(bodyParam)[0] == 0) {
      if (value && enableCache) {
        this.ctx.logger.info('getData数据缓存', key, bodyParam, queryParam)
        ctx.body = value
      }
      let resData = []
      bodyParam.forEach(value => {
        resData.push(
          new Promise(async (resolve, reject) => {
            try {
              const config = Object.assign(value, queryParam, headerParam, {
                workspaceId: 18
              })
              const res = await ctx.service.datastorage.getData(config)
              resolve(res)
            } catch (e) {
              resolve(e)
            }
          })
        )
      })
      const ret = await Promise.all(resData)
      const returnValue = ret.map(item => {
        return getResponseBody(item)
      })
      if (!value || !enableCache) {
        ctx.body = returnValue
      }
      await ctx.service.cacheservice.set(key, returnValue, 60000)
    } else {
      const config = Object.assign(queryParam, bodyParam, headerParam, {
        workspaceId: 18
      })
      const res = await ctx.service.datastorage.getData(config)
      if (!value || !enableCache) {
        ctx.body = getResponseBody({
          data: res,
          style,
          filters,
          scripts: 'https://fuxi.haizhi.com' + style.requirePath
        })
      }
      await ctx.service.cacheservice.set(key, res, 60000)
    }
  }

  async comInfoList() {
    const { ctx, config } = this
    const body = ctx.request.body
    const { screenId, groupId } = body
    const sign = base64.encode(config.serverIp)
    const fuxiToken = ctx.request.header['fuxi-token']
    const fuxiPlatform = ctx.request.header['fuxi-platform']
    if (!checkSign(sign, fuxiToken, fuxiPlatform)) {
      return (ctx.body = getResponseBody(null, false, 'token参数校验失败', 400))
    }
    if (!body || !screenId) {
      ctx.body = getResponseBody(null, false, '请求参数错误', 400)
      return
    }
    const res = await ctx.service.screen.findOne({ id: body.screenId })
    const layerTree = res.layerTree || []
    const compIds = getCompsByLayer(layerTree, groupId)
    const comps = await ctx.service.component.find({
      id: { $in: compIds }
    })
    const styles = comps.map(comp => {
      const obj = {
        id: comp.id
      }
      obj.attr = comp.attr
      return obj
    })
    ctx.body = getResponseBody(styles)
  }

  async userComList() {
    const { ctx, config } = this
    const body = ctx.request.body
    // const userId = body.userId
    const sign = base64.encode(config.serverIp)
    const fuxiToken = ctx.request.header['fuxi-token']
    const fuxiPlatform = ctx.request.header['fuxi-platform']
    const userId = decipher(fuxiToken).split('&&')[1]
    if (!checkSign(sign, fuxiToken, fuxiPlatform)) {
      return (ctx.body = getResponseBody(null, false, 'token参数校验失败', 400))
    }
    const workspaceData = await ctx.service.workspace.findOne({ userId })
    if (!workspaceData) {
      ctx.body = getResponseBody(null, false, '用户id不存在', 400)
    }
    const workspaceId = workspaceData.id
    const screenIds = []
    const screenDataList = await ctx.service.screen.find({ workspaceId })
    for (let s = 0; s < screenDataList.length; s++) {
      const screenData = screenDataList[s]
      screenIds.push(screenData.id)
    }
    const componentDataList = await ctx.service.component.findcomList(
      {
        screenId: { $in: screenIds }
      },
      {
        id: 1,
        orginName: 1,
        alias: 1
      }
    )
    ctx.body = getResponseBody(componentDataList)
  }

  async screenComList() {
    const { ctx, config } = this
    const body = ctx.request.body
    let screenIds = body.screenIds
    const sign = base64.encode(config.serverIp)
    const fuxiToken = ctx.request.header['fuxi-token']
    const fuxiPlatform = ctx.request.header['fuxi-platform']
    const userId = decipher(fuxiToken).split('&&')[1]
    if (!checkSign(sign, fuxiToken, fuxiPlatform)) {
      return (ctx.body = getResponseBody(null, false, 'token参数校验失败', 400))
    }
    const comInfo = await ctx.service.component.find({
      screenId: { $in: screenIds },
      comType: { $in: EXPORT_COMTYPE.COPYCOMTYPE }
    })
    if (comInfo) {
      // 获取面板大屏id
      for (let index = 0; index < comInfo.length; index++) {
        const element = comInfo[index]
        if (element.config.screens && element.config.screens.length) {
          screenIds.push(...element.config.screens.map(item => item.id))
        }
      }
    }
    const componentDataList = await ctx.service.component.findcomList(
      {
        screenId: { $in: screenIds }
      },
      {
        id: 1,
        orginName: 1,
        alias: 1
      }
    )
    ctx.body = getResponseBody(componentDataList)
  }

  async screenLayers() {
    const { ctx, config } = this
    const screenId = ctx.query.screenId
    const sign = base64.encode(config.serverIp)
    const fuxiToken = ctx.request.header['fuxi-token']
    const fuxiPlatform = ctx.request.header['fuxi-platform']
    const userId = decipher(fuxiToken).split('&&')[1]
    if (!checkSign(sign, fuxiToken, fuxiPlatform)) {
      return (ctx.body = getResponseBody(null, false, 'token参数校验失败', 400))
    }
    let originLayerTree = []
    try {
      const res = await ctx.service.screen.findOne({ id: screenId })
      originLayerTree = (res && res.layerTree) || []
      ctx.body = getResponseBody(originLayerTree)
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })

      this.ctx.logger.info(
        getLoggerModel({
          message: '/openapi/screenLayers error',
          data: {
            error: error.toString(),
            stack: error.stack
          }
        })
      )
    }
  }

  async getuserInfo() {
    const { ctx, config } = this
    const sign = base64.encode(config.serverIp)
    const fuxiToken = ctx.request.header['fuxi-token']
    const fuxiPlatform = ctx.request.header['fuxi-platform']
    const userId = decipher(fuxiToken).split('&&')[1]
    if (!checkSign(sign, fuxiToken, fuxiPlatform)) {
      return (ctx.body = getResponseBody(null, false, 'token参数校验失败', 400))
    }
    const projection = {
      token: 0,
      _id: 0,
      __v: 0
    }
    try {
      const workspaceData = await ctx.service.workspace.findOne({ userId })
      const userData = await ctx.service.user.findOne({ userId }, projection)
      if (workspaceData) {
        ctx.body = getResponseBody({
          userData,
          workspaceData
        })
      } else {
        return (ctx.body = getResponseBody(
          null,
          false,
          '该用户空间不存在',
          400
        ))
      }
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })
    }
  }
}

module.exports = OpenApiController
