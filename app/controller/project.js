'use strict'
// 大屏分组信息
const Controller = require('egg').Controller
const _ = require('lodash')
const { getResponseBody } = require('../extend/utils')
const { screenDataTransfer } = require('../utils/screen')
const path = require('path')
const SeatomException = require('../exception/seatomException')
const { ERROR_CODES } = require('../extend/constant')

class ProjectController extends Controller {
  async index() {
    const { ctx } = this
    const workspaceId = ctx.query.workspaceId
    const userId = ctx.request.header.sysuserid
    const defaultData = await ctx.service.project.findOne({
      workspaceId,
      type: 0
    })
    if (!defaultData) {
      await ctx.service.project.create({
        workspaceId,
        type: 0,
        name: '未分组'
      })
    }
    const workspace = await ctx.service.workspace.findOne({
      id: workspaceId,
      userId
    })
    let projects
    if (workspaceId == 0) {
      projects = await ctx.service.project.findToPromise({}).sort({ type: 1 })
    } else {
      if (!workspace || workspace.length === 0) {
        return (ctx.body = getResponseBody(null, false, '没有这个空间', 402))
      }
      projects = await ctx.service.project
        .findToPromise({ workspaceId })
        .sort({ type: 1 })
    }
    const data = []
    // const screenObj = {};
    // const screenIds = [];
    for (const pro of projects) {
      // problem is here
      const obj = pro.toJSON()
      // console.log('pro===',pro);
      // 获取到的是属于这个分类下的数据
      obj.totalScreens = await ctx.service.screen.count({
        projectId: pro.id,
        workspaceId: pro.workspaceId,
        isScreentpl: false,
        isDynamicScreen: false,
        screenType: { $in: ['scene', 'common'] }
      })
      // 还需要获取协同编辑的，协同编辑的是要添加到未分组里的
      // 筛选，workspaceId来判断是否
      // if(pro.name === '未分组') {
      //   const coeditFilter = {
      //     workspaceId: pro.workspaceId,
      //     coeditUserId: userId
      //   }
      //   obj.totalScreens += await ctx.service.screencoedit.count(
      //     coeditFilter,
      //   )
      // }
      data.push(obj)
    }
    // projects.forEach(value => {
    //   const project = value.toJSON()
    //   data.push(
    //     new Promise(async (resolve, reject) => {
    //       try {
    //         let screens
    //         screens = await ctx.service.screen.find({
    //           projectId: project.id,
    //           workspaceId: project.workspaceId,
    //           isScreentpl: false,
    //           isDynamicScreen: false
    //         })
    //         const promises = []
    //         // 处理我共享给别人的大屏
    //         screens.forEach((screeenItem, index) => {
    //           if (!screeenItem.coeditId) {
    //             return
    //           }

    //           screeenItem = screeenItem.toObject()
    //           promises.push(
    //             ctx.service.screencoedit
    //               .getCoeditInfoByCoeditId({
    //                 coeditId: screeenItem.coeditId
    //               })
    //               .then(coeditInfo => {
    //                 screeenItem.coeditInfo = coeditInfo
    //                 screens[index] = screeenItem
    //               })
    //           )
    //         })

    //         if (promises.length) {
    //           await Promise.all(promises)
    //         }

    //         const screenCoeditProjection = {
    //           _id: 0,
    //           __v: 0,
    //           createdAt: 0,
    //           updatedAt: 0
    //         }

    //         // 处理别人共享给我的大屏
    //         // 根据workspaceId、projectId找出协同列表
    //         const screenCoeditList = await ctx.service.screencoedit.find(
    //           {
    //             projectId: project.id,
    //             workspaceId: project.workspaceId
    //           },
    //           screenCoeditProjection
    //         )

    //         if (screenCoeditList.length) {
    //           // 找到别人共享给我的大屏列表
    //           const coeditScreens =
    //             await ctx.service.screencoedit.getCoeditScreensScreenCoeditList(
    //               {
    //                 screenCoeditList,
    //                 projectId: project.id
    //               }
    //             )

    //           // console.log('coeditScreens', coeditScreens)

    //           if (coeditScreens.length) {
    //             // 简单做一下合并
    //             screens = coeditScreens.concat(screens)
    //           }
    //         }

    //         project.screens = screens.map(screen => {
    //           let screenData = screenDataTransfer(screen)
    //           let screenId = screen.id
    //           screenObj[screenId] = screenData
    //           screenIds.push(screenId)
    //           return screenData
    //         })
    //         let shareDatas = await ctx.service.screenshare.find({
    //           screenId: { $in: screenIds }
    //         })
    //         shareDatas.forEach(shareData => {
    //           let screenData = screenObj[shareData['screenId']]
    //           if (screenData) {
    //             screenData['isPublic'] = shareData['isPublic']
    //             screenData['shareUrl'] = shareData['shareUrl']
    //           }
    //         })
    //         resolve(project)
    //       } catch (e) {
    //         this.ctx.logger.info(e)
    //         resolve(e)
    //       }
    //     })
    //   )
    // })
    // const ret = await Promise.all(data)
    ctx.body = getResponseBody(data)
  }

  async create() {
    const { ctx } = this
    const body = ctx.request.body
    const workspaceId = body.workspaceId
    const defaultData = await ctx.service.project.find({
      name: body.name,
      workspaceId
    })
    if (defaultData && defaultData.length) {
      ctx.body = getResponseBody(null, false, '项目名称已存在', 400)
      return
    }
    if (body.type === 0) {
      const defaultData = await ctx.service.project.find({ type: 0 })
      if (defaultData.length) {
        ctx.body = getResponseBody(null, false, '未分组已存在', 400)
        return
      }
    }
    const data = await ctx.service.project.create(body)
    ctx.body = getResponseBody(data)
  }

  async delete() {
    const { ctx } = this
    const body = ctx.request.body
    const id = body.id
    const workspaceId = body.workspaceId
    const defaultData = await ctx.service.project.findOne({
      workspaceId,
      type: 0
    })
    const projectId = defaultData.id
    const screenData = await ctx.service.screen.find({ projectId: id })
    if (screenData && screenData.length) {
      await ctx.service.screen.update(
        { projectId: id },
        { projectId },
        { multi: true }
      )
    }

    const screenCoeditData = await ctx.service.screencoedit.find({
      projectId: id,
      workspaceId
    })
    // 协同大屏移动到未分组
    if (screenCoeditData && screenCoeditData.length) {
      await ctx.service.screencoedit.updateMany(
        { projectId: { $eq: id }, workspaceId: { $eq: workspaceId } },
        { projectId }
      )
    }

    const data = await ctx.service.project.delete({ id })
    ctx.body = getResponseBody(data)
  }

  async update() {
    const { ctx } = this
    const body = ctx.request.body
    const workspaceId = body.workspaceId
    const defaultData = await ctx.service.project.findOne({
      name: body.name,
      workspaceId
    })
    if (defaultData && defaultData.id !== body.id) {
      ctx.body = getResponseBody(null, false, '项目名称已存在', 400)
      return
    }
    const data = await ctx.service.project.update({ id: body.id }, body)
    ctx.body = getResponseBody(data)
  }

  // 外部对接接口for data-portal-server
  async getProjects() {
    const { ctx } = this
    const userId = ctx.request.query.userId
    const workspace = await ctx.service.workspace.findOne({ userId })
    let projects
    if (workspace && workspace.id == 0) {
      projects = await ctx.service.project.find({}, { type: 0, workspaceId: 0 })
    } else {
      if (!workspace || workspace.length === 0) {
        return (ctx.body = getResponseBody(null, false, '无项目空间', 401))
      }
      const projection = {
        type: 0
      }
      projects = await ctx.service.project.find(
        { workspaceId: workspace.id },
        projection
      )
    }

    let data = []
    for (let i = 0, len = projects.length; i < len; i++) {
      const project = projects[i].toJSON()
      let screens
      screens = await ctx.service.screen.find({
        projectId: project.id,
        workspaceId: project.workspaceId
      })
      project.screens = screens.map(screen => {
        const screenData = _.pick(screen, [
          'name',
          'id',
          'isDynamicScreen',
          'isScreentpl'
        ])
        return screenData
      })

      project.screens = project.screens.filter(item => {
        if (item.isScreentpl || item.isDynamicScreen) {
          return false
        }
        return true
      })
      data.push(project)
    }
    data = data.map(item => {
      delete item.workspaceId
      return item
    })
    const res = { id: 'root', name: 'root', children: [] }
    res.children = data
    ctx.body = getResponseBody(res)
  }
}

module.exports = ProjectController
