'use strict'
// 字体
const _ = require('lodash')
const fs = require('fs-extra')
const path = require('path')
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
var crypto = require('crypto')

class FontController extends Controller {
  async upload() {
    const { ctx, config } = this
    const fontName = ctx.request.body.fontName
    // const userId = ctx.request.body.userId
    const description = ctx.request.body.description || ''
    if (!ctx.request.files || !ctx.request.files.length) {
      ctx.body = getResponseBody(
        null,
        false,
        '无法创建，上传字体文件为空！',
        400
      )
      return
    }
    const fontlist = await ctx.service.font.find({ fontName })
    if (fontlist && fontlist.length) {
      ctx.body = getResponseBody(null, false, '字体名称不可以重复', 400)
      return
    }
    const file = ctx.request.files[0]
    const md5data = fs.readFileSync(file.filepath)
    const fileName = await crypto
      .createHash('md5')
      .update(md5data, 'utf8')
      .digest('base64')
    const fileNameArr = file.filename.split('.')
    const fileType = fileNameArr[fileNameArr.length - 1] // 获取文件格式
    let filePath, fontUrl, filesize
    filePath = path.resolve(
      config.resourcePath,
      './fontlist/' + fileName + '.' + fileType
    )
    fontUrl = '/public/fontlist/' + fileName + '.' + fileType
    if (!fs.existsSync(filePath)) {
      await fs.moveSync(file.filepath, filePath)
    }
    const stats = fs.statSync(filePath)
    filesize = (stats.size / 1024 / 1024).toFixed(3)
    const formatDate = new Date()
    formatDate.setHours(formatDate.getHours() + 8)
    const fontData = {
      fontUrl,
      fontName,
      description,
      size: filesize
    }
    const data = await ctx.service.font.create(fontData)
    ctx.body = getResponseBody(data)
  }

  async index() {
    const { ctx } = this
    // const userId = ctx.query.userId;
    let condition = {}
    const data = await ctx.service.font.find(condition)
    ctx.body = getResponseBody(data)
  }

  async info() {
    const { ctx } = this
    const projection = {
      _id: 0
    }
    const data = await ctx.service.font.findOne(
      { id: ctx.query.id },
      projection
    )
    ctx.body = getResponseBody(data)
  }

  async update() {
    const { ctx } = this
    const body = ctx.request.body
    const id = ctx.query.id
    // const userId = ctx.query.userId;
    const fontName = body.fontName
    const defaultData = await ctx.service.font.findOne({
      fontName,
      id: { $ne: id }
    })
    if (defaultData) {
      ctx.body = getResponseBody(null, false, '字体名称不可以重复', 400)
      return
    }
    const data = await ctx.service.font.update({ id }, body)
    ctx.body = getResponseBody(data)
  }

  async delete() {
    const { ctx } = this
    const body = ctx.request.body
    const id = body.id
    // const userId = body.userId;
    const data = await ctx.service.font.delete({ id })
    ctx.body = getResponseBody(data)
  }
}

module.exports = FontController
