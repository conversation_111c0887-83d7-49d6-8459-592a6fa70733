'use strict'

const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')

const { encryptfilter } = require('../extend/crypto')

const fs = require('fs-extra')
const path = require('path')

class SystemFilterController extends Controller {
  async index() {
    const { ctx } = this
    const data = await ctx.service.systemfilter.find()
    const res = data.map(item => {
      if (item.content.indexOf('encrypt_') == -1) {
        item.content = 'encrypt_' + encryptfilter(item.content)
        return item
      }
      return item
    })
    ctx.body = getResponseBody(res)
  }
  async export() {
    const { ctx, config } = this
    const query = ctx.query
    const ids = query.ids ? { id: { $in: JSON.parse(query.ids) } } : {}
    console.log(ids)
    const data = await ctx.service.systemfilter.find(ids)
    const t = new Date().getTime()
    const filePath = path.resolve(config.resourcePath, `./screenData`)
    const filterjson = `${filePath}/filter${t}.json`
    await fs.writeFileSync(filterjson, JSON.stringify({ data }), err => {
      if (err) throw err
    })
    ctx.attachment(filterjson)
    ctx.set('Content-Type', 'application/octet-stream')
    ctx.body = fs.createReadStream(filterjson)
  }
  async import() {
    const { ctx, config } = this
    const body = ctx.query || {}
    if (!ctx.request.files || !ctx.request.files.length) {
      ctx.body = getResponseBody(null, false, '无法创建，导入文件为空！', 400)
      return
    }
    const file = ctx.request.files[0]
    const t = new Date().getTime()
    const filePath = path.resolve(config.resourcePath, `./screenData`)
    const filterjson = `${filePath}/filter${t}.json`
    await fs.writeFileSync(filterjson, JSON.stringify({ data }), err => {
      if (err) throw err
    })
    ctx.body = getResponseBody(data)
  }
  async create() {
    const { ctx } = this
    const body = ctx.request.body
    const data = await ctx.service.systemfilter.create(body)
    ctx.body = getResponseBody(data)
  }
  async import() {
    const { ctx } = this
    const body = ctx.request.body
    const data = await ctx.service.systemfilter.find()
    ctx.body = getResponseBody(data)
  }
  async update() {
    const { ctx } = this
    const id = ctx.query.id
    const body = ctx.request.body
    const data = await ctx.service.systemfilter.update({ id: id }, body)
    ctx.body = getResponseBody(data)
  }
  async delete() {
    const { ctx } = this
    const id = ctx.request.body.id
    const data = await ctx.service.systemfilter.delete({ id })
    ctx.body = getResponseBody(data)
  }
}

module.exports = SystemFilterController
