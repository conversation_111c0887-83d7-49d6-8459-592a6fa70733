'use strict'
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
const path = require('path')
const { spawnSync, exec } = require('child_process')
const fs = require('fs')
const compressing = require('compressing')
class upgradeController extends Controller {
  async upgradeComponent() {
    const { ctx, app } = this
    const component_base_dir = path.join(app.baseDir, '..', 'atom')
    const file = ctx.request.files[0]
    const baseName = path.basename(file.filename, '.tar.gz')
    const component = path.join(component_base_dir, baseName)
    if (fs.existsSync(component)) {
      const bak = path.join(component + '_bak')
      fs.renameSync(component, bak)
    } else {
      ctx.body = getResponseBody(null, false, '不存在此组件不能更新')
      return
    }
    await compressing.tgz.uncompress(file.filepath, component_base_dir)
    process.chdir(component)
    const res = new Promise(function (resolve, reject) {
      exec('seatom publish-prod ', (error, stdout, stderr) => {
        if (error) {
          console.error(`exec error: ${error}`)
          reject({
            error: `exec error: ${error}`,
            stdout,
            stderr,
            message: '更新失败'
          })
          return
        }
        resolve({
          stdout,
          stderr,
          message: '更新成功'
        })
      })
    })
    try {
      const success = await res
      ctx.body = getResponseBody({ fileData: success })
    } catch (error) {
      ctx.body = getResponseBody({ fileData: error }, false, '发布失败', 444)
    }
  }
  async upgradeFront() {
    const { ctx, app } = this
    const component_base_dir = path.join(app.baseDir, '..', 'seatom-fallback')
    const file = ctx.request.files[0]
    const baseName = path.basename(file.filename, '.tar.gz')
    const component = path.join(component_base_dir, baseName)
    const bak = path.join(component + '_bak')
    if (fs.existsSync(component)) {
      fs.renameSync(component, bak)
    } else {
      ctx.body = getResponseBody(null, false, '不存在此组件不能更新')
      return
    }
    await compressing.tgz.uncompress(file.filepath, component_base_dir)
    process.chdir(component)
    const res = new Promise(function (resolve, reject) {
      exec(' pm2 restart index-prod ', (error, stdout, stderr) => {
        if (error) {
          //回退
          if (fs.existsSync(bak)) {
            const fail = path.join(component + '_fail')
            fs.renameSync(component, fail)
            fs.renameSync(bak, component)
          }
          console.error(`exec error: ${error}`)
          reject({
            error: `exec error: ${error}`,
            stdout,
            stderr,
            message: '更新失败'
          })
          return
        }
        resolve({
          stdout,
          stderr,
          message: '更新成功'
        })
      })
    })
    try {
      const success = await res
      ctx.body = getResponseBody({ fileData: success })
    } catch (error) {
      ctx.body = getResponseBody({ fileData: error }, false, '发布失败', 444)
    }
  }

  async upgradeBackEnd() {
    const { ctx, app } = this
    let server
    const type = (ctx.request.body && ctx.request.body.type) || 'all'
    if (type == 'app') {
      const server_base_dir = path.join(app.baseDir, '..', 'seatom-server')
      const file = ctx.request.files[0]
      const baseName = path.basename(file.filename, '.tar.gz')
      server = path.join(server_base_dir, baseName)
      const bak = path.join(server + '_bak')
      if (fs.existsSync(server)) {
        fs.renameSync(server, bak)
      } else {
        ctx.body = getResponseBody(null, false, '不存在此组件不能更新')
        return
      }
      await compressing.tgz.uncompress(file.filepath, server_base_dir)
      process.chdir(server)
    } else {
    }
    const res = new Promise(function (resolve, reject) {
      exec(' npm run dev ', (error, stdout, stderr) => {
        if (error) {
          //回退
          if (fs.existsSync(bak)) {
            const fail = path.join(server + '_fail')
            fs.renameSync(server, fail)
            fs.renameSync(bak, server)
          }
          console.error(`exec error: ${error}`)
          reject({
            error: `exec error: ${error}`,
            stdout,
            stderr,
            message: '更新失败'
          })
          return
        }
        resolve({
          stdout,
          stderr,
          message: '更新成功'
        })
      })
    })
    try {
      const success = await res
      ctx.body = getResponseBody({ fileData: success })
    } catch (error) {
      ctx.body = getResponseBody({ fileData: error }, false, '发布失败', 444)
    }
  }
}

module.exports = upgradeController
