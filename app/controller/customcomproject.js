'use strict'
// 自定义组件列表
const fs = require('fs-extra')
const path = require('path')
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
const { spawn, spawnSync, exec, execSync } = require('child_process')
class CustomComProjectController extends Controller {
  async index() {
    const { ctx } = this
    const res = []
    const data = await ctx.service.customcomproject.find()
    for (let index = 0; index < data.length; index++) {
      const element = data[index]
      const comdata = await ctx.service.customcomponent.find({
        customcomprojectId: element.id
      })
      res.push({
        comdata,
        id: element.id,
        name: element.name,
        createdAt: element.createdAt,
        updatedAt: element.updatedAt
      })
    }
    ctx.body = getResponseBody(res)
  }
  async info() {
    const { ctx } = this
    const res = []
    const data = await ctx.service.customcomproject.find()
    for (let index = 0; index < data.length; index++) {
      const element = data[index]
      const comdata = await ctx.service.customcomponent.find({
        customcomprojectId: element.id
      })
      res.push({
        comdata,
        id: element.id,
        name: element.name,
        createdAt: element.createdAt,
        updatedAt: element.updatedAt
      })
    }
    ctx.body = getResponseBody(res)
  }
  async create() {
    const { ctx } = this
    const body = ctx.request.body
    const data = await ctx.service.customcomproject.create(body)
    ctx.body = getResponseBody(data)
  }
  async update() {
    const { ctx } = this
    const id = ctx.query.id
    const body = ctx.request.body
    const data = await ctx.service.customcomproject.update({ id: id }, body)
    ctx.body = getResponseBody(data)
  }
  async delete() {
    const { ctx } = this
    const id = ctx.request.body.id
    const data = await ctx.service.customcomproject.delete({ id })
    await ctx.service.customcomponent.deleteMany({ customcomprojectId: id })
    ctx.body = getResponseBody(data)
  }
}

module.exports = CustomComProjectController
