'use strict'
const _ = require('lodash')
const fs = require('fs-extra')
const path = require('path')
const Controller = require('egg').Controller
const {
  getResponseBody,
  getScreenshot,
  addConfig,
  compareVersion
} = require('../extend/utils')
const parseConfig = require('../extend/parse-config')
const console = require('console')
class ComThemesController extends Controller {
  async index() {
    const { ctx } = this
    let condition = {}
    condition = ctx.query
    if (condition.themeType && condition.themeType === 'style') {
      const data = await ctx.service.comtheme.find({
        comType: condition.comType
      })
      const styleData = data.filter(item => {
        return !item.themeType || item.themeType === 'style'
      })
      ctx.body = getResponseBody(styleData)
      return
    }
    // if (comType) {
    //   condition.comType = comType;
    // }
    const data = await ctx.service.comtheme.find(condition)
    ctx.body = getResponseBody(data)
  }

  async info() {
    const { ctx } = this
    const body = ctx.request.body
    const packageId = body.packageId
    const id = body.id
    const comDefine = await ctx.service.package.findOne({ id: packageId })
    const comConfig = JSON.parse(comDefine.config)
    addConfig(comConfig.config, 'themePicture')
    const comthemeData = await ctx.service.comtheme.findOne({ id })
    const comthemeDataConfig = comthemeData.config
    const comData = {
      id: comthemeData.id,
      type: 'com',
      name: comthemeData.name,
      comName: comDefine.name,
      comType: comDefine.type,
      alias: comDefine.alias,
      orginName: comDefine.alias,
      version: comDefine.version,
      // parent: null,
      // children: (subData && subData.map(d => d.id)) || [],
      icon: comDefine.icon,
      show: comConfig.show,
      config: _.defaultsDeep(comthemeDataConfig, parseConfig(comConfig.config)), // 将不同版本主题设置进行合并
      controlConfig: JSON.stringify(comConfig.config),
      dataConfig: {
        fields: comConfig.data.fields,
        fieldMapping: comConfig.data.fields.map(f => {
          return {
            source: f.name,
            target: '',
            description: f.description,
            type: f.type,
            status: 0
          }
        })
      },
      interactionConfig: {},
      staticData: comConfig.data.source,
      requirePath: comDefine.path,
      events: comConfig.events,
      actions: comConfig.actions,
      attr: {
        w: comDefine.width,
        h: comDefine.height,
        x: 100,
        y: 100,
        deg: 0,
        opacity: 1,
        lock: false,
        flipH: false,
        flipV: false
      },
      other: {
        isTheme: true
      }
    }
    ctx.body = getResponseBody(comData)
  }

  async create() {
    const { ctx, config } = this
    const body = ctx.request.body
    const packageId = body.packageId
    const name = body.name
    const themeType = body.themeType
    const comDefine = await ctx.service.package.findOne({ id: packageId })
    const comConfig = JSON.parse(comDefine.config)
    const defaultData = await ctx.service.comtheme.find({
      name,
      comType: comDefine.comType
    })
    if (defaultData && defaultData.length) {
      ctx.body = getResponseBody(null, false, '该组件主题名称已存在', 400)
      return
    }
    const data = await ctx.service.comtheme.create({
      name,
      themeType: themeType,
      comType: comDefine.name,
      alias: comDefine.alias,
      orginName: comDefine.alias,
      config: parseConfig(comConfig.config),
      icon: comDefine.icon,
      width: comDefine.width,
      height: comDefine.height
    })
    ctx.body = getResponseBody(data)
  }

  async update() {
    const { ctx } = this
    const id = ctx.query.id
    const body = ctx.request.body
    const isDefault = body.isDefault
    const comType = body.comType
    if (isDefault && comType) {
      await ctx.service.comtheme.update(
        { comType },
        { isDefault: false },
        { multi: true }
      )
    }
    const data = await ctx.service.comtheme.update({ id }, body)
    ctx.body = getResponseBody(data)
  }

  // async update() {
  //   const { ctx } = this;
  //   const data = await ctx.service.comtheme.update({ }, {themeSchemeId:''},{ multi: true });
  //   ctx.body = getResponseBody(data);
  // }

  async delete() {
    const { ctx } = this
    const id = ctx.request.body.id
    const data = await ctx.service.comtheme.delete({ id })
    ctx.body = getResponseBody(data)
  }

  async save() {
    const { ctx, config } = this
    const body = ctx.request.body
    const comthemeId = body.id
    const packageId = body.packageId
    const url = body.url
    // const comthemeData = await ctx.service.comtheme.findOne({ id: comthemeId })
    // const comthemeshotUrl = await ctx.service.comtheme.saveIcon({
    //   comthemeId,
    //   packageId,
    //   comthemeData
    // })
    await ctx.service.comtheme.update({ id: comthemeId }, { icon: url })
    ctx.body = getResponseBody({ url })
  }

  async saveComtheme() {
    const { ctx, config } = this
    const body = ctx.request.body
    const { name, comId, themeType } = body
    const comDefine = await ctx.service.component.findOne({ id: comId })
    const defaultData = await ctx.service.comtheme.find({
      name,
      comType: comDefine.comType
    })
    if (defaultData && defaultData.length) {
      ctx.body = getResponseBody(null, false, '该组件主题名称已存在', 400)
      return
    }
    const packagedata = (
      await ctx.service.package.find({ type: comDefine.comType })
    ).map(d => d.toJSON())
    // 排序，只获取此组件的最新版本
    packagedata.sort((a, b) => {
      const res = compareVersion(a.version, b.version)
      return -res
    })
    const packageId = packagedata[0].id
    // const comConfig = JSON.parse(comDefine.config);

    const data = await ctx.service.comtheme.create({
      name,
      comType: comDefine.comType,
      alias: comDefine.alias,
      orginName: comDefine.alias,
      config: comDefine.config,
      icon: comDefine.icon,
      width: packagedata[0].width,
      height: packagedata[0].height,
      themeType
    })
    const comthemeId = data.id
    const comthemeData = data
    const comthemeshotUrl = await ctx.service.comtheme.saveIcon({
      comthemeId,
      packageId,
      comthemeData
    })
    await ctx.service.comtheme.update(
      { id: comthemeId },
      { icon: comthemeshotUrl }
    )
    data.icon = comthemeshotUrl
    ctx.body = getResponseBody(data)
  }
}

module.exports = ComThemesController
