'use strict'
const _ = require('lodash')
const fs = require('fs-extra')
const path = require('path')
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
const { EXPORT_COMTYPE, DASHBOARD_CODES } = require('../extend/constant')
var crypto = require('crypto')

class ScreenAiController extends Controller {
  async create() {
    const { ctx, config } = this
    const body = ctx.request.body
    const { workspaceId, projectId, name } = body
    // const screenId = body.id || 907871133644726
    // const screenId = body.id || 934702404700510
    const screenId = body.id || 939693668980787
    const t = new Date().getTime()
    // isScreentpl生成模版传true, 以模版创建项目传false
    let isScreentpl
    if (body.isCreateScreen) {
      isScreentpl = false
    } else {
      isScreentpl = !!body.isScreentpl
    }
    const tag = body.tag || []
    // 大屏原id与现id之间的映射
    const screenIdMapping = {}
    const newScreenIdList = []
    // 获取大屏原组件数据
    const comInfoOrigin = await ctx.service.component.find(
      { screenId },
      { _id: 0 }
    )
    for (let index = 0; index < comInfoOrigin.length; index++) {
      const comInfoOriginItem = comInfoOrigin[index]
      if (EXPORT_COMTYPE.COPYCOMTYPE.includes(comInfoOriginItem.comType)) {
        for (let i = 0; i < comInfoOriginItem.config.screens.length; i++) {
          const quoteScreenId = comInfoOriginItem.config.screens[i].id
          const screenInfoNow = await ctx.service.screen.copy({
            screenIdOrigin: quoteScreenId,
            workspaceId,
            projectId,
            name: `${name}${comInfoOriginItem.alias}`,
            isScreentpl,
            tag,
            t
          })
          screenIdMapping[quoteScreenId] = screenInfoNow.id
          if (screenInfoNow.id) {
            newScreenIdList.push(Number(screenInfoNow.id))
          }
        }
      }
    }
    const screenInfoNow = await ctx.service.screen.copy({
      screenIdOrigin: screenId,
      workspaceId,
      projectId,
      name,
      screenIdMapping,
      isScreentpl,
      tag,
      isCreateTpl: true,
      t
    })
    await ctx.service.screen.updateMany(
      { id: { $in: newScreenIdList } },
      { parentId: screenInfoNow.id }
    )
    this.ctx.logger.info('get final copy screenInfo: ', screenInfoNow)
    ctx.body = getResponseBody({
      id: screenInfoNow.id
    })
  }
}

module.exports = ScreenAiController
