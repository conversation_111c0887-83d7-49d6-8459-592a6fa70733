const Controller = require('egg').Controller

const { getResponseBody } = require('../extend/utils')

class ConfigInfoController extends Controller {

  async index () {
    const { ctx } = this
    const data = await ctx.service.configinfo.find()
    ctx.body = getResponseBody(data)
  }

  async update() {
    const { ctx } = this
    const body = ctx.request.body
    console.log(body)
    try {
      const data = await ctx.service.configinfo.update({}, body)
      ctx.body = getResponseBody(data)
      console.log(await ctx.service.configinfo.find())
    } catch (error) {
      ctx.body = getResponseError({ data: error.toString() })
    }
  }

}

module.exports = ConfigInfoController
