'use strict'
// 组件打包
const path = require('path')
let fs = require('fs-extra')
const base64 = require('base-64')
const { spawnSync, exec } = require('child_process')
const compressing = require('compressing')
const _ = require('lodash')
const Controller = require('egg').Controller
const { getResponseBody } = require('../extend/utils')
class PackcomController extends Controller {
  async index() {
    const { ctx } = this
    const body = ctx.request.body
    const data = await ctx.service.packcom.findOne({ id: 1 })
    ctx.body = getResponseBody(data)
  }
  async update() {
    const { ctx, config } = this
    const Comjson = path.resolve(config.resourcePath, './components.json')
    const fileData = await fs.readFileSync(Comjson, 'utf-8')
    const packdata = await ctx.service.packcom.findOne({ id: 1 })
    if (packdata) {
      await ctx.service.packcom.update({ id: 1 }, { comjson: fileData })
    } else {
      await ctx.service.packcom.create({
        id: 1,
        comjson: fileData,
        ispack: false
      })
    }
    const data = await ctx.service.packcom.findOne({ id: 1 })
    ctx.body = getResponseBody(data)
  }
  async getcomjson() {
    const { ctx, config } = this
    const templatePath = path.resolve(config.resourcePath, '../atom-source')
    const Comjson = path.resolve(config.resourcePath, './components.json')
    let comItem = {}
    function fn(templatePath) {
      let files = fs.readdirSync(templatePath)
      for (let index = 0; index < files.length; index++) {
        const item = files[index]
        fs.stat(templatePath + '/' + item, (err, data) => {
          if (data.isFile()) {
            let pathstr = templatePath + '/' + item
            if (
              pathstr.indexOf('index.js') !== -1 &&
              pathstr.indexOf('node_modules') === -1 &&
              pathstr.indexOf('src/packages') === -1
            ) {
              pathstr = pathstr.replace('/index.js', '')
              const packjson = fs.readFileSync(
                `${pathstr}/package.json`,
                'utf-8'
              )
              const comName = JSON.parse(packjson).name
              const comVersion = JSON.parse(packjson).version
              const comcnName = JSON.parse(packjson).chartConfig.cn_name
              comItem[comName] = pathstr + '&&' + comcnName
              fs.writeFile(
                Comjson,
                JSON.stringify(comItem, undefined, 2),
                (err, data) => {}
              )
            }
          } else {
            fn(templatePath + '/' + item)
          }
        })
      }
    }
    fn(templatePath)
    ctx.body = getResponseBody(comItem)
  }
  async pack() {
    const { ctx, config } = this
    const requestBody = ctx.request.body
    const serverIp = requestBody.serverIp || 'http://127.0.0.1:7001'
    const enName = requestBody.enName
    await ctx.service.packcom.update({ id: 1 }, { ispack: true })
    const packdata = await ctx.service.packcom.findOne({ id: 1 })
    if (packdata.ispack) {
      ctx.body = getResponseBody(
        { fileData: '请稍等，组件打包中' },
        false,
        '请稍等，组件打包中',
        400
      )
    }
    const date = new Date()
    const datestr = `${date.getFullYear()}-${date.getMonth() + 1}-${
      date.getDate() + 1
    }`
    const compackagesName = path.resolve(
      config.resourcePath,
      `./commonfiles/compackages_${base64.encode(serverIp)}_${datestr}`
    )
    if (fs.existsSync(compackagesName)) {
      fs.removeSync(compackagesName)
    }
    fs.mkdirSync(compackagesName)

    try {
      for (let index = 0; index < enName.length; index++) {
        const element = enName[index].label
        const compath = await ctx.service.packcom.pack(serverIp, element)
        await fs.moveSync(compath, `${compackagesName}/${element}`)
      }
      await compressing.tgz.compressDir(
        compackagesName,
        `${compackagesName}.tgz`
      )
      await ctx.service.packcom.update({ id: 1 }, { ispack: false })
      if (config.componentUrl === '') {
        ctx.body = getResponseBody({
          fileData: { message: '打包成功' },
          packurl: `http://127.0.0.1:7001/public/commonfiles/compackages_${base64.encode(
            serverIp
          )}_${datestr}.tgz`
        })
      } else {
        ctx.body = getResponseBody({
          packurl: `${
            config.componentUrl
          }/public/commonfiles/compackages_${base64.encode(
            serverIp
          )}_${datestr}.tgz`
        })
      }
    } catch (error) {
      await ctx.service.packcom.update({ id: 1 }, { ispack: false })
      ctx.body = getResponseBody(error, false, '打包失败', 400)
    }
  }
}

module.exports = PackcomController
