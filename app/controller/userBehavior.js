const Controller = require('egg').Controller
const { getResponseBody, getDayAgo } = require('../extend/utils')
class UserBehaviorController extends Controller {
  // 获取用户操作分页
  async getOpLog() {
    const { ctx } = this
    const {
      page_no,
      page_size,
      keyword,
      user_id,
      group_id,
      optype,
      status,
      date_type = 3,
      s_time,
      e_time
    } = ctx.request.body

    const filter = {}

    if (user_id) filter.user_id = user_id
    if (group_id) filter.group_id = group_id
    if (keyword) filter.details = new RegExp(keyword, 'i')
    if (optype) filter.operateType = optype
    if (status) filter.status = status

    switch (Number(date_type)) {
      case 1: // 今天
        filter.c_time = {
          $gte: getDayAgo(1),
          $lte: new Date()
        }
        break;
      case 2: // 昨天
        filter.c_time = {
          $gte: getDayAgo(2),
          $lte: new Date()
        }
        break;
      case 3: // 7天前
        filter.c_time = {
          $gte: getDayAgo(7),
          $lte: new Date()
        }
        break;
      case 4: // 30天前
        filter.c_time = {
          $gte: getDayAgo(30),
          $lte: new Date()
        }
        break;
      case 5: // 半年前
        filter.c_time = {
          $gte: getDayAgo(180),
          $lte: new Date()
        }
        break;
      case 6: // 一年前
      filter.c_time = {
        $gte: getDayAgo(360),
        $lte: new Date()
      }
        break;
      default:
        filter.c_time = {
          $gte: s_time,
          $lte: e_time
        }
        break;
    }

    let u_behaviors = await ctx.service.userBehavior.paginfind(
      filter, 
      {},
      page_no,
      page_size,
    )
    
    const totalCount = await ctx.service.userBehavior.count(filter)

    ctx.body = getResponseBody({ data: u_behaviors, total: totalCount })
  }
}

module.exports = UserBehaviorController
