# seatom-server

伏羲大屏服务器

### 本地开发环境

用于本地开发
需要预先安装 redis, mongodb 数据库。
前端端口号：9999

```bash
$ npm i
$ npm run dev
$ open http://127.0.0.1:7001/
```

### 测试环境

用于 测试环境
服务器地址/home/<USER>/front/www/seatom/project-prev/seatom-server
容器名字：seatom-server-test

```bash
# 启动
$ npm i
$ npm run start-test
# 更新
$ npm run stop-test
$ npm run start-test
# 停止
$ npm run stop-test
```

### 演示环境

用于演示环境
服务器地址/home/<USER>/front/www/seatom/project-prev/seatom-server
容器名字：seatom-server-prev

```bash
# 启动
$ npm i
$ npm run start-prev
# 更新
$ npm run stop-prev
$ npm run start-prev
# 停止
$ npm run stop-prev
```

### npm scripts

- Use `npm run lint` to check code style.
- Use `npm test` to run unit test.
- Use `npm run autod` to auto detect dependencies upgrade, see [autod](https://www.npmjs.com/package/autod) for more detail.

[egg]: https://eggjs.org

## 静态资源路径

静态资源保存在项目同级目录的 `seatom-resources` 文件夹，项目初始化时会检查该文件夹是否存在，如果不存在，则自动创建一个，并将 `public/resources` 文件夹下的内容拷贝进去。

```js
;(resourceDirName = 'seatom-resources'),
  (userConfig.resourcePath = path.join(
    appInfo.baseDir,
    '../' + userConfig.resourceDirName
  ))
```

## 分支管理

- master：每次发完版合并到 master，只有管理员有权限。
- dev：开发人员联调时，统一合并到此分支。前线部署也是这个分支
- seatom_x.x.x：发布版本时单独拉的分支。
- 其他：各个开发者自己拉各自分支。

注：开发者只能往自己分支 push 代码，向 master 或版本分支 push 代码时，要提 PR 给管理员。
