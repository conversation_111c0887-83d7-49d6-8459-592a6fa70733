const path = require('path')
const commonFunction = require('./config.common')
const pageURL = 'https://127.0.0.1' //外网443端口
const HttpPageURL = 'http://127.0.0.1' //外网80端口
module.exports = appInfo => {
  const commonConfig = commonFunction(appInfo)
  commonConfig['ucenter'] = true
  commonConfig['security']['domainWhiteList'].push(pageURL)
  commonConfig['security']['domainWhiteList'].push(HttpPageURL)
  commonConfig['pageURL'] = pageURL
  commonConfig['mongoose'] = {
    client: {
      url: 'mongodb://127.0.0.1:27777/seatom-prod', //内网数据库地址
      options: {
        useUnifiedTopology: true,
        useCreateIndex: true,
        auth: {
          authSource: 'admin'
        },
        user: 'root',
        pass: 'haizhimongo'
      }
    }
  }
  commonConfig['ai'] = {
    // 语音服务的接口地址
    speech2ie: 'http://127.0.0.1:18213//speech/',
    text2ie: 'http://127.0.0.1:18213/text2ie'
  }
  commonConfig['voiceConfig'] = {
    // 语音服务配置
    show: false, // 是否开启语音服务
    voiceWeak: false, // 是否开启语音唤醒
    weakWord: '休息中，通过 "你好伏羲" 呼出我哦' // 语音唤醒词
  }
  commonConfig['serverIp'] = '127.0.0.1' // 外网ip
  commonConfig['intranetIp'] = '127.0.0.1' // 内网ip
  commonConfig['errorHandler']['data']['authorizationTime'] = 1679902080689
  commonConfig['webServerIp'] = 'http://127.0.0.1' // 内网前端地址
  commonConfig['cluster'] = {
    listen: {
      path: '',
      port: 7001,
      hostname: '0.0.0.0'
    }
  }
  commonConfig['redis'] = {
    clients: {
      db0: {
        port: 6679, // Redis port
        host: '127.0.0.1', // Redis host
        password: 'h1iz6i2dp4redis',
        db: 0
      },
      db1: {
        port: 6679, // Redis port
        host: '127.0.0.1', // Redis host
        password: 'h1iz6i2dp4redis',
        db: 1
      }
    }
  }
  commonConfig['openInfo'] = {
    domain: 'haizhi',
    ip: 'http://***************:8908',
    api: '/workbench/s/fuxi/checkApi'
  }
  commonConfig['dmcAddress'] = {
    address: 'http://dmc.haizhi.com', // dmc地址
    tassadar: 'http://dmc.haizhi.com:19988', // dmc tassadar地址对接dmc数据表
    version: '2.0.6',
    cipher: 'base64', // dmc登录时传输的密码加密方式，取值none, md5, smssl、base64，分别为不加密、md5、国密
    publicKey:
      '046d02ba2a33cd7b061ab63d2effdf7a672f021c491eaf7c71db5a9899342251df1a35290bcd444779d7923c56bb957932b5b5cdbf880fff5e28b0df794934add8', // 公钥，加密方式为国密时必须要填
    cipherMode: 1 // 1 - C1C3C2，0 - C1C2C3，默认为1，加密方式为国密时必须要填
  }
  commonConfig['componentUrl'] = 'https://127.0.0.1' // 外网组件拼的地址
  commonConfig['logger'] = {
    dir: path.join(appInfo.baseDir, '../seatom-server-logs') // 大屏服务端日志路径
  }
  commonConfig['io'] = {
    init: {
      path: '/api/socket'
    },
    generateId: req => {
      return req._query.uid
    },
    namespace: {
      '/chat': {
        connectionMiddleware: ['auth'],
        packetMiddleware: []
      },
      '/editor': {
        connectionMiddleware: ['editorConnect'],
        packetMiddleware: []
      },
      '/linkage': {
        connectionMiddleware: ['linkage'],
        packetMiddleware: []
      }
    },
    redis: {
      ...commonConfig['redis'].clients['db1'],
      auth_pass: commonConfig['redis'].clients['db1'].password
    }
  }
  commonConfig['opLog']['enable'] = false // 控制用户行为记录中间件开关
  return {
    ...commonConfig
  }
}
