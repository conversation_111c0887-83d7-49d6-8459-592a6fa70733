/*
 * @Description: EGG_SERVER_ENV=prev 演示环境，用于演示
 * @Date: 2022-12-12 16:12:21
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */

'use strict'
const path = require('path')
const commonFunction = require('./config.common')

const pageURL = 'https://fuxi.haizhi.com'
const HttpPageURL = 'http://fuxi.haizhi.com'
/**
 * @param {Egg.EggAppInfo} appInfo app info
 */
module.exports = appInfo => {
  const commonConfig = commonFunction(appInfo)
  commonConfig['ucenter'] = true
  commonConfig['security']['domainWhiteList'].push(pageURL)
  commonConfig['security']['domainWhiteList'].push(HttpPageURL)
  commonConfig['pageURL'] = pageURL
  commonConfig['mongoose'] = {
    client: {
      url: 'mongodb://************:29018/seatom-prev',
      options: {
        // auth: {
        //   authSource: 'admin'
        // },
        // user: 'root1',
        // pass: 'h1izhi2dp4root',
        useUnifiedTopology: true,
        useCreateIndex: true
      }
    }
  }

  commonConfig['redis'] = {
    // client: {
    //   port: 1679, // Redis port
    //   host: '127.0.0.1', // Redis host
    //   password: 'haizhi1234',
    //   db: 0
    // }
    clients: {
      db0: {
        port: 1679, // Redis port
        host: '************', // Redis host
        password: 'haizhi1234',
        db: 0
      },
      db1: {
        port: 1679, // Redis port
        host: '************', // Redis host
        password: 'haizhi1234',
        db: 1
      }
    }
  }
  commonConfig['ai'] = {
    // 语音服务的接口地址
    speech2ie: 'http://**************:7011/speech/asr/',
    text2ie: 'http://127.0.0.1:18213/text2ie'
  }
  commonConfig['voiceConfig'] = {
    // 语音服务配置
    show: true,
    voiceWeak: true, // 是否开启语音唤醒
    weakWord: '休息中，通过 "你好伏羲" 呼出我哦' // 语音唤醒词
  }
  commonConfig['serverIp'] = '**************'
  commonConfig['webServerIp'] = pageURL
  commonConfig['intranetIp'] = '127.0.0.1'
  commonConfig['renderType'] = 'static'
  commonConfig['cluster'] = {
    listen: {
      path: '',
      port: 7001,
      hostname: '0.0.0.0'
    }
  }
  commonConfig['dmcAddress'] = {
    address: 'https://dmc.haizhi.com', // 对接dmc地址，登录获取token基于该配置
    tassadar: 'http://************:19988', //tassadar地址
    version: '2.2.7',
    cipher: 'base64', // dmc登录时传输的密码加密方式，取值none, md5, smssl、base64，分别为不加密、md5、国密
    publicKey:
      '046d02ba2a33cd7b061ab63d2effdf7a672f021c491eaf7c71db5a9899342251df1a35290bcd444779d7923c56bb957932b5b5cdbf880fff5e28b0df794934add8', // 公钥，加密方式为国密时必须要填
    cipherMode: 1 // 1 - C1C3C2，0 - C1C2C3，默认为1，加密方式为国密时必须要填
  }
  commonConfig['componentUrl'] = 'https://fuxi.haizhi.com'
  commonConfig['logger'] = {
    dir: path.join(appInfo.baseDir, '../seatom-server-logs')
  }

  commonConfig['io'] = {
    init: {
      path: '/api/socket'
    },
    generateId: req => {
      return req._query.uid
    },
    namespace: {
      '/chat': {
        connectionMiddleware: ['auth'],
        packetMiddleware: []
      },
      '/editor': {
        connectionMiddleware: ['editorConnect'],
        packetMiddleware: []
      },
      '/linkage': {
        connectionMiddleware: ['linkage'],
        packetMiddleware: []
      }
    },
    redis: {
      ...commonConfig['redis'].clients['db1'],
      auth_pass: commonConfig['redis'].clients['db1'].password
    }
  }
  commonConfig['opLog']['enable'] = false // 控制用户行为记录中间件开关

  return {
    ...commonConfig
  }
}
