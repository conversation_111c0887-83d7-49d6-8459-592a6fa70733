const path = require('path')
const commonFunction = require('./config.common')
const pageURL = 'https://*************' //外网443端口
const HttpPageURL = 'http://*************' //外网80端口

module.exports = appInfo => {
  const commonConfig = commonFunction(appInfo)
  commonConfig['ucenter'] = false   // 是否对接用户中心自动跳转
  commonConfig['security']['domainWhiteList'].push(pageURL)
  commonConfig['security']['domainWhiteList'].push(HttpPageURL)
  commonConfig['pageURL'] = pageURL
  commonConfig['mongoose'] = {
    client: {
      url: 'mongodb://127.0.0.1:27777/seatom-test1', //内网数据库地址
      options: {
        useUnifiedTopology: true,
        useCreateIndex: true,
        auth: {
          authSource: 'admin'
        },
        user: 'root',
        pass: 'ha<PERSON>him<PERSON>o'
      }
    }
  }
  commonConfig['ai'] = {
    // 语音服务的接口地址
    speech2ie: 'http://127.0.0.1:18213//speech/',
    text2ie: 'http://127.0.0.1:18213/text2ie'
  }
  commonConfig['voiceConfig'] = {
    // 语音服务配置
    show: false, // 是否开启语音服务
    voiceWeak: false, // 是否开启语音唤醒
    weakWord: '休息中，通过 "你好伏羲" 呼出我哦' // 语音唤醒词
  }
  commonConfig['previewConfig'] = {
    // 发布页面设置
    blankShow: true, // 是否开启锁屏配置
    blankTime: 30 * 60 * 1000 // 锁屏时间
  }
  commonConfig['serverIp'] = '*************' // 外网ip
  commonConfig['intranetIp'] = '127.0.0.1' // 内网ip
  commonConfig['errorHandler']['data']['authorizationTime'] = 1679902080689
  commonConfig['webServerIp'] = 'http://127.0.0.1:8888' // 内网前端地址
  commonConfig['cluster'] = {
    listen: {
      path: '',
      port: 7001,
      hostname: '0.0.0.0'
    }
  }
  commonConfig['redis'] = {
    clients: {
      db0: {
        port: 6479, // Redis port
        host: '127.0.0.1', // Redis host
        password: 'h1iz6i2dp4redis',
        db: 0
      },
      db1: {
        port: 6479, // Redis port
        host: '127.0.0.1', // Redis host
        password: 'h1iz6i2dp4redis',
        db: 1
      }
    }
  }
  // 对接第三方的权限接口配置
  commonConfig['openInfo'] = {
    domain: 'haizhi',
    ip: 'http://***************:8908',
    api: '/workbench/s/fuxi/checkApi'
  }
  // 对接dmc配置
  commonConfig['dmcAddress'] = {
    address: 'http://*************', // dmc内网地址
    externalAddress: 'http://*************', // dmc外网地址
    tassadar: 'http://*************:19988', // dmc tassadar地址对接dmc数据表
    version: '2.2.5',
    cipher: 'base64', // dmc登录时传输的密码加密方式，取值none, md5, smssl、base64，分别为不加密、md5、国密
    publicKey:
      '046d02ba2a33cd7b061ab63d2effdf7a672f021c491eaf7c71db5a9899342251df1a35290bcd444779d7923c56bb957932b5b5cdbf880fff5e28b0df794934add8', // 公钥，加密方式为国密时必须要填
    cipherMode: 1 // 1 - C1C3C2，0 - C1C2C3，默认为1，加密方式为国密时必须要填
  }
  commonConfig['bdpAddress'] = 'http://guangfa.dev.stargraph.cn/bdp/api'
  commonConfig['componentUrl'] = 'https://*************' // 外网组件拼的地址
  commonConfig['logger'] = {
    dir: path.join(appInfo.baseDir, '../seatom-server-logs') // 大屏服务端日志路径
  }
  commonConfig['io'] = {
    init: {
      path: '/api/socket'
    },
    generateId: req => {
      return req._query.uid
    },
    namespace: {
      // 终端控制命名空间
      '/chat': {
        connectionMiddleware: ['auth'],
        packetMiddleware: []
      },
      // 协同编辑命名空间
      '/editor': {
        connectionMiddleware: ['editorConnect'],
        packetMiddleware: []
      },
      // 大屏联动命名空间
      '/linkage': {
        connectionMiddleware: ['linkage'],
        packetMiddleware: []
      }
    },
    redis: {
      ...commonConfig['redis'].clients['db1'],
      auth_pass: commonConfig['redis'].clients['db1'].password
    }
  }
  commonConfig['opLog']['enable'] = false // 控制用户行为记录中间件开关
  return {
    ...commonConfig
  }
}