/*
 * @Description: EGG_SERVER_ENV=test 测试环境，用于QA测试
 * @Date: 2022-12-12 16:12:21
 * @Author: chenxingyu
 * @LastEditors: chenxingyu
 */

'use strict'
const path = require('path')
const commonFunction = require('./config.common')

const pageURL = 'http://**************:6670'

/**
 * @param {Egg.EggAppInfo} appInfo app info
 */
module.exports = appInfo => {
  const commonConfig = commonFunction(appInfo)
  commonConfig['ucenter'] = false
  commonConfig['security']['domainWhiteList'].push(pageURL)
  commonConfig['pageURL'] = pageURL
  commonConfig['mongoose'] = {
    client: {
      url: 'mongodb://************:29018/seatom-test',
      options: {
        // auth: {
        //   authSource: 'admin'
        // },
        // user: 'root',
        // pass: 'haizhim<PERSON>o',
        useUnifiedTopology: true,
        useCreateIndex: true
      }
    }
  }
  commonConfig['redis'] = {
    // client: {
    //   port: 1679, // Redis port
    //   host: '************', // Redis host
    //   password: 'haizhi1234',
    //   db: 0
    // }
    clients: {
      db0: {
        port: 1679, // Redis port
        host: '************', // Redis host
        password: 'haizhi1234',
        db: 0
      },
      db1: {
        port: 1679, // Redis port
        host: '************', // Redis host
        password: 'haizhi1234',
        db: 1
      }
    }
  }
  commonConfig['ai'] = {
    // 语音服务的接口地址
    speech2ie: 'http://127.0.0.1:18213/speech2ie',
    text2ie: 'http://127.0.0.1:18213/text2ie'
  }
  commonConfig['voiceConfig'] = {
    // 语音服务配置
    show: false,
    voiceWeak: true, // 是否开启语音唤醒
    weakWord: '休息中，通过 "你好伏羲" 呼出我哦' // 语音唤醒词
  }
  commonConfig['serverIp'] = '**************'
  commonConfig['intranetIp'] = '127.0.0.1'
  commonConfig['webServerIp'] = pageURL
  commonConfig['cluster'] = {
    listen: {
      path: '',
      port: 5001,
      hostname: '0.0.0.0'
    }
  }
  commonConfig['logger'] = {
    dir: path.join(appInfo.baseDir, '../seatom-server-logs')
  }
  commonConfig['componentUrl'] = ''

  commonConfig['io'] = {
    init: {
      path: '/api/socket'
    },
    generateId: req => {
      return req._query.uid
    },
    namespace: {
      '/chat': {
        connectionMiddleware: ['auth'],
        packetMiddleware: []
      },
      '/editor': {
        connectionMiddleware: ['editorConnect'],
        packetMiddleware: []
      },
      '/linkage': {
        connectionMiddleware: ['linkage'],
        packetMiddleware: []
      }
    },
    redis: {
      ...commonConfig['redis'].clients['db1'],
      auth_pass: commonConfig['redis'].clients['db1'].password
    }
  }

  return {
    ...commonConfig
  }
}
